# Implementação dos Componentes dos Links do Footer

Este documento contém instruções detalhadas para implementar os componentes das páginas referenciadas nos links do footer da aplicação CurrencyWise API.

## Visão Geral

O footer da aplicação contém links para diversas páginas que precisam ser implementadas. Atualmente, muitos desses links estão configurados como `url: '#'`, indicando que as páginas ainda não foram criadas.

### Estrutura Atual do Footer

O footer está organizado em três seções:

1. **Links Rápidos**
   - Documentação (já implementado)
   - Preços (já implementado)
   - API Status (não implementado)

2. **Recursos**
   - Blog (não implementado)
   - Tutoriais (não implementado)
   - FAQ (não implementado)

3. **Contato**
   - Suporte (não implementado)
   - Vendas (não implementado)
   - Sobre Nós (não implementado)

## Componentes a Serem Implementados

### 1. API Status

#### 1.1. Descrição
Página que mostra o status atual da API, incluindo tempo de atividade, latência e incidentes recentes.

#### 1.2. Especificações
- **Caminho**: `src/app/features/status/components/status.component.ts`
- **Rota**: `/status`
- **Acesso**: Público
- **Elementos UI**:
  - Indicador visual do status atual (verde para operacional, amarelo para degradado, vermelho para problemas)
  - Gráfico de tempo de resposta das últimas 24 horas
  - Lista de incidentes recentes
  - Histórico de tempo de atividade mensal

### 2. Blog

#### 2.1. Descrição
Página que lista artigos do blog sobre atualizações da API, casos de uso e melhores práticas.

#### 2.2. Especificações
- **Caminho**: `src/app/features/blog/components/blog.component.ts`
- **Rota**: `/blog`
- **Acesso**: Público
- **Elementos UI**:
  - Lista de artigos com imagem, título, resumo e data
  - Filtros por categoria
  - Paginação
  - Página de detalhe do artigo (rota: `/blog/:id`)

### 3. Tutoriais

#### 3.1. Descrição
Página com tutoriais passo a passo sobre como usar a API em diferentes cenários.

#### 3.2. Especificações
- **Caminho**: `src/app/features/tutorials/components/tutorials.component.ts`
- **Rota**: `/tutorials`
- **Acesso**: Público
- **Elementos UI**:
  - Lista de tutoriais organizados por nível (iniciante, intermediário, avançado)
  - Filtros por linguagem de programação
  - Página de detalhe do tutorial (rota: `/tutorials/:id`)
  - Exemplos de código com syntax highlighting

### 4. FAQ

#### 4.1. Descrição
Página com perguntas frequentes sobre a API, preços, suporte, etc.

#### 4.2. Especificações
- **Caminho**: `src/app/features/faq/components/faq.component.ts`
- **Rota**: `/faq`
- **Acesso**: Público
- **Elementos UI**:
  - Lista de perguntas organizadas por categoria
  - Accordion para expandir/colapsar respostas
  - Campo de busca para filtrar perguntas
  - Seção "Não encontrou o que procurava?" com link para suporte

### 5. Suporte

#### 5.1. Descrição
Página com informações de suporte e formulário de contato para questões técnicas.

#### 5.2. Especificações
- **Caminho**: `src/app/features/support/components/support.component.ts`
- **Rota**: `/support`
- **Acesso**: Público
- **Elementos UI**:
  - Formulário de contato (nome, email, assunto, mensagem, prioridade)
  - FAQ específico para suporte
  - Informações de SLA por plano
  - Opções de contato alternativas (email, chat)

### 6. Vendas

#### 6.1. Descrição
Página com informações para clientes corporativos e formulário de contato para vendas.

#### 6.2. Especificações
- **Caminho**: `src/app/features/sales/components/sales.component.ts`
- **Rota**: `/sales`
- **Acesso**: Público
- **Elementos UI**:
  - Informações sobre planos corporativos
  - Casos de uso para empresas
  - Formulário de contato para vendas
  - Seção de perguntas frequentes sobre licenciamento corporativo

### 7. Sobre Nós

#### 7.1. Descrição
Página com informações sobre a empresa, equipe e missão.

#### 7.2. Especificações
- **Caminho**: `src/app/features/about/components/about.component.ts`
- **Rota**: `/about`
- **Acesso**: Público
- **Elementos UI**:
  - História da empresa
  - Missão e valores
  - Equipe (opcional)
  - Informações de contato corporativo

## Instruções de Implementação

### Passo 1: Criar as Features

Para cada componente, crie a estrutura de diretórios necessária:

```bash
mkdir -p src/app/features/status/components
mkdir -p src/app/features/blog/components
mkdir -p src/app/features/tutorials/components
mkdir -p src/app/features/faq/components
mkdir -p src/app/features/support/components
mkdir -p src/app/features/sales/components
mkdir -p src/app/features/about/components
```

### Passo 2: Implementar os Componentes

Para cada componente, crie os arquivos necessários seguindo o padrão Angular:

1. Arquivo TypeScript (.ts)
2. Arquivo HTML (.html)
3. Arquivo CSS (.css)
4. Arquivo de índice (index.ts)

### Passo 3: Atualizar as Rotas

Adicione as novas rotas ao arquivo de rotas principal:

```typescript
// src/app/layout/main-layout/main-layout.routes.ts
import { Routes } from '@angular/router';
import { MainLayoutComponent } from './main-layout.component';

export const MAIN_ROUTES: Routes = [
  {
    path: '',
    component: MainLayoutComponent,
    children: [
      // Rotas existentes...
      
      // Novas rotas
      {
        path: 'status',
        loadComponent: () => import('../../features/status').then(m => m.StatusComponent),
        title: 'API Status - CurrencyWise'
      },
      {
        path: 'blog',
        loadComponent: () => import('../../features/blog').then(m => m.BlogComponent),
        title: 'Blog - CurrencyWise'
      },
      {
        path: 'tutorials',
        loadComponent: () => import('../../features/tutorials').then(m => m.TutorialsComponent),
        title: 'Tutoriais - CurrencyWise'
      },
      {
        path: 'faq',
        loadComponent: () => import('../../features/faq').then(m => m.FaqComponent),
        title: 'FAQ - CurrencyWise'
      },
      {
        path: 'support',
        loadComponent: () => import('../../features/support').then(m => m.SupportComponent),
        title: 'Suporte - CurrencyWise'
      },
      {
        path: 'sales',
        loadComponent: () => import('../../features/sales').then(m => m.SalesComponent),
        title: 'Vendas - CurrencyWise'
      },
      {
        path: 'about',
        loadComponent: () => import('../../features/about').then(m => m.AboutComponent),
        title: 'Sobre Nós - CurrencyWise'
      }
    ]
  }
];
```

### Passo 4: Atualizar os Links do Footer

Atualize os links no componente do footer para apontar para as novas rotas:

```typescript
// src/app/shared/components/footer/footer.component.ts
footerSections: FooterSection[] = [
  {
    title: 'Links Rápidos',
    links: [
      { label: 'Documentação', url: '/documentation', isRouterLink: true },
      { label: 'Preços', url: '/subscriptions', isRouterLink: true },
      { label: 'API Status', url: '/status', isRouterLink: true },
    ],
  },
  {
    title: 'Recursos',
    links: [
      { label: 'Blog', url: '/blog', isRouterLink: true },
      { label: 'Tutoriais', url: '/tutorials', isRouterLink: true },
      { label: 'FAQ', url: '/faq', isRouterLink: true },
    ],
  },
  {
    title: 'Contato',
    links: [
      { label: 'Suporte', url: '/support', isRouterLink: true },
      { label: 'Vendas', url: '/sales', isRouterLink: true },
      { label: 'Sobre Nós', url: '/about', isRouterLink: true },
    ],
  },
];
```

## Diretrizes de Design

1. **Consistência**: Mantenha o design consistente com o restante da aplicação
2. **Responsividade**: Garanta que todos os componentes sejam responsivos
3. **Acessibilidade**: Siga as melhores práticas de acessibilidade
4. **Dados**: Use dados fictícios para demonstração

## Tecnologias a Serem Utilizadas

1. **Angular**: Use a sintaxe moderna (@for, @if)
2. **Tailwind CSS**: Para estilização
3. **Lucide Angular**: Para ícones
4. **CommonModule**: Para pipes e diretivas comuns

## Lembre-se de:

1. Seguir a estrutura modular por features
2. Separar HTML e TypeScript em arquivos diferentes
3. Usar Tailwind CSS para estilização
4. Implementar dados fake para demonstração
5. Preparar os componentes para futura integração com serviços reais
