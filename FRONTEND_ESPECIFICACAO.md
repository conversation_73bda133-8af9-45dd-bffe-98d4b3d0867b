# Especificação do Frontend para CurrencyWise API

## 📋 Visão Geral

Este documento detalha a especificação completa para o desenvolvimento do frontend da CurrencyWise API, incluindo estrutura de projeto, painéis, planos de assinatura, funcionalidades e implementação de testes gratuitos.

## 🏗️ Arquitetura e Tecnologias

### Tecnologias Recomendadas

- **Framework**: Angular 17+
- **UI/UX**: Angular Material + Tailwind CSS
- **Gráficos**: ngx-charts ou Chart.js
- **Gerenciamento de Estado**: NgRx (para funcionalidades complexas)
- **Internacionalização**: ngx-translate (suporte para português e inglês)
- **Testes**: Jest + Cypress

### Estrutura do Projeto

```
currencywise-portal/
├── src/
│   ├── app/
│   │   ├── core/                    # Serviços e utilitários centrais
│   │   │   ├── auth/                # Autenticação e autorização
│   │   │   ├── http/                # Interceptores HTTP
│   │   │   ├── services/            # Serviços compartilhados
│   │   │   └── guards/              # Guards de rota
│   │   │
│   │   ├── shared/                  # Componentes e diretivas compartilhadas
│   │   │   ├── components/          # Componentes reutilizáveis
│   │   │   ├── directives/          # Diretivas personalizadas
│   │   │   └── pipes/               # Pipes personalizados
│   │   │
│   │   ├── features/                # Módulos de funcionalidades
│   │   │   ├── home/                # Página inicial
│   │   │   ├── auth/                # Login e registro
│   │   │   ├── dashboard/           # Dashboard do usuário
│   │   │   ├── api-keys/            # Gerenciamento de API keys
│   │   │   ├── subscriptions/       # Gerenciamento de assinaturas
│   │   │   ├── documentation/       # Documentação interativa
│   │   │   ├── playground/          # Área de testes da API
│   │   │   └── admin/               # Painel administrativo
│   │   │
│   │   ├── layouts/                 # Layouts da aplicação
│   │   │   ├── main-layout/         # Layout principal
│   │   │   ├── auth-layout/         # Layout para autenticação
│   │   │   └── admin-layout/        # Layout para administração
│   │   │
│   │   └── app.module.ts
│   │
│   ├── assets/                      # Recursos estáticos
│   │   ├── images/
│   │   ├── icons/
│   │   └── i18n/                    # Arquivos de tradução
│   │
│   ├── environments/                # Configurações de ambiente
│   └── styles/                      # Estilos globais
│
└── package.json
```

## 🖥️ Painéis e Páginas

### 1. Página Inicial (Pública)

- **Objetivo**: Apresentar a API e atrair novos usuários
- **Componentes**:
  - ✅ Header com navegação e botões de login/registro (`HeaderComponent`)
  - ✅ Banner principal com call-to-action (`HeroBannerComponent`)
  - ✅ Seção de recursos e benefícios (`FeaturesComponent`)
  - ✅ Comparativo de planos (`PricingComponent`)
  - ✅ Seção de estatísticas (número de requisições processadas, etc.) (`StatisticsComponent`)
  - ✅ Seção de depoimentos/casos de uso (`TestimonialsComponent`)
  - ✅ FAQ (`FaqComponent`)
  - ✅ Formulário de contato (`ContactFormComponent`)
  - ✅ Footer com links úteis (`FooterComponent`)

### 2. Autenticação (Pública)

- **Páginas**:
  - ✅ Login (`LoginComponent`)
  - ✅ Registro (`RegisterComponent`)
  - ✅ Recuperação de senha (`ForgotPasswordComponent`)
  - ✅ Verificação de email (`VerifyEmailComponent`)
  - ✅ Termos de serviço e política de privacidade (`TermsComponent`)

### 3. Dashboard do Usuário (Autenticada)

- **Objetivo**: Visão geral do uso da API pelo usuário
- **Componentes**:
  - ✅ Resumo do plano atual (`DashboardComponent`)
  - ✅ Gráfico de uso diário/mensal (`UsageChartComponent`)
  - ✅ Estatísticas de uso (requisições, tempo de resposta) (`UsageSummaryComponent`)
  - ✅ Alertas e notificações (`NotificationComponent`)
  - ✅ Acesso rápido às API keys (`QuickAccessComponent`)
  - ✅ Links para documentação e playground (`QuickAccessComponent`)

### 4. Gerenciamento de API Keys (Autenticada)

- **Objetivo**: Criar e gerenciar chaves de API
- **Componentes**:
  - ✅ Lista de API keys ativas (`ApiKeysComponent`)
  - ✅ Formulário para criar nova API key (`ApiKeysComponent`)
  - ✅ Opções para regenerar ou revogar chaves (`ApiKeysComponent`)
  - ✅ Histórico de uso por chave (`ApiKeyUsageHistoryComponent`)
  - ✅ Configurações de segurança (IPs permitidos, etc.) (`ApiKeySecuritySettingsComponent`)

### 5. Gerenciamento de Assinaturas (Autenticada)

- **Objetivo**: Gerenciar plano de assinatura
- **Componentes**:
  - ✅ Detalhes do plano atual (`SubscriptionsComponent`, `SubscriptionStatusComponent`)
  - ✅ Comparativo de planos (`SubscriptionPlansComponent`)
  - ✅ Histórico de pagamentos (`PaymentHistoryComponent`)
  - ✅ Formulário de upgrade/downgrade (`SubscriptionsComponent`)
  - ✅ Integração com gateway de pagamento (`PaymentGatewayComponent`)
  - ✅ Opções para cancelamento (`SubscriptionsComponent`)

### 6. Documentação Interativa (Autenticada)

- **Objetivo**: Documentação completa da API
- **Componentes**:
  - ✅ Documentação de endpoints (`DocumentationComponent`)
  - ✅ Exemplos de código em múltiplas linguagens (`DocumentationComponent`)
  - ✅ Console interativo para testes (`InteractiveConsoleComponent`)
  - ✅ Guias de início rápido (`QuickStartGuidesComponent`)
  - ✅ Tutoriais passo a passo (`TutorialsComponent`)
  - ✅ FAQ e troubleshooting (`DocsFaqComponent`)

### 7. Playground (Autenticada)

- **Objetivo**: Testar a API em tempo real
- **Componentes**:
  - ✅ Interface para construir requisições (`PlaygroundComponent`)
  - ✅ Visualização de respostas (`PlaygroundComponent`)
  - ✅ Histórico de requisições (`RequestHistoryComponent`)
  - ✅ Opções para salvar requisições (`SavedRequestsComponent`)
  - ✅ Exemplos pré-configurados (`RequestExamplesComponent`)

### 8. Painel Administrativo (Admin)

- **Objetivo**: Gerenciar usuários, planos e monitorar o sistema
- **Componentes**:
  - ✅ Dashboard com métricas gerais (`AdminDashboardComponent`)
  - ✅ Gerenciamento de usuários (`UsersComponent`)
  - ✅ Gerenciamento de planos (`PlansComponent`)
  - ✅ Monitoramento de uso (`UsageMonitoringComponent`)
  - ✅ Logs e auditoria (`LogsComponent`)
  - ✅ Configurações do sistema (`SystemSettingsComponent`)

## 💰 Planos de Assinatura

### Implementação no Frontend

Cada plano deve ser claramente apresentado com:

1. **Comparativo Visual**:

   - Tabela comparativa de recursos
   - Destaque para o plano recomendado
   - Indicadores visuais de limites e recursos

2. **Processo de Assinatura**:

   - Fluxo simplificado de checkout
   - Integração com gateway de pagamento
   - Confirmação e recibo

3. **Gerenciamento de Plano**:
   - Visualização clara de limites e uso atual
   - Alertas quando se aproximar dos limites
   - Processo simples para upgrade

### Estrutura de Planos

#### Plano Gratuito

- **Nome**: Free
- **Preço**: R$ 0
- **Limites**:
  - 1.000 requisições por dia
  - 30.000 requisições por mês
  - Acesso a 10 moedas principais
  - Dados históricos de 7 dias
  - Sem acesso a WebSockets
  - Sem recursos avançados

#### Plano Básico

- **Nome**: Basic
- **Preço**: R$ 49/mês
- **Limites**:
  - 10.000 requisições por dia
  - 300.000 requisições por mês
  - Acesso a 50 moedas
  - Dados históricos de 30 dias
  - WebSockets com atualizações a cada 5 minutos
  - Suporte por email

#### Plano Profissional

- **Nome**: Pro
- **Preço**: R$ 149/mês
- **Limites**:
  - 50.000 requisições por dia
  - 1.500.000 requisições por mês
  - Acesso a todas as moedas
  - Dados históricos de 1 ano
  - WebSockets em tempo real
  - Suporte prioritário
  - Recursos avançados

#### Plano Empresarial

- **Nome**: Enterprise
- **Preço**: R$ 499/mês
- **Limites**:
  - 500.000 requisições por dia
  - 15.000.000 requisições por mês
  - Acesso a todas as moedas
  - Dados históricos completos
  - WebSockets em tempo real
  - API dedicada com SLA
  - Suporte 24/7
  - Todos os recursos avançados
  - Personalização de endpoints

## 🧪 Implementação de Testes Gratuitos

### Estratégia de Testes Gratuitos

1. **Playground Público**:

   - Versão limitada do playground acessível sem registro
   - Limite de 5-10 requisições por IP
   - Resultados reais, mas com delay de 5 segundos

2. **Plano Free com Registro**:

   - Acesso completo ao plano gratuito após registro
   - API key gerada automaticamente
   - Limites claramente comunicados

3. **Trial de Planos Pagos**:
   - Trial de 7 dias do plano Pro
   - Sem necessidade de cartão de crédito
   - Downgrade automático para Free após o período

### Implementação no Frontend

1. **Página de Playground Público**:

   - Interface simplificada
   - Contador de requisições restantes
   - CTA para registro após limite atingido

2. **Processo de Onboarding**:

   - Tutorial interativo após registro
   - Guia passo a passo para primeira API key
   - Exemplos de código para integração

3. **Notificações de Limite**:
   - Alertas visuais ao se aproximar dos limites
   - Emails informativos sobre uso
   - Sugestões de upgrade baseadas no padrão de uso

## 🎨 Design e Experiência do Usuário

### Princípios de Design

1. **Simplicidade**: Interface limpa e intuitiva
2. **Consistência**: Padrões visuais e de interação consistentes
3. **Feedback**: Respostas claras para ações do usuário
4. **Acessibilidade**: Conformidade com WCAG 2.1 AA
5. **Responsividade**: Adaptação a diferentes dispositivos

### Elementos Visuais

1. **Paleta de Cores**:

   - Primária: Tons de azul (#1E88E5, #1565C0)
   - Secundária: Verde (#4CAF50) para sucesso, Vermelho (#F44336) para erro
   - Neutros: Cinzas (#F5F5F5, #E0E0E0, #9E9E9E, #424242)

2. **Tipografia**:

   - Família principal: Roboto
   - Títulos: Montserrat
   - Código: Fira Code

3. **Componentes**:
   - Cards para agrupamento de informações
   - Gráficos interativos para visualização de dados
   - Formulários com validação em tempo real
   - Tabelas com ordenação e filtragem

## 🚀 Implementação e Desenvolvimento

### Priorização de Desenvolvimento

1. **Fase 1: MVP (1-2 meses)**

   - Página inicial
   - Autenticação básica
   - Dashboard simples
   - Gerenciamento de API keys
   - Documentação estática

2. **Fase 2: Recursos Essenciais (2-3 meses)**

   - Playground completo
   - Gerenciamento de assinaturas
   - Documentação interativa
   - Integração com gateway de pagamento

3. **Fase 3: Aprimoramentos (3+ meses)**
   - Painel administrativo
   - Recursos avançados de análise
   - Personalização avançada
   - Internacionalização completa

### Integração com Backend

1. **Autenticação**:

   - Uso de JWT para autenticação
   - Refresh tokens para sessões prolongadas
   - Interceptor HTTP para incluir tokens

2. **Comunicação com API**:

   - Serviços Angular para cada módulo da API
   - Tratamento centralizado de erros
   - Cache de dados quando apropriado

3. **WebSockets**:
   - Integração com Socket.io para atualizações em tempo real
   - Componentes reativos para exibir dados em tempo real

## 📊 Métricas e Analytics

### Métricas a Serem Rastreadas

1. **Engajamento**:

   - Taxa de conversão de visitantes para registros
   - Taxa de conversão de free para planos pagos
   - Tempo médio de sessão
   - Páginas mais visitadas

2. **Uso da API**:

   - Volume de requisições por usuário/plano
   - Endpoints mais utilizados
   - Taxas de erro
   - Tempo de resposta

3. **Negócio**:
   - MRR (Monthly Recurring Revenue)
   - Churn rate
   - LTV (Lifetime Value)
   - CAC (Customer Acquisition Cost)

### Implementação

- Google Analytics para métricas gerais
- Métricas personalizadas para uso da API
- Dashboard administrativo com KPIs em tempo real

## 🔌 Endpoints e DTOs para Integração

### Endpoints da API

#### Autenticação e Usuários

| Método | Endpoint                      | Descrição                   | Acesso      |
| ------ | ----------------------------- | --------------------------- | ----------- |
| POST   | `/api/auth/register`          | Registrar novo usuário      | Público     |
| POST   | `/api/auth/login`             | Autenticar usuário          | Público     |
| POST   | `/api/auth/validate`          | Validar token JWT           | Autenticado |
| GET    | `/api/users/profile`          | Obter perfil do usuário     | Autenticado |
| PUT    | `/api/users/profile`          | Atualizar perfil do usuário | Autenticado |
| GET    | `/api/users/verify-email/:id` | Verificar email             | Público     |

#### API Keys

| Método | Endpoint            | Descrição                  | Acesso      |
| ------ | ------------------- | -------------------------- | ----------- |
| POST   | `/api/api-keys`     | Criar nova API key         | Autenticado |
| GET    | `/api/api-keys`     | Listar API keys do usuário | Autenticado |
| DELETE | `/api/api-keys/:id` | Desativar API key          | Autenticado |

#### Planos e Assinaturas

| Método | Endpoint                        | Descrição                     | Acesso      |
| ------ | ------------------------------- | ----------------------------- | ----------- |
| GET    | `/api/plans`                    | Listar planos disponíveis     | Público     |
| GET    | `/api/plans/:id`                | Obter detalhes de um plano    | Público     |
| POST   | `/api/subscriptions`            | Criar nova assinatura         | Autenticado |
| GET    | `/api/subscriptions`            | Listar assinaturas do usuário | Autenticado |
| GET    | `/api/subscriptions/active`     | Obter assinatura ativa        | Autenticado |
| POST   | `/api/subscriptions/:id/cancel` | Cancelar assinatura           | Autenticado |

#### Uso da API

| Método | Endpoint             | Descrição              | Acesso      |
| ------ | -------------------- | ---------------------- | ----------- |
| GET    | `/api/usage`         | Obter histórico de uso | Autenticado |
| GET    | `/api/usage/daily`   | Obter uso diário       | Autenticado |
| GET    | `/api/usage/monthly` | Obter uso mensal       | Autenticado |

#### Cotações

| Método | Endpoint                  | Descrição                                 | Acesso  |
| ------ | ------------------------- | ----------------------------------------- | ------- |
| GET    | `/api/cotacao`            | Obter taxa de câmbio                      | API Key |
| POST   | `/api/cotacao/converter`  | Converter valor entre moedas              | API Key |
| GET    | `/api/cotacao/moedas`     | Listar moedas disponíveis                 | API Key |
| GET    | `/api/cotacao/historical` | Obter histórico de taxas                  | API Key |
| WS     | `/cotacao`                | WebSocket para atualizações em tempo real | API Key |

### DTOs (Interfaces TypeScript)

#### Autenticação

```typescript
// Registro de usuário
interface CreateUserDto {
  email: string;
  name: string;
  password: string;
}

// Login
interface LoginDto {
  email: string;
  password: string;
}

// Resposta de login
interface LoginResponseDto {
  access_token: string;
}

// Perfil de usuário
interface UserProfileDto {
  id: string;
  email: string;
  name: string;
  emailVerified: boolean;
  createdAt: string;
}
```

#### API Keys

```typescript
// Criação de API Key
interface CreateApiKeyDto {
  name: string;
}

// Resposta de API Key
interface ApiKeyDto {
  id: string;
  name: string;
  key?: string; // Presente apenas na criação
  active: boolean;
  createdAt: string;
  lastUsedAt: string;
}
```

#### Planos e Assinaturas

```typescript
// Plano
interface PlanDto {
  id: string;
  name: string;
  description: string;
  price: number;
  dailyLimit: number;
  monthlyLimit: number;
  allowedCurrencies: string;
  historicalDataDays: number;
  hasWebSockets: boolean;
  hasAdvancedFeatures: boolean;
}

// Criação de assinatura
interface CreateSubscriptionDto {
  planId: string;
}

// Assinatura
interface SubscriptionDto {
  id: string;
  userId: string;
  planId: string;
  plan: PlanDto;
  status: "active" | "canceled" | "expired";
  startDate: string;
  endDate: string;
}
```

#### Uso da API

```typescript
// Registro de uso
interface UsageDto {
  id: string;
  userId: string;
  apiKeyId: string;
  endpoint: string;
  statusCode: number;
  responseTime: number;
  timestamp: string;
}

// Resumo de uso
interface UsageSummaryDto {
  total: number;
  successful: number;
  failed: number;
  averageResponseTime: number;
  byEndpoint: {
    endpoint: string;
    count: number;
  }[];
}
```

#### Cotações

```typescript
// Parâmetros de cotação
interface CotacaoQueryParams {
  from: string;
  to: string;
}

// Resposta de cotação
interface CotacaoResponseDto {
  from: string;
  to: string;
  rate: number;
  timestamp: string;
}

// Conversão
interface ConversaoDto {
  from: string;
  to: string;
  amount: number;
}

// Resposta de conversão
interface ConversaoResponseDto {
  from: string;
  to: string;
  amount: number;
  convertedValue: number;
  timestamp: string;
}

// Moeda
interface MoedaDto {
  codigo: string;
  nome: string;
  tipo: "fiat" | "crypto";
}

// Resposta de listagem de moedas
interface MoedasResponseDto {
  tipo: string;
  total: number;
  moedas: MoedaDto[];
  timestamp: string;
}
```

## 🧪 Implementação de Playgrounds e Integração com Swagger

### Playground de Testes

#### Abordagem Recomendada

1. **Playground Personalizado**:

   - Desenvolver uma interface intuitiva e amigável para testar a API
   - Organizar por categorias de endpoints (cotações, conversões, etc.)
   - Permitir salvar e compartilhar requisições
   - Exibir histórico de requisições do usuário

2. **Integração com Swagger UI**:

   - Incorporar o Swagger UI existente em um iframe ou componente dedicado
   - Personalizar a aparência para combinar com o design do portal
   - Adicionar recursos extras como tutoriais e exemplos de código

3. **Implementação Híbrida (Recomendada)**:
   - Usar o Swagger UI para documentação técnica completa
   - Desenvolver playground personalizado para experiência simplificada
   - Permitir transição fácil entre ambos

#### Implementação Técnica

1. **Componente de Playground Personalizado**:

```typescript
@Component({
  selector: "app-api-playground",
  templateUrl: "./api-playground.component.html",
  styleUrls: ["./api-playground.component.scss"],
})
export class ApiPlaygroundComponent implements OnInit {
  endpoints = [
    {
      name: "Obter Taxa de Câmbio",
      method: "GET",
      path: "/api/cotacao",
      params: ["from", "to"],
    },
    {
      name: "Converter Valor",
      method: "POST",
      path: "/api/cotacao/converter",
      body: true,
    },
    {
      name: "Listar Moedas",
      method: "GET",
      path: "/api/cotacao/moedas",
      params: ["tipo"],
    },
    // Outros endpoints
  ];

  selectedEndpoint: any;
  params: { [key: string]: string } = {};
  bodyContent: string = "";
  response: any;
  loading = false;

  constructor(private http: HttpClient) {}

  ngOnInit() {
    this.selectedEndpoint = this.endpoints[0];
  }

  executeRequest() {
    this.loading = true;

    // Construir a URL com parâmetros
    let url = this.selectedEndpoint.path;
    if (this.selectedEndpoint.params && this.selectedEndpoint.params.length) {
      url += "?";
      this.selectedEndpoint.params.forEach((param, index) => {
        if (this.params[param]) {
          url += `${param}=${this.params[param]}`;
          if (index < this.selectedEndpoint.params.length - 1) {
            url += "&";
          }
        }
      });
    }

    // Executar a requisição apropriada
    let request;
    if (this.selectedEndpoint.method === "GET") {
      request = this.http.get(url);
    } else if (this.selectedEndpoint.method === "POST") {
      const body = this.bodyContent ? JSON.parse(this.bodyContent) : {};
      request = this.http.post(url, body);
    }

    // Processar a resposta
    request.pipe(finalize(() => (this.loading = false))).subscribe(
      (response) => (this.response = response),
      (error) => (this.response = error)
    );
  }
}
```

2. **Integração do Swagger UI**:

```typescript
@Component({
  selector: "app-swagger-ui",
  template: `
    <div class="swagger-container">
      <iframe [src]="swaggerUrl | safe" width="100%" height="800px" frameBorder="0"> </iframe>
    </div>
  `,
  styles: [
    `
      .swagger-container {
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        overflow: hidden;
      }
    `,
  ],
})
export class SwaggerUiComponent implements OnInit {
  swaggerUrl: string;

  constructor(private configService: ConfigService) {}

  ngOnInit() {
    this.swaggerUrl = this.configService.getApiUrl() + "/docs";
  }
}

// Pipe para sanitizar a URL
@Pipe({
  name: "safe",
})
export class SafePipe implements PipeTransform {
  constructor(private sanitizer: DomSanitizer) {}

  transform(url) {
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }
}
```

### Estratégias para Testes Gratuitos

1. **Playground Público com Limites**:
   - Implementar contador de requisições por sessão/IP
   - Exibir claramente o número de requisições restantes
   - Mostrar CTA para registro quando o limite estiver próximo
   - Código de exemplo:

```typescript
@Injectable()
export class PublicPlaygroundService {
  private readonly MAX_REQUESTS = 10;
  private requestCount = 0;

  constructor(private http: HttpClient) {}

  executeRequest(endpoint: string, method: string, params: any, body?: any): Observable<any> {
    if (this.requestCount >= this.MAX_REQUESTS) {
      return throwError("Limite de requisições atingido. Registre-se para continuar.");
    }

    this.requestCount++;

    // Adicionar parâmetro especial para identificar requisições do playground
    const requestParams = { ...params, playground: "true" };

    // Executar requisição com delay artificial para plano gratuito
    return this.http
      .request(method, endpoint, {
        params: requestParams,
        body,
      })
      .pipe(
        delay(2000) // Adicionar delay artificial para versão gratuita
      );
  }

  getRemainingRequests(): number {
    return this.MAX_REQUESTS - this.requestCount;
  }
}
```

2. **Integração com Swagger para Testes Autenticados**:
   - Adicionar botão "Testar no Swagger" em cada endpoint da documentação
   - Passar automaticamente a API key do usuário para o Swagger
   - Permitir alternância entre diferentes API keys

```typescript
@Component({
  selector: "app-api-documentation",
  template: `
    <div class="endpoint-card" *ngFor="let endpoint of endpoints">
      <h3>{{ endpoint.name }}</h3>
      <p>{{ endpoint.description }}</p>
      <div class="endpoint-actions">
        <button (click)="testInPlayground(endpoint)">Testar no Playground</button>
        <button (click)="testInSwagger(endpoint)">Testar no Swagger</button>
      </div>
    </div>
  `,
})
export class ApiDocumentationComponent {
  endpoints = [
    /* lista de endpoints */
  ];

  constructor(private router: Router, private apiKeyService: ApiKeyService) {}

  testInPlayground(endpoint) {
    this.router.navigate(["/playground"], {
      queryParams: { endpoint: endpoint.path },
    });
  }

  testInSwagger(endpoint) {
    // Obter API key ativa
    this.apiKeyService.getActiveKey().subscribe((key) => {
      // Navegar para o Swagger com parâmetros para pré-configurar
      const swaggerUrl = `/docs?url=${endpoint.path}&apiKey=${key.key}`;
      window.open(swaggerUrl, "_blank");
    });
  }
}
```

### Aproveitando a Interface do Swagger

1. **Personalização do Swagger UI**:

   - Configurar o tema do Swagger para combinar com a identidade visual do portal
   - Adicionar descrições detalhadas e exemplos para cada endpoint
   - Incluir informações sobre limites de planos nos endpoints

2. **Integração Avançada**:

   - Implementar um plugin personalizado para o Swagger UI que adicione funcionalidades específicas
   - Criar um wrapper que permita salvar e compartilhar requisições feitas no Swagger
   - Adicionar métricas de uso diretamente na interface do Swagger

3. **Autenticação Automática**:
   - Implementar um mecanismo que preencha automaticamente a API key no Swagger
   - Permitir alternar entre diferentes API keys do usuário
   - Exemplo de implementação:

```typescript
// Serviço para configurar o Swagger UI
@Injectable()
export class SwaggerConfigService {
  constructor(private apiKeyService: ApiKeyService, private authService: AuthService) {}

  configureSwaggerUI() {
    // Verificar se o usuário está autenticado
    if (this.authService.isAuthenticated()) {
      // Obter API keys do usuário
      this.apiKeyService.getApiKeys().subscribe((keys) => {
        if (keys.length > 0) {
          // Selecionar a primeira API key ativa
          const activeKey = keys.find((k) => k.active);
          if (activeKey) {
            // Configurar o Swagger UI para usar esta API key
            this.injectApiKeyToSwagger(activeKey.key);
          }
        }
      });
    }
  }

  private injectApiKeyToSwagger(apiKey: string) {
    // Acessar o objeto window.ui do Swagger (requer que o Swagger UI esteja carregado)
    const swaggerUI = (window as any).ui;
    if (swaggerUI) {
      // Configurar a autorização
      swaggerUI.preauthorizeApiKey("api-key", apiKey);
    }
  }
}
```

## 📝 Conclusão

Esta especificação fornece um guia completo para o desenvolvimento do frontend da CurrencyWise API, incluindo detalhes sobre endpoints, DTOs e implementação de playgrounds de teste. A integração com o Swagger existente, combinada com um playground personalizado, oferecerá uma experiência de usuário superior e facilitará a adoção da API pelos desenvolvedores. A implementação deve seguir uma abordagem iterativa, priorizando recursos essenciais e expandindo gradualmente para criar uma experiência completa e profissional para os usuários da API.
