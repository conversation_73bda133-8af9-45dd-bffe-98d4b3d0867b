/* You can add global styles to this file, and also import other style files */
@import "tailwindcss";

/* Classes reutilizáveis com @apply */
/* Navegação */
.nav-link {
  @apply text-gray-700 hover:text-blue-600 flex items-center gap-1;
}

.nav-icon {
  @apply w-4 h-4;
}

.language-selector {
  @apply flex items-center gap-1 px-2 py-1 rounded-md hover:bg-gray-100 cursor-pointer;
}

/* <PERSON><PERSON><PERSON><PERSON> */
.btn-primary {
  @apply bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center gap-1;
}

.btn-secondary {
  @apply bg-transparent border-2 border-blue-600 text-blue-600 px-4 py-2 rounded-md hover:bg-blue-600 hover:text-white flex items-center gap-1 transition-colors duration-200;
}

/* Footer */
.footer-heading {
  @apply text-lg font-semibold mb-4;
}

.footer-link {
  @apply text-gray-400 hover:text-white;
}

/* Cards e Containers */
.card {
  @apply bg-white rounded-lg shadow-md p-6;
}

.card-title {
  @apply text-xl font-semibold mb-4;
}

.section-title {
  @apply text-2xl font-bold mb-6;
}

.page-title {
  @apply text-3xl font-bold mb-6;
}

.flex-between {
  @apply flex justify-between items-center;
}

.flex-center {
  @apply flex items-center justify-center;
}

/* Texto */
.text-muted {
  @apply text-sm text-gray-600;
}

.text-value {
  @apply font-medium;
}

/* Ícones */
.icon-sm {
  @apply w-4 h-4;
}

.icon-md {
  @apply w-6 h-6;
}

.icon-lg {
  @apply w-8 h-8;
}

/* Código */
.code-block {
  @apply bg-gray-900 text-white p-4 rounded-lg overflow-x-auto;
}

/* Status */
.badge-success {
  @apply px-2 py-1 bg-emerald-100 text-emerald-800 text-xs font-medium rounded-full;
}

.badge-warning {
  @apply px-2 py-1 bg-amber-100 text-amber-800 text-xs font-medium rounded-full;
}

.badge-danger {
  @apply px-2 py-1 bg-rose-100 text-rose-800 text-xs font-medium rounded-full;
}

/* Grids */
.grid-responsive {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

/* Containers */
.container-padded {
  @apply container mx-auto px-4 py-8;
}

/* Estilos Globais para Abas Padronizadas */
.tab-button-base {
  @apply px-4 py-3 md:px-6 md:py-3 text-sm md:text-base font-medium focus:outline-none transition-colors duration-150 ease-in-out border-b-2 hover:text-blue-600 text-gray-500 border-transparent;
  margin-bottom: -1px; /* Para sobrepor a borda do container pai se houver */
}

.tab-button-active {
  @apply text-blue-600 border-blue-600 bg-white;
}

/* Estilos Globais para Cards de Termos/Seções Padronizados */
.term-card {
  @apply bg-white rounded-lg shadow-lg p-6 md:p-8 border border-gray-200 transition-all duration-300 ease-in-out hover:shadow-xl hover:transform hover:-translate-y-1;
}
