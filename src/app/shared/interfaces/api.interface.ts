/**
 * Interface para endpoints da API
 */
export interface ApiEndpoint {
  /**
   * Identificador único do endpoint
   */
  id: string;
  
  /**
   * Nome amigável do endpoint
   */
  name: string;
  
  /**
   * Método HTTP (GET, POST, etc.)
   */
  method: string;
  
  /**
   * Caminho do endpoint
   */
  path: string;
  
  /**
   * Descrição do endpoint
   */
  description: string;
  
  /**
   * Parâmetros aceitos pelo endpoint
   */
  params: ApiParam[];
}

/**
 * Interface para parâmetros da API
 */
export interface ApiParam {
  /**
   * Nome do parâmetro
   */
  name: string;
  
  /**
   * Tipo do parâmetro (string, number, etc.)
   */
  type: string;
  
  /**
   * Indica se o parâmetro é obrigatório
   */
  required: boolean;
  
  /**
   * Descrição do parâmetro
   */
  description: string;
  
  /**
   * Valor padrão do parâmetro (opcional)
   */
  default?: string;
  
  /**
   * Opções disponíveis para o parâmetro (opcional)
   */
  options?: string[];
}

/**
 * Interface para respostas da API
 */
export interface ApiResponse {
  /**
   * Código de status HTTP
   */
  status: number;
  
  /**
   * Texto do status HTTP
   */
  statusText: string;
  
  /**
   * Dados retornados pela API
   */
  data: any;
  
  /**
   * Tempo de resposta em milissegundos
   */
  time: number;
}
