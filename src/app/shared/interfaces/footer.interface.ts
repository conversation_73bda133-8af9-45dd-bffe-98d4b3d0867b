/**
 * Interface para links do footer
 */
export interface FooterLink {
  /**
   * Texto a ser exibido
   */
  label: string;
  
  /**
   * URL de destino
   */
  url: string;
  
  /**
   * Indica se é uma rota interna (RouterLink) ou link externo
   */
  isRouterLink: boolean;
}

/**
 * Interface para seções do footer
 */
export interface FooterSection {
  /**
   * <PERSON><PERSON><PERSON><PERSON> da se<PERSON>
   */
  title: string;
  
  /**
   * Links da seção
   */
  links: FooterLink[];
}
