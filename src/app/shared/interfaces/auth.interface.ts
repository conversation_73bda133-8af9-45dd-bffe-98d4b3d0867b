/**
 * Interface para credenciais de login
 */
export interface LoginCredentials {
  /**
   * Email do usuário
   */
  email: string;
  
  /**
   * Senha do usuário
   */
  password: string;
  
  /**
   * Indica se o usuário deseja permanecer conectado
   */
  rememberMe?: boolean;
}

/**
 * Interface para dados de registro
 */
export interface RegisterData {
  /**
   * Nome do usuário
   */
  name: string;
  
  /**
   * Email do usuário
   */
  email: string;
  
  /**
   * Senha do usuário
   */
  password: string;
  
  /**
   * Confirmação da senha
   */
  confirmPassword: string;
  
  /**
   * Indica se o usuário aceitou os termos de serviço
   */
  acceptTerms: boolean;
}

/**
 * Interface para dados de recuperação de senha
 */
export interface ForgotPasswordData {
  /**
   * Email do usuário
   */
  email: string;
}

/**
 * Interface para resposta de autenticação
 */
export interface AuthResponse {
  /**
   * Token de acesso
   */
  accessToken: string;
  
  /**
   * Token de atualização
   */
  refreshToken: string;
  
  /**
   * Dados do usuário
   */
  user: User;
}

/**
 * Interface para dados do usuário
 */
export interface User {
  /**
   * Identificador único do usuário
   */
  id: string;
  
  /**
   * Nome do usuário
   */
  name: string;
  
  /**
   * Email do usuário
   */
  email: string;
  
  /**
   * URL da foto do usuário
   */
  avatarUrl?: string;
  
  /**
   * Indica se o email do usuário foi verificado
   */
  emailVerified: boolean;
  
  /**
   * Data de criação da conta
   */
  createdAt: Date;
}
