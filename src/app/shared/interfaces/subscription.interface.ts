/**
 * Interface para planos de assinatura
 */
export interface Plan {
  /**
   * Identificador único do plano
   */
  id: string;
  
  /**
   * Nome do plano
   */
  name: string;
  
  /**
   * Preço do plano
   */
  price: number;
  
  /**
   * Período de cobrança (mensal, anual, etc.)
   */
  billingPeriod: string;
  
  /**
   * Descrição do plano
   */
  description: string;
  
  /**
   * Recursos incluídos no plano
   */
  features: string[];
  
  /**
   * Limite de requisições por mês
   */
  requestLimit: number;
  
  /**
   * Indica se é o plano recomendado
   */
  isPopular?: boolean;
}

/**
 * Interface para assinatura do usuário
 */
export interface Subscription {
  /**
   * Identificador único da assinatura
   */
  id: string;
  
  /**
   * Plano associado à assinatura
   */
  plan: Plan;
  
  /**
   * Data de início da assinatura
   */
  startDate: Date;
  
  /**
   * Data da próxima cobrança
   */
  nextBillingDate: Date;
  
  /**
   * Status da assinatura (ativo, cancelado, etc.)
   */
  status: 'active' | 'canceled' | 'past_due' | 'trialing';
  
  /**
   * Método de pagamento
   */
  paymentMethod: {
    /**
     * Tipo do cartão (visa, mastercard, etc.)
     */
    type: string;
    
    /**
     * Últimos 4 dígitos do cartão
     */
    last4: string;
    
    /**
     * Mês de expiração
     */
    expMonth: number;
    
    /**
     * Ano de expiração
     */
    expYear: number;
  };
  
  /**
   * Uso atual de requisições
   */
  usage: {
    /**
     * Requisições usadas no período atual
     */
    current: number;
    
    /**
     * Porcentagem do limite usado
     */
    percentage: number;
  };
}

/**
 * Interface para histórico de faturas
 */
export interface Invoice {
  /**
   * Identificador único da fatura
   */
  id: string;
  
  /**
   * Data da fatura
   */
  date: Date;
  
  /**
   * Valor da fatura
   */
  amount: number;
  
  /**
   * Status da fatura (pago, pendente, etc.)
   */
  status: 'paid' | 'pending' | 'failed';
  
  /**
   * URL para download da fatura
   */
  downloadUrl: string;
}
