import { headerTranslations } from './header.translations';

/**
 * Arquivo de traduções para o aplicativo
 *
 * Estrutura:
 * - Cada seção representa um componente ou área do aplicativo
 * - Cada seção contém traduções para todos os idiomas suportados
 * - As chaves de tradução devem ser as mesmas em todos os idiomas
 */

// Traduções para a página inicial
export const homeTranslations = {
  pt: {
    hero: {
      title: 'API de Conversão de Moedas Confiável e Rápida',
      subtitle:
        'Acesse taxas de câmbio em tempo real e histórico para mais de 170 moedas com nossa API simples e poderosa.',
      startButton: 'Começar Agora',
      tryButton: 'Testar API',
    },
    stats: {
      developers: 'Desenvolvedores',
      requests: 'Requisições/mês',
      uptime: 'Disponibilidade',
      countries: 'Países',
    },
    features: {
      title: 'Por que escolher a CurrencyWise?',
      subtitle:
        'Nossa API oferece a melhor experiência para desenvolvedores com recursos poderosos e confiáveis.',
      fast: {
        title: 'Rápida e Confiável',
        description:
          'Tempos de resposta inferiores a 50ms com disponibilidade de 99,9% garantida.',
      },
      secure: {
        title: 'Segura',
        description:
          'Conexões HTTPS criptografadas e autenticação por API key para proteger seus dados.',
      },
      easy: {
        title: 'Fácil de Usar',
        description:
          'Documentação completa, SDKs para várias linguagens e suporte técnico especializado.',
      },
    },
    demo: {
      title: 'Experimente Nossa API',
      subtitle:
        'Veja como é simples integrar taxas de câmbio em tempo real no seu projeto.',
      requestTitle: 'Requisição',
      responseTitle: 'Resposta',
      tryButton: 'Testar no Playground',
    },
    pricing: {
      title: 'Planos para Todos os Tamanhos de Projetos',
      subtitle:
        'Escolha o plano ideal para suas necessidades e comece a integrar hoje mesmo.',
      basic: {
        title: 'Básico',
        price: 'Grátis',
        period: 'Para sempre',
        feature1: '1.000 requisições/mês',
        feature2: 'Taxas atualizadas a cada hora',
        feature3: 'Suporte por email',
        button: 'Começar Grátis',
      },
      professional: {
        title: 'Profissional',
        price: 'R$ 49',
        period: '/mês',
        badge: 'POPULAR',
        feature1: '100.000 requisições/mês',
        feature2: 'Taxas atualizadas a cada 10 minutos',
        feature3: 'Dados históricos (1 ano)',
        feature4: 'Suporte prioritário',
        button: 'Assinar Agora',
      },
      enterprise: {
        title: 'Empresarial',
        price: 'R$ 199',
        period: '/mês',
        feature1: '1.000.000 requisições/mês',
        feature2: 'Taxas em tempo real',
        feature3: 'Dados históricos completos',
        feature4: 'Suporte 24/7',
        feature5: 'SLA garantido',
        button: 'Contate Vendas',
      },
    },
    cta: {
      title: 'Pronto para Começar?',
      subtitle:
        'Junte-se a milhares de desenvolvedores que confiam na CurrencyWise para suas necessidades de conversão de moedas.',
      primaryButton: 'Criar Conta Grátis',
      secondaryButton: 'Ver Documentação',
    },
  },
  en: {
    hero: {
      title: 'Fast and Reliable Currency Conversion API',
      subtitle:
        'Access real-time and historical exchange rates for over 170 currencies with our simple and powerful API.',
      startButton: 'Get Started',
      tryButton: 'Try API',
    },
    stats: {
      developers: 'Developers',
      requests: 'Requests/month',
      uptime: 'Uptime',
      countries: 'Countries',
    },
    features: {
      title: 'Why Choose CurrencyWise?',
      subtitle:
        'Our API provides the best developer experience with powerful and reliable features.',
      fast: {
        title: 'Fast and Reliable',
        description:
          'Response times under 50ms with guaranteed 99.9% availability.',
      },
      secure: {
        title: 'Secure',
        description:
          'Encrypted HTTPS connections and API key authentication to protect your data.',
      },
      easy: {
        title: 'Easy to Use',
        description:
          'Comprehensive documentation, SDKs for various languages, and expert technical support.',
      },
    },
    demo: {
      title: 'Try Our API',
      subtitle:
        'See how simple it is to integrate real-time exchange rates into your project.',
      requestTitle: 'Request',
      responseTitle: 'Response',
      tryButton: 'Test in Playground',
    },
    pricing: {
      title: 'Plans for All Project Sizes',
      subtitle:
        'Choose the ideal plan for your needs and start integrating today.',
      basic: {
        title: 'Basic',
        price: 'Free',
        period: 'Forever',
        feature1: '1,000 requests/month',
        feature2: 'Hourly updated rates',
        feature3: 'Email support',
        button: 'Start Free',
      },
      professional: {
        title: 'Professional',
        price: '$19',
        period: '/month',
        badge: 'POPULAR',
        feature1: '100,000 requests/month',
        feature2: 'Rates updated every 10 minutes',
        feature3: 'Historical data (1 year)',
        feature4: 'Priority support',
        button: 'Subscribe Now',
      },
      enterprise: {
        title: 'Enterprise',
        price: '$99',
        period: '/month',
        feature1: '1,000,000 requests/month',
        feature2: 'Real-time rates',
        feature3: 'Complete historical data',
        feature4: '24/7 support',
        feature5: 'Guaranteed SLA',
        button: 'Contact Sales',
      },
    },
    cta: {
      title: 'Ready to Get Started?',
      subtitle:
        'Join thousands of developers who trust CurrencyWise for their currency conversion needs.',
      primaryButton: 'Create Free Account',
      secondaryButton: 'View Documentation',
    },
  },
};

// Traduções para componentes comuns
export const commonTranslations = {
  pt: {
    loading: 'Carregando...',
    error: 'Ocorreu um erro',
    success: 'Operação realizada com sucesso',
    save: 'Salvar',
    cancel: 'Cancelar',
    confirm: 'Confirmar',
    delete: 'Excluir',
    edit: 'Editar',
    view: 'Visualizar',
    search: 'Buscar',
    filter: 'Filtrar',
    noResults: 'Nenhum resultado encontrado',
    required: 'Campo obrigatório',
    invalidEmail: 'Email inválido',
    passwordMismatch: 'As senhas não coincidem',
    passwordTooShort: 'A senha deve ter pelo menos 8 caracteres',
    welcome: 'Bem-vindo',
    logout: 'Sair',
    profile: 'Perfil',
    settings: 'Configurações',
    notifications: 'Notificações',
    help: 'Ajuda',
    about: 'Sobre',
    contact: 'Contato',
    privacyPolicy: 'Política de Privacidade',
    termsOfService: 'Termos de Serviço',
    copyright: 'Todos os direitos reservados',
    language: 'Idioma',
    theme: 'Tema',
    dark: 'Escuro',
    light: 'Claro',
    system: 'Sistema',
    version: 'Versão',
    lastUpdated: 'Última atualização',
    poweredBy: 'Desenvolvido por',
  },
  en: {
    loading: 'Loading...',
    error: 'An error occurred',
    success: 'Operation completed successfully',
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    delete: 'Delete',
    edit: 'Edit',
    view: 'View',
    search: 'Search',
    filter: 'Filter',
    noResults: 'No results found',
    required: 'Required field',
    invalidEmail: 'Invalid email',
    passwordMismatch: 'Passwords do not match',
    passwordTooShort: 'Password must be at least 8 characters',
    welcome: 'Welcome',
    logout: 'Logout',
    profile: 'Profile',
    settings: 'Settings',
    notifications: 'Notifications',
    help: 'Help',
    about: 'About',
    contact: 'Contact',
    privacyPolicy: 'Privacy Policy',
    termsOfService: 'Terms of Service',
    copyright: 'All rights reserved',
    language: 'Language',
    theme: 'Theme',
    dark: 'Dark',
    light: 'Light',
    system: 'System',
    version: 'Version',
    lastUpdated: 'Last updated',
    poweredBy: 'Powered by',
  },
};

// Traduções para o footer
export const footerTranslations = {
  pt: {
    company: 'Empresa',
    about: 'Sobre nós',
    careers: 'Carreiras',
    blog: 'Blog',
    press: 'Imprensa',
    resources: 'Recursos',
    documentation: 'Documentação',
    pricing: 'Preços',
    faq: 'Perguntas Frequentes',
    api: 'API',
    legal: 'Legal',
    terms: 'Termos de Serviço',
    privacy: 'Política de Privacidade',
    cookies: 'Política de Cookies',
    security: 'Segurança',
    contact: 'Contato',
    support: 'Suporte',
    sales: 'Vendas',
    partnerships: 'Parcerias',
    copyright: '© 2024 CurrencyWise. Todos os direitos reservados.',
  },
  en: {
    company: 'Company',
    about: 'About Us',
    careers: 'Careers',
    blog: 'Blog',
    press: 'Press',
    resources: 'Resources',
    documentation: 'Documentation',
    pricing: 'Pricing',
    faq: 'FAQ',
    api: 'API',
    legal: 'Legal',
    terms: 'Terms of Service',
    privacy: 'Privacy Policy',
    cookies: 'Cookie Policy',
    security: 'Security',
    contact: 'Contact',
    support: 'Support',
    sales: 'Sales',
    partnerships: 'Partnerships',
    copyright: '© 2024 CurrencyWise. All rights reserved.',
  },
};

// Traduções para o dashboard
export const dashboardTranslations = {
  pt: {
    title: 'Dashboard',
    lastUpdated: 'Última atualização',
    refresh: 'Atualizar',
    loading: 'Carregando dados...',
    recentConversions: 'Conversões Recentes',
    noRecentConversions: 'Nenhuma conversão recente',
    apiUsage: 'Uso da API',
    requestsThisMonth: 'Requisições este mês',
    remainingRequests: 'Requisições restantes',
    plan: 'Plano atual',
    upgrade: 'Fazer upgrade',
    viewHistory: 'Ver histórico',
    currencies: 'Moedas',
    exchangeRates: 'Taxas de Câmbio',
    charts: 'Gráficos',
    statistics: 'Estatísticas',
    currentPlan: 'Plano atual',
    nextRenewal: 'Próxima renovação',
    status: 'Status',
    active: 'Ativo',
  },
  en: {
    title: 'Dashboard',
    lastUpdated: 'Last updated',
    refresh: 'Refresh',
    loading: 'Loading data...',
    recentConversions: 'Recent Conversions',
    noRecentConversions: 'No recent conversions',
    apiUsage: 'API Usage',
    requestsThisMonth: 'Requests this month',
    remainingRequests: 'Remaining requests',
    plan: 'Current plan',
    upgrade: 'Upgrade',
    viewHistory: 'View history',
    currencies: 'Currencies',
    exchangeRates: 'Exchange Rates',
    charts: 'Charts',
    statistics: 'Statistics',
    currentPlan: 'Current plan',
    nextRenewal: 'Next renewal',
    status: 'Status',
    active: 'Active',
  },
};

// Traduções para a página de Termos e Privacidade
export const termsTranslations = {
  pt: {
    pageTitle: 'Termos de Serviço e Política de Privacidade',
    lastUpdated: 'Última atualização',
    tabs: {
      termsOfService: 'Termos de Serviço',
      privacyPolicy: 'Política de Privacidade',
    },
    contact: {
      question: 'Tem dúvidas sobre nossos termos ou política de privacidade?',
      button: 'Entrar em Contato com o Suporte',
    },
    acceptanceNotice:
      'Ao continuar usando nossa API, você concorda com estes termos. Se você não concordar com qualquer parte destes termos, por favor, não use nossa API.',
    termsOfService: {
      acceptance: {
        title: '1. Aceitação dos Termos',
        paragraph1:
          'Ao acessar e usar a API CurrencyWise, você concorda em cumprir estes termos de serviço, todas as leis e regulamentos aplicáveis, e reconhece que é responsável pelo cumprimento de quaisquer leis locais aplicáveis.',
        paragraph2:
          'Se você não concordar com qualquer parte destes termos, você não está autorizado a acessar ou usar nossa API.',
      },
      apiUsage: {
        title: '2. Uso da API',
        paragraph1:
          'A API CurrencyWise é fornecida para uso em conformidade com estes termos. Você concorda em não usar a API para qualquer finalidade ilegal ou não autorizada.',
        paragraph2:
          'Você não deve tentar obter acesso não autorizado à nossa API, outros sistemas ou redes conectadas à nossa API.',
        paragraph3:
          'Você é responsável por manter a confidencialidade de suas credenciais de API e por todas as atividades que ocorrem sob sua conta.',
      },
      usageLimitations: {
        title: '3. Limitações de Uso',
        paragraph1:
          'O uso da API está sujeito a limites de taxa e outras restrições conforme detalhado em sua assinatura. O uso excessivo ou abusivo pode resultar em suspensão ou término do acesso.',
        listTitle: 'Você concorda em não:',
        listItem1:
          'Usar a API de maneira que possa danificar, desativar, sobrecarregar ou prejudicar nossos servidores ou redes.',
        listItem2:
          'Usar a API para distribuir malware, spyware ou qualquer outro código malicioso.',
        listItem3:
          'Revender, sublicenciar ou redistribuir a API sem nossa autorização expressa por escrito.',
        listItem4: 'Usar a API para criar um produto ou serviço concorrente.',
      },
      apiKeys: {
        title: '4. Chaves de API',
        paragraph1: '...',
        paragraph2: '...',
      },
      intellectualProperty: {
        title: '5. Propriedade Intelectual',
        paragraph1: '...',
        paragraph2: '...',
      },
      liabilityLimitation: {
        title: '6. Limitação de Responsabilidade',
        paragraph1: '...',
        paragraph2: '...',
      },
      indemnification: { title: '7. Indenização', paragraph1: '...' },
      modifications: {
        title: '8. Modificações',
        paragraph1: '...',
        paragraph2: '...',
      },
      termination: { title: '9. Rescisão', paragraph1: '...' },
      applicableLaw: {
        title: '10. Lei Aplicável',
        paragraph1: '...',
        paragraph2: '...',
      },
    },
    privacyPolicy: {
      informationCollected: {
        title: '1. Informações que Coletamos',
        paragraph1:
          'Coletamos as seguintes informações quando você se registra e usa nossa API:',
        listItem1:
          'Informações de registro: nome, endereço de e-mail, informações de contato.',
        listItem2:
          'Informações de uso: padrões de uso da API, volume de solicitações, endpoints acessados.',
        listItem3:
          'Informações de pagamento: detalhes de faturamento e pagamento.',
        listItem4:
          'Informações técnicas: endereço IP, tipo de navegador, sistema operacional.',
      },
      howWeUseInfo: {
        title: '2. Como Usamos Suas Informações',
        paragraph1: 'Usamos suas informações para:',
        listItem1: 'Fornecer e manter nossos serviços.',
        listItem2: 'Processar transações e enviar faturas.',
        listItem3: 'Monitorar e analisar tendências de uso.',
        listItem4:
          'Comunicar-se com você sobre atualizações, suporte e outras informações relacionadas ao serviço.',
        listItem5:
          'Detectar, prevenir e resolver problemas técnicos e de segurança.',
      },
      sharingInfo: {
        title: '3. Compartilhamento de Informações',
        paragraph1: '...',
      },
      dataSecurity: {
        title: '4. Segurança de Dados',
        paragraph1: '...',
        paragraph2: '...',
      },
      yourRights: {
        title: '5. Seus Direitos',
        paragraph1:
          'Dependendo da sua localização, você pode ter certos direitos em relação às suas informações pessoais, incluindo:',
        listItem1: 'Direito de acesso às suas informações.',
        listItem2: 'Direito de corrigir informações imprecisas.',
        listItem3: 'Direito de excluir suas informações.',
        listItem4: 'Direito de restringir ou opor-se ao processamento.',
        listItem5:
          'Direito à portabilidade de dados. Para exercer esses direitos, entre em contato conosco através do e-mail <EMAIL>.',
      },
      dataRetention: {
        title: '6. Retenção de Dados',
        paragraph1: '...',
        paragraph2: '...',
      },
      cookiesAndSimilarTechnologies: {
        title: '7. Cookies e Tecnologias Semelhantes',
        paragraph1: '...',
        paragraph2: '...',
      },
      policyModifications: {
        title: '8. Alterações a Esta Política',
        paragraph1: '...',
        paragraph2: '...',
      },
      contact: {
        title: '9. Contato',
        paragraph1:
          'Se você tiver dúvidas sobre esta Política de Privacidade, entre em contato conosco em:',
        paragraph2:
          'E-mail: <EMAIL><br>Endereço: Av. Paulista, 1000, São Paulo, SP, Brasil',
      },
    },
  },
  en: {
    pageTitle: 'Terms of Service and Privacy Policy',
    lastUpdated: 'Last updated',
    tabs: {
      termsOfService: 'Terms of Service',
      privacyPolicy: 'Privacy Policy',
    },
    contact: {
      question: 'Have questions about our terms or privacy policy?',
      button: 'Contact Support',
    },
    acceptanceNotice:
      'By continuing to use our API, you agree to these terms. If you do not agree with any part of these terms, please do not use our API.',
    termsOfService: {
      acceptance: {
        title: '1. Acceptance of Terms',
        paragraph1:
          'By accessing and using the CurrencyWise API, you agree to comply with these terms of service, all applicable laws and regulations, and acknowledge that you are responsible for compliance with any applicable local laws.',
        paragraph2:
          'If you do not agree with any part of these terms, you are not authorized to access or use our API.',
      },
      apiUsage: {
        title: '2. API Usage',
        paragraph1:
          'The CurrencyWise API is provided for use in accordance with these terms. You agree not to use the API for any illegal or unauthorized purpose.',
        paragraph2:
          'You must not attempt to gain unauthorized access to our API, other systems, or networks connected to our API.',
        paragraph3:
          'You are responsible for maintaining the confidentiality of your API credentials and for all activities that occur under your account.',
      },
      usageLimitations: {
        title: '3. Usage Limitations',
        paragraph1:
          'Use of the API is subject to rate limits and other restrictions as detailed in your subscription. Excessive or abusive use may result in suspension or termination of access.',
        listTitle: 'You agree not to:',
        listItem1:
          'Use the API in a manner that could damage, disable, overburden, or impair our servers or networks.',
        listItem2:
          'Use the API to distribute malware, spyware, or any other malicious code.',
        listItem3:
          'Resell, sublicense, or redistribute the API without our express written permission.',
        listItem4: 'Use the API to create a competing product or service.',
      },
      apiKeys: { title: '4. API Keys', paragraph1: '...', paragraph2: '...' },
      intellectualProperty: {
        title: '5. Intellectual Property',
        paragraph1: '...',
        paragraph2: '...',
      },
      liabilityLimitation: {
        title: '6. Limitation of Liability',
        paragraph1: '...',
        paragraph2: '...',
      },
      indemnification: { title: '7. Indemnification', paragraph1: '...' },
      modifications: {
        title: '8. Modifications',
        paragraph1: '...',
        paragraph2: '...',
      },
      termination: { title: '9. Termination', paragraph1: '...' },
      applicableLaw: {
        title: '10. Governing Law',
        paragraph1: '...',
        paragraph2: '...',
      },
    },
    privacyPolicy: {
      informationCollected: {
        title: '1. Information We Collect',
        paragraph1:
          'We collect the following information when you register and use our API:',
        listItem1:
          'Registration information: name, email address, contact information.',
        listItem2:
          'Usage information: API usage patterns, request volume, accessed endpoints.',
        listItem3: 'Payment information: billing and payment details.',
        listItem4:
          'Technical information: IP address, browser type, operating system.',
      },
      howWeUseInfo: {
        title: '2. How We Use Your Information',
        paragraph1: 'We use your information to:',
        listItem1: 'Provide and maintain our services.',
        listItem2: 'Process transactions and send invoices.',
        listItem3: 'Monitor and analyze usage trends.',
        listItem4:
          'Communicate with you about updates, support, and other service-related information.',
        listItem5:
          'Detect, prevent, and address technical and security issues.',
      },
      sharingInfo: { title: '3. Information Sharing', paragraph1: '...' },
      dataSecurity: {
        title: '4. Data Security',
        paragraph1: '...',
        paragraph2: '...',
      },
      yourRights: {
        title: '5. Your Rights',
        paragraph1:
          'Depending on your location, you may have certain rights regarding your personal information, including:',
        listItem1: 'Right to access your information.',
        listItem2: 'Right to correct inaccurate information.',
        listItem3: 'Right to delete your information.',
        listItem4: 'Right to restrict or object to processing.',
        listItem5:
          'Right to data portability. To exercise these rights, please contact <NAME_EMAIL>.',
      },
      dataRetention: {
        title: '6. Data Retention',
        paragraph1: '...',
        paragraph2: '...',
      },
      cookiesAndSimilarTechnologies: {
        title: '7. Cookies and Similar Technologies',
        paragraph1: '...',
        paragraph2: '...',
      },
      policyModifications: {
        title: '8. Changes to This Policy',
        paragraph1: '...',
        paragraph2: '...',
      },
      contact: {
        title: '9. Contact Us',
        paragraph1:
          'If you have questions about this Privacy Policy, please contact us at:',
        paragraph2:
          'Email: <EMAIL><br>Address: 1000 Paulista Ave, São Paulo, SP, Brazil',
      },
    },
  },
};

// Exporta todas as traduções
export const translations = {
  header: headerTranslations,
  common: commonTranslations,
  footer: footerTranslations,
  home: homeTranslations,
  dashboard: dashboardTranslations,
  terms: termsTranslations,
};

// Tipo para facilitar o acesso às traduções
export type TranslationSection = keyof typeof translations;
