import { CommonModule } from '@angular/common';
import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { LucideAngularModule } from 'lucide-angular';
import { Subscription } from 'rxjs';
import {
  Notification,
  NotificationService,
} from '../../../core/services/notification.service';

@Component({
  selector: 'app-notification',
  standalone: true,
  imports: [CommonModule, LucideAngularModule],
  template: `
    <div
      *ngIf="notification"
      [class]="getNotificationClasses()"
      class="fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 ease-in-out"
      role="alert"
    >
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <lucide-icon [name]="getIconName()" class="h-5 w-5"></lucide-icon>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium">{{ notification.message }}</p>
        </div>
        <div class="ml-auto pl-3">
          <div class="-mx-1.5 -my-1.5">
            <button
              type="button"
              (click)="close()"
              class="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2"
              [class]="getCloseButtonClasses()"
            >
              <span class="sr-only">Fechar</span>
              <lucide-icon name="x" class="h-5 w-5"></lucide-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
})
export class NotificationComponent implements OnInit, OnDestroy {
  notification: Notification | null = null;
  private subscription: Subscription | null = null;

  constructor(private notificationService: NotificationService) {}

  ngOnInit() {
    this.subscription = this.notificationService.notification$.subscribe(
      (notification) => {
        this.notification = notification;
      }
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  close() {
    this.notificationService.clear();
  }

  getNotificationClasses(): string {
    if (!this.notification) return '';

    const baseClasses = 'max-w-sm w-full';
    const typeClasses = {
      success: 'bg-green-50 text-green-800',
      error: 'bg-red-50 text-red-800',
      info: 'bg-blue-50 text-blue-800',
      warning: 'bg-yellow-50 text-yellow-800',
    };

    return `${baseClasses} ${typeClasses[this.notification.type]}`;
  }

  getCloseButtonClasses(): string {
    if (!this.notification) return '';

    const typeClasses = {
      success:
        'bg-green-50 text-green-500 hover:bg-green-100 focus:ring-green-600',
      error: 'bg-red-50 text-red-500 hover:bg-red-100 focus:ring-red-600',
      info: 'bg-blue-50 text-blue-500 hover:bg-blue-100 focus:ring-blue-600',
      warning:
        'bg-yellow-50 text-yellow-500 hover:bg-yellow-100 focus:ring-yellow-600',
    };

    return typeClasses[this.notification.type];
  }

  getIconName(): string {
    if (!this.notification) return '';

    const icons = {
      success: 'check-circle',
      error: 'x-circle',
      info: 'info',
      warning: 'alert-triangle',
    };

    return icons[this.notification.type];
  }
}
