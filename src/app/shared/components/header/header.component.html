<header class="bg-white shadow-md">
  <div class="container mx-auto px-4 py-4 flex justify-between items-center">
    <div class="flex items-center">
      <a routerLink="/" class="text-blue-600 font-bold text-xl cursor-pointer"
        >CurrencyWise</a
      >
    </div>
    <nav class="hidden md:flex space-x-6">
      @for (item of navItems; track item.path) {
      <a
        [routerLink]="item.path"
        class="text-gray-700 hover:text-blue-600 flex items-center gap-1 cursor-pointer"
      >
        <i-lucide [img]="item.icon" class="w-4 h-4"></i-lucide>
        <span>{{ item.label | translate }}</span>
      </a>
      }
    </nav>
    <div class="flex items-center space-x-4">
      <!-- Seletor de idioma -->
      <div class="relative">
        <button
          (click)="toggleLanguage()"
          class="flex items-center gap-1 px-2 py-1 rounded-md hover:bg-gray-100 cursor-pointer text-gray-700 hover:text-blue-600 transition-colors duration-200"
          [title]="getLanguageTooltip()"
        >
          <i-lucide [img]="GlobeIcon" class="w-4 h-4"></i-lucide>
          <span class="text-sm font-medium">
            {{ translationService.getCurrentLanguageFlag() }}
            {{ currentLanguage.toUpperCase() }}
          </span>
        </button>
      </div>

      <a
        routerLink="/auth/login"
        class="text-gray-700 hover:text-blue-600 flex items-center gap-1"
      >
        <i-lucide [img]="LogInIcon" class="w-4 h-4"></i-lucide>
        <span>{{ "header.login" | translate }}</span>
      </a>
      <a
        routerLink="/auth/register"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center gap-1"
      >
        <i-lucide [img]="UserPlusIcon" class="w-4 h-4"></i-lucide>
        <span>{{ "header.register" | translate }}</span>
      </a>
    </div>
  </div>
</header>
