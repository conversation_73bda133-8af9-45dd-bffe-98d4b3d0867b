import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import {
  ChartBar,
  CreditCardIcon,
  FileTextIcon,
  GlobeIcon,
  HomeIcon,
  KeyIcon,
  LogInIcon,
  LucideAngularModule,
  TerminalIcon,
  UserPlusIcon,
} from 'lucide-angular';
import { Subscription } from 'rxjs';
import { Language } from '../../../core/services/translation.service';
import { NavItem } from '../../interfaces/nav-item.interface';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule, RouterModule, LucideAngularModule, TranslateModule],
  templateUrl: './header.component.html',
  styleUrl: './header.component.css',
})
export class HeaderComponent implements OnInit, OnDestroy {
  readonly LogInIcon = LogInIcon;
  readonly UserPlusIcon = UserPlusIcon;
  readonly GlobeIcon = GlobeIcon;

  currentLanguage: Language = 'pt';
  nextLanguageName: string = '';
  currentLanguageFlag: string = '';
  private languageSubscription?: Subscription;

  navItems: NavItem[] = [];

  constructor(
    public translationService: TranslationService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    // Inicializa os valores
    this.currentLanguage = this.translationService.getCurrentLanguage();
    this.updateLanguageInfo();
    this.updateNavItems();

    // Inscreve-se para receber atualizações de idioma
    this.languageSubscription =
      this.translationService.currentLanguage$.subscribe(
        (language: Language) => {
          console.log('Header recebeu nova linguagem:', language);
          this.currentLanguage = language;
          this.updateLanguageInfo();
          this.updateNavItems();
          this.cdr.detectChanges(); // Força a detecção de mudanças
        }
      );
  }

  ngOnDestroy(): void {
    // Cancela a inscrição para evitar memory leaks
    if (this.languageSubscription) {
      this.languageSubscription.unsubscribe();
    }
  }

  /**
   * Atualiza as informações do idioma atual
   */
  private updateLanguageInfo(): void {
    console.log('Atualizando informações de idioma');
    this.nextLanguageName = this.translationService.getNextLanguageName();
    this.currentLanguageFlag = this.translationService.getCurrentLanguageFlag();
    console.log('Bandeira atual:', this.currentLanguageFlag);
    console.log('Próximo idioma:', this.nextLanguageName);
  }

  /**
   * Atualiza os itens de navegação com base no idioma atual
   */
  private updateNavItems(): void {
    this.navItems = [
      { path: '/', label: 'header.home', icon: HomeIcon },
      { path: '/dashboard', label: 'header.dashboard', icon: ChartBar },
      { path: '/api-keys', label: 'header.apiKeys', icon: KeyIcon },
      {
        path: '/subscriptions',
        label: 'header.subscriptions',
        icon: CreditCardIcon,
      },
      {
        path: '/documentation',
        label: 'header.documentation',
        icon: FileTextIcon,
      },
      { path: '/playground', label: 'header.playground', icon: TerminalIcon },
    ];
  }

  /**
   * Alterna entre os idiomas disponíveis
   */
  toggleLanguage(): void {
    console.log('Botão de alternar idioma clicado');
    console.log('Idioma atual antes da mudança:', this.currentLanguage);
    this.translationService.toggleLanguage();
    // Força a atualização da view após a mudança de idioma
    setTimeout(() => {
      this.cdr.detectChanges();
    });
  }

  /**
   * Obtém o texto para o tooltip do botão de idioma
   */
  getLanguageTooltip(): string {
    const nextLanguage = this.translationService.getNextLanguageOption();
    return `Mudar para ${nextLanguage.name} (${nextLanguage.flag})`;
  }
}
