import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { FooterSection } from '../../interfaces/footer.interface';

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './footer.component.html',
  styleUrl: './footer.component.css',
})
export class FooterComponent {
  footerSections: FooterSection[] = [
    {
      title: 'Links Rápidos',
      links: [
        { label: 'Documentação', url: '/documentation', isRouterLink: true },
        { label: 'Preços', url: '/subscriptions', isRouterLink: true },
        { label: 'API Status', url: '/status', isRouterLink: true },
      ],
    },
    {
      title: 'Recursos',
      links: [
        { label: 'Blog', url: '/blog', isRouterLink: true },
        { label: '<PERSON>toriais', url: '/tutorials', isRouterLink: true },
        { label: 'FAQ', url: '/faq', isRouterLink: true },
      ],
    },
    {
      title: 'Contato',
      links: [
        { label: 'Suporte', url: '/support', isRouterLink: true },
        { label: 'Vendas', url: '/sales', isRouterLink: true },
        { label: 'Sobre Nós', url: '/about', isRouterLink: true },
      ],
    },
  ];
}
