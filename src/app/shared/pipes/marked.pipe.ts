import { Pipe, PipeTransform } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { marked } from 'marked'; // Importar a biblioteca marked

@Pipe({
  name: 'marked',
  standalone: true,
})
export class MarkedPipe implements PipeTransform {
  constructor(private sanitizer: DomSanitizer) {}

  transform(value: string | null | undefined): SafeHtml {
    if (value === null || value === undefined || value.trim() === '') {
      return '';
    }
    // Converte Markdown para HTML usando a biblioteca marked
    const html = marked.parse(value) as string;
    // Sanitiza o HTML para prevenir XSS
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }
}
