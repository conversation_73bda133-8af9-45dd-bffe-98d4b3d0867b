import {
  ChangeDetectorRef,
  OnD<PERSON>roy,
  Pipe,
  PipeTransform,
} from '@angular/core';
import { Subscription } from 'rxjs';
import { I18nService, Language } from '../../core/services/i18n.service';
import { translations, TranslationSection } from '../i18n/translations';

@Pipe({
  name: 'translate',
  standalone: true,
  pure: false, // Necessário para detectar mudanças de idioma
})
export class TranslatePipe implements PipeTransform, OnDestroy {
  private currentLanguage: Language = 'pt';
  private languageSubscription?: Subscription;
  private translationCache = new Map<string, string>();

  constructor(
    private i18nService: I18nService,
    private cdr: ChangeDetectorRef
  ) {
    this.currentLanguage = this.i18nService.getCurrentLanguage();

    // Subscribe to language changes and clear cache
    this.languageSubscription = this.i18nService.currentLanguage$.subscribe(
      (language) => {
        if (this.currentLanguage !== language) {
          this.currentLanguage = language;
          this.translationCache.clear();
          this.cdr.detectChanges(); // Força a detecção de mudanças
        }
      }
    );
  }

  ngOnDestroy(): void {
    if (this.languageSubscription) {
      this.languageSubscription.unsubscribe();
    }
  }

  /**
   * Transforma uma chave de tradução em texto traduzido
   * @param key Chave de tradução no formato 'section.key'
   * @param params Parâmetros para substituição (opcional)
   * @returns Texto traduzido
   */
  transform(key: string, params?: Record<string, string | number>): string {
    if (!key) return '';

    // Create cache key including params
    const cacheKey = params
      ? `${key}_${this.currentLanguage}_${JSON.stringify(params)}`
      : `${key}_${this.currentLanguage}`;

    // Check cache first
    if (this.translationCache.has(cacheKey)) {
      return this.translationCache.get(cacheKey)!;
    }

    // Divide a chave em seção e chave
    const [section, ...keyParts] = key.split('.');
    const keyPath = keyParts.join('.');

    // Verifica se a seção existe
    if (!section || !(section in translations)) {
      console.warn(`Seção de tradução não encontrada: ${section}`);
      this.translationCache.set(cacheKey, key);
      return key;
    }

    // Obtém as traduções da seção
    const sectionTranslations = translations[section as TranslationSection];

    // Verifica se o idioma existe na seção
    if (!sectionTranslations[this.currentLanguage]) {
      console.warn(
        `Idioma não encontrado na seção: ${section}.${this.currentLanguage}`
      );
      this.translationCache.set(cacheKey, key);
      return key;
    }

    // Obtém as traduções do idioma atual
    const languageTranslations = sectionTranslations[this.currentLanguage];

    // Obtém o valor da tradução
    let translation = this.getNestedTranslation(languageTranslations, keyPath);

    // Se não encontrou a tradução, retorna a chave
    if (!translation) {
      console.warn(`Chave de tradução não encontrada: ${key}`);
      this.translationCache.set(cacheKey, key);
      return key;
    }

    // Substitui os parâmetros, se houver
    if (params) {
      Object.keys(params).forEach((param) => {
        translation = translation.replace(
          `{{${param}}}`,
          String(params[param])
        );
      });
    }

    // Cache the result
    this.translationCache.set(cacheKey, translation);
    return translation;
  }

  /**
   * Obtém uma tradução aninhada
   * @param obj Objeto de traduções
   * @param path Caminho da chave
   * @returns Valor da tradução
   */
  private getNestedTranslation(obj: any, path: string): string {
    const keys = path.split('.');
    let result = obj;

    for (const key of keys) {
      if (result && typeof result === 'object' && key in result) {
        result = result[key];
      } else {
        return '';
      }
    }

    return result;
  }
}
