import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { TranslationService } from './core/services/translation.service';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css',
})
export class AppComponent implements OnInit {
  title = 'currency-frontend';

  constructor(private translationService: TranslationService) {}

  ngOnInit(): void {
    // Inicializa o serviço de tradução
    // O serviço já é inicializado no constructor, mas garantimos que está funcionando
    this.translationService.getCurrentLanguage();
  }
}
