import { Injectable } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { BehaviorSubject, Observable } from 'rxjs';

export type Language = 'pt' | 'en';

export interface LanguageOption {
  code: Language;
  name: string;
  flag: string;
  direction?: 'ltr' | 'rtl';
}

@Injectable({
  providedIn: 'root',
})
export class I18nService {
  private readonly LANGUAGE_KEY = 'app_language';
  // Lista de idiomas suportados
  readonly supportedLanguages: LanguageOption[] = [
    { code: 'pt', name: 'Portuguê<PERSON>', flag: '🇧🇷', direction: 'ltr' },
    { code: 'en', name: 'English', flag: '🇺🇸', direction: 'ltr' },
  ];

  private currentLanguageSubject: BehaviorSubject<Language>;
  public currentLanguage$: Observable<Language>;

  constructor(private titleService: Title) {
    this.currentLanguageSubject = new BehaviorSubject<Language>(
      this.getInitialLanguage()
    );
    this.currentLanguage$ = this.currentLanguageSubject.asObservable();
    // Atualiza o atributo lang do HTML quando o idioma muda
    this.currentLanguage$.subscribe((lang) => {
      document.documentElement.lang = lang;
      // Define a direção do texto (LTR ou RTL)
      const langOption = this.getLanguageOption(lang);
      if (langOption && langOption.direction) {
        document.documentElement.dir = langOption.direction;
      }
    });
  }

  /**
   * Obtém o idioma inicial com base na preferência do usuário ou no idioma do navegador
   */
  private getInitialLanguage(): Language {
    // Verifica se há um idioma salvo no localStorage
    const savedLanguage = localStorage.getItem(this.LANGUAGE_KEY) as Language;
    if (savedLanguage && this.isLanguageSupported(savedLanguage)) {
      return savedLanguage;
    }

    // Verifica o idioma do navegador
    const browserLanguage = navigator.language.split('-')[0] as Language;
    return this.isLanguageSupported(browserLanguage) ? browserLanguage : 'en';
  }

  /**
   * Verifica se um idioma é suportado
   */
  private isLanguageSupported(language: string): boolean {
    return this.supportedLanguages.some((lang) => lang.code === language);
  }

  /**
   * Obtém o idioma atual
   */
  getCurrentLanguage(): Language {
    return this.currentLanguageSubject.value;
  }

  /**
   * Define o idioma atual
   * @param language Idioma a ser definido
   */
  setLanguage(language: Language): void {
    console.log('setLanguage chamado com:', language);
    console.log('Idioma atual antes da mudança:', this.getCurrentLanguage());

    if (!this.isLanguageSupported(language)) {
      console.warn(`Idioma não suportado: ${language}`);
      return;
    }

    localStorage.setItem(this.LANGUAGE_KEY, language);
    this.currentLanguageSubject.next(language);

    console.log('Idioma definido para:', language);
    console.log('BehaviorSubject value:', this.currentLanguageSubject.value);

    // Emite um evento personalizado para notificar outros componentes
    const event = new CustomEvent('languageChanged', { detail: { language } });
    window.dispatchEvent(event);
  }

  /**
   * Alterna entre os idiomas disponíveis
   */
  toggleLanguage(): void {
    console.log('toggleLanguage chamado');
    const currentLanguage = this.getCurrentLanguage();
    console.log('Idioma atual:', currentLanguage);

    const currentIndex = this.supportedLanguages.findIndex(
      (lang) => lang.code === currentLanguage
    );
    console.log('Índice atual:', currentIndex);

    // Se o idioma atual não for encontrado, usa o primeiro idioma da lista
    if (currentIndex === -1) {
      console.log('Idioma atual não encontrado, usando o primeiro da lista');
      this.setLanguage(this.supportedLanguages[0].code);
      return;
    }

    // Alterna para o próximo idioma na lista (ou volta para o primeiro)
    const nextIndex = (currentIndex + 1) % this.supportedLanguages.length;
    const nextLanguage = this.supportedLanguages[nextIndex].code;
    console.log('Próximo idioma:', nextLanguage);
    this.setLanguage(nextLanguage);
  }

  /**
   * Obtém informações sobre o idioma atual
   */
  getLanguageOption(langCode?: Language): LanguageOption | undefined {
    const code = langCode || this.getCurrentLanguage();
    return this.supportedLanguages.find((lang) => lang.code === code);
  }

  /**
   * Obtém o nome do idioma atual
   */
  getCurrentLanguageName(): string {
    const langOption = this.getLanguageOption();
    return langOption ? langOption.name : '';
  }

  /**
   * Obtém a bandeira do idioma atual
   */
  getCurrentLanguageFlag(): string {
    const langOption = this.getLanguageOption();
    return langOption ? langOption.flag : '';
  }

  /**
   * Obtém o próximo idioma na lista
   */
  getNextLanguageOption(): LanguageOption {
    const currentLanguage = this.getCurrentLanguage();
    const currentIndex = this.supportedLanguages.findIndex(
      (lang) => lang.code === currentLanguage
    );

    // Se o idioma atual não for encontrado, retorna o primeiro idioma da lista
    if (currentIndex === -1) {
      return this.supportedLanguages[0];
    }

    // Retorna o próximo idioma na lista (ou volta para o primeiro)
    const nextIndex = (currentIndex + 1) % this.supportedLanguages.length;
    return this.supportedLanguages[nextIndex];
  }

  /**
   * Obtém o nome do próximo idioma (para o botão de alternância)
   */
  getNextLanguageName(): string {
    return this.getNextLanguageOption().name;
  }

  /**
   * Obtém a bandeira do próximo idioma (para o botão de alternância)
   */
  getNextLanguageFlag(): string {
    return this.getNextLanguageOption().flag;
  }

  /**
   * Atualiza o título da página com tradução
   * @param title Título a ser traduzido
   */
  setPageTitle(title: string): void {
    // Por enquanto, apenas define o título diretamente
    this.titleService.setTitle(title);
  }
}
