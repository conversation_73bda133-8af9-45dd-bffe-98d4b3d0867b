import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import Swal, {
  SweetAlertIcon,
  SweetAlertPosition,
  SweetAlertResult,
} from 'sweetalert2';

export interface Notification {
  type: 'success' | 'error' | 'info' | 'warning';
  message: string;
  title?: string;
  duration?: number;
  position?: SweetAlertPosition;
  showConfirmButton?: boolean;
  confirmButtonText?: string;
  showCancelButton?: boolean;
  cancelButtonText?: string;
  html?: string;
}

export interface ConfirmDialogOptions {
  title?: string;
  text: string;
  icon?: SweetAlertIcon;
  confirmButtonText?: string;
  cancelButtonText?: string;
  showCancelButton?: boolean;
  focusCancel?: boolean;
}

@Injectable({
  providedIn: 'root',
})
export class NotificationService {
  // Mantemos o BehaviorSubject para compatibilidade com o componente existente
  private notificationSubject = new BehaviorSubject<Notification | null>(null);
  public notification$ = this.notificationSubject.asObservable();

  private defaultDuration = 5000; // 5 segundos
  private defaultPosition: SweetAlertPosition = 'top-end';

  /**
   * Exibe uma notificação de sucesso
   * @param message Mensagem a ser exibida
   * @param title Título opcional
   * @param duration Duração em milissegundos (padrão: 5000ms)
   */
  success(
    message: string,
    title: string = 'Sucesso!',
    duration: number = this.defaultDuration
  ): void {
    this.showToast({
      type: 'success',
      message,
      title,
      duration,
      position: this.defaultPosition,
      showConfirmButton: false,
    });
  }

  /**
   * Exibe uma notificação de erro
   * @param message Mensagem a ser exibida
   * @param title Título opcional
   * @param duration Duração em milissegundos (padrão: 5000ms)
   */
  error(
    message: string,
    title: string = 'Erro!',
    duration: number = this.defaultDuration
  ): void {
    this.showToast({
      type: 'error',
      message,
      title,
      duration,
      position: this.defaultPosition,
      showConfirmButton: false,
    });
  }

  /**
   * Exibe uma notificação informativa
   * @param message Mensagem a ser exibida
   * @param title Título opcional
   * @param duration Duração em milissegundos (padrão: 5000ms)
   */
  info(
    message: string,
    title: string = 'Informação',
    duration: number = this.defaultDuration
  ): void {
    this.showToast({
      type: 'info',
      message,
      title,
      duration,
      position: this.defaultPosition,
      showConfirmButton: false,
    });
  }

  /**
   * Exibe uma notificação de aviso
   * @param message Mensagem a ser exibida
   * @param title Título opcional
   * @param duration Duração em milissegundos (padrão: 5000ms)
   */
  warning(
    message: string,
    title: string = 'Atenção!',
    duration: number = this.defaultDuration
  ): void {
    this.showToast({
      type: 'warning',
      message,
      title,
      duration,
      position: this.defaultPosition,
      showConfirmButton: false,
    });
  }

  /**
   * Exibe um diálogo de confirmação
   * @param options Opções do diálogo
   * @returns Promise com o resultado da confirmação
   */
  async confirm(options: ConfirmDialogOptions): Promise<SweetAlertResult<any>> {
    return Swal.fire({
      title: options.title || 'Você tem certeza?',
      text: options.text,
      icon: options.icon || 'question',
      showCancelButton:
        options.showCancelButton !== undefined
          ? options.showCancelButton
          : true,
      confirmButtonText: options.confirmButtonText || 'Sim',
      cancelButtonText: options.cancelButtonText || 'Cancelar',
      focusCancel: options.focusCancel || false,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
    });
  }

  /**
   * Exibe um toast usando SweetAlert2
   * @param notification Configuração da notificação
   */
  private showToast(notification: Notification): void {
    // Mantemos a compatibilidade com o componente existente
    this.notificationSubject.next(notification);

    // Exibimos o toast usando SweetAlert2
    const toast = Swal.mixin({
      toast: true,
      position: notification.position || 'top-end',
      showConfirmButton: notification.showConfirmButton || false,
      timer: notification.duration,
      timerProgressBar: true,
      didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer);
        toast.addEventListener('mouseleave', Swal.resumeTimer);
      },
    });

    toast.fire({
      icon: notification.type as SweetAlertIcon,
      title: notification.title,
      text: notification.message,
      html: notification.html,
    });
  }

  /**
   * Limpa a notificação atual
   */
  clear(): void {
    this.notificationSubject.next(null);
    Swal.close();
  }
}
