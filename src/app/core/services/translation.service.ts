import { Injectable } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Observable } from 'rxjs';

export type Language = 'pt' | 'en';

export interface LanguageOption {
  code: Language;
  name: string;
  flag: string;
  direction?: 'ltr' | 'rtl';
}

@Injectable({
  providedIn: 'root',
})
export class TranslationService {
  private readonly LANGUAGE_KEY = 'app_language';
  private currentLanguageSubject = new BehaviorSubject<Language>(
    this.getInitialLanguage()
  );
  public currentLanguage$ = this.currentLanguageSubject.asObservable();

  // Lista de idiomas suportados
  readonly supportedLanguages: LanguageOption[] = [
    { code: 'pt', name: 'Português', flag: '🇧🇷', direction: 'ltr' },
    { code: 'en', name: 'English', flag: '🇺🇸', direction: 'ltr' },
  ];

  constructor(
    private translate: TranslateService,
    private titleService: Title
  ) {
    this.initializeTranslationService();
  }

  /**
   * Inicializa o serviço de tradução
   */
  private initializeTranslationService(): void {
    // Aguarda o TranslateService estar pronto
    setTimeout(() => {
      try {
        // Configura idiomas suportados
        this.translate.addLangs(['pt', 'en']);

        // Define idioma padrão
        this.translate.setDefaultLang('en');

        // Define idioma inicial
        const initialLanguage = this.getInitialLanguage();
        this.setLanguage(initialLanguage);

        // Atualiza o atributo lang do HTML quando o idioma muda
        this.currentLanguage$.subscribe((lang) => {
          document.documentElement.lang = lang;

          // Define a direção do texto (LTR ou RTL)
          const langOption = this.getLanguageOption(lang);
          if (langOption && langOption.direction) {
            document.documentElement.dir = langOption.direction;
          }
        });
      } catch (error) {
        console.warn('Erro ao inicializar TranslationService:', error);
        // Define um idioma padrão em caso de erro
        this.currentLanguageSubject.next('en');
      }
    }, 100);
  }

  /**
   * Obtém o idioma inicial com base na preferência do usuário ou no idioma do navegador
   */
  private getInitialLanguage(): Language {
    // Verifica se há um idioma salvo no localStorage
    const savedLanguage = localStorage.getItem(this.LANGUAGE_KEY) as Language;
    if (savedLanguage && this.isLanguageSupported(savedLanguage)) {
      return savedLanguage;
    }

    // Verifica o idioma do navegador
    const browserLanguage = navigator.language.split('-')[0] as Language;
    return this.isLanguageSupported(browserLanguage) ? browserLanguage : 'en';
  }

  /**
   * Verifica se um idioma é suportado
   */
  private isLanguageSupported(language: string): boolean {
    return this.supportedLanguages.some((lang) => lang.code === language);
  }

  /**
   * Obtém o idioma atual
   */
  getCurrentLanguage(): Language {
    return this.currentLanguageSubject.value;
  }

  /**
   * Define o idioma atual
   * @param language Idioma a ser definido
   */
  setLanguage(language: Language): void {
    if (!this.isLanguageSupported(language)) {
      console.warn(`Idioma não suportado: ${language}`);
      return;
    }

    // Verifica se o TranslateService está disponível
    if (!this.translate) {
      console.warn('TranslateService não está disponível');
      this.currentLanguageSubject.next(language);
      return;
    }

    // Usa o ngx-translate para mudar o idioma
    this.translate.use(language).subscribe({
      next: () => {
        localStorage.setItem(this.LANGUAGE_KEY, language);
        this.currentLanguageSubject.next(language);

        // Emite um evento personalizado para notificar outros componentes
        const event = new CustomEvent('languageChanged', {
          detail: { language },
        });
        window.dispatchEvent(event);
      },
      error: (error) => {
        console.warn('Erro ao mudar idioma:', error);
        // Mesmo com erro, atualiza o estado local
        localStorage.setItem(this.LANGUAGE_KEY, language);
        this.currentLanguageSubject.next(language);
      },
    });
  }

  /**
   * Alterna entre os idiomas disponíveis
   */
  toggleLanguage(): void {
    const currentLanguage = this.getCurrentLanguage();
    const currentIndex = this.supportedLanguages.findIndex(
      (lang) => lang.code === currentLanguage
    );

    // Se o idioma atual não for encontrado, usa o primeiro idioma da lista
    if (currentIndex === -1) {
      this.setLanguage(this.supportedLanguages[0].code);
      return;
    }

    // Alterna para o próximo idioma na lista (ou volta para o primeiro)
    const nextIndex = (currentIndex + 1) % this.supportedLanguages.length;
    this.setLanguage(this.supportedLanguages[nextIndex].code);
  }

  /**
   * Obtém informações sobre o idioma atual
   */
  getLanguageOption(langCode?: Language): LanguageOption | undefined {
    const code = langCode || this.getCurrentLanguage();
    return this.supportedLanguages.find((lang) => lang.code === code);
  }

  /**
   * Obtém o nome do idioma atual
   */
  getCurrentLanguageName(): string {
    const langOption = this.getLanguageOption();
    return langOption ? langOption.name : '';
  }

  /**
   * Obtém a bandeira do idioma atual
   */
  getCurrentLanguageFlag(): string {
    const langOption = this.getLanguageOption();
    return langOption ? langOption.flag : '';
  }

  /**
   * Obtém o próximo idioma na lista
   */
  getNextLanguageOption(): LanguageOption {
    const currentLanguage = this.getCurrentLanguage();
    const currentIndex = this.supportedLanguages.findIndex(
      (lang) => lang.code === currentLanguage
    );

    // Se o idioma atual não for encontrado, retorna o primeiro idioma da lista
    if (currentIndex === -1) {
      return this.supportedLanguages[0];
    }

    // Retorna o próximo idioma na lista (ou volta para o primeiro)
    const nextIndex = (currentIndex + 1) % this.supportedLanguages.length;
    return this.supportedLanguages[nextIndex];
  }

  /**
   * Obtém o nome do próximo idioma (para o botão de alternância)
   */
  getNextLanguageName(): string {
    return this.getNextLanguageOption().name;
  }

  /**
   * Obtém a bandeira do próximo idioma (para o botão de alternância)
   */
  getNextLanguageFlag(): string {
    return this.getNextLanguageOption().flag;
  }

  /**
   * Traduz uma chave usando ngx-translate
   * @param key Chave de tradução
   * @param params Parâmetros para interpolação
   * @returns Observable com a tradução
   */
  get(key: string, params?: any): Observable<string> {
    if (!this.translate) {
      console.warn('TranslateService não está disponível para get()');
      return new Observable((observer) => {
        observer.next(key);
        observer.complete();
      });
    }
    return this.translate.get(key, params);
  }

  /**
   * Traduz uma chave instantaneamente (síncrono)
   * @param key Chave de tradução
   * @param params Parâmetros para interpolação
   * @returns Tradução ou chave se não encontrada
   */
  instant(key: string, params?: any): string {
    if (!this.translate) {
      console.warn('TranslateService não está disponível para instant()');
      return key;
    }
    return this.translate.instant(key, params);
  }

  /**
   * Atualiza o título da página com tradução
   * @param titleKey Chave de tradução do título
   * @param params Parâmetros para interpolação
   */
  setPageTitle(titleKey: string, params?: any): void {
    this.get(titleKey, params).subscribe((title) => {
      this.titleService.setTitle(title);
    });
  }

  /**
   * Recarrega as traduções do idioma atual
   */
  reloadLang(): void {
    const currentLang = this.getCurrentLanguage();
    this.translate.reloadLang(currentLang);
  }
}
