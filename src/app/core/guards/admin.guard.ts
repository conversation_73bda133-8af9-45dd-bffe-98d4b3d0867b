import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivate,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { AuthService } from '../auth/auth.service';

@Injectable({
  providedIn: 'root',
})
export class AdminGuard implements CanActivate {
  constructor(private authService: AuthService, private router: Router) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    // Durante o desenvolvimento, permitir acesso a todas as rotas administrativas
    // IMPORTANTE: Remover esta linha antes de ir para produção
    return true;

    /* Código original comentado para desenvolvimento
    // Verifica se o usuário está autenticado e tem papel de admin
    if (this.authService.isAuthenticated() && this.authService.isAdmin()) {
      return true;
    }

    // Se o usuário está autenticado mas não é admin, redireciona para o dashboard
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/dashboard']);
      return false;
    }

    // Se o usuário não está autenticado, redireciona para o login
    this.router.navigate(['/auth/login'], {
      queryParams: { returnUrl: state.url },
    });
    return false;
    */
  }
}
