import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { RouterLink, RouterOutlet } from '@angular/router';
import {
  HomeIcon,
  LogOutIcon,
  LucideAngularModule,
  PackageIcon,
  SettingsIcon,
  UsersIcon,
  ChartBar,
} from 'lucide-angular';

@Component({
  selector: 'app-admin-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet, RouterLink, LucideAngularModule],
  template: `
    <div class="min-h-screen flex">
      <!-- Sidebar -->
      <aside class="w-64 bg-gray-900 text-white">
        <div class="p-4 border-b border-gray-800">
          <h1 class="text-xl font-bold">CurrencyWise Admin</h1>
        </div>
        <nav class="p-4">
          <ul class="space-y-2">
            <li>
              <a
                routerLink="/admin"
                class="flex items-center gap-2 p-2 rounded hover:bg-gray-800"
              >
                <i-lucide [img]="BarChartIcon" class="w-5 h-5"></i-lucide>
                Dashboard
              </a>
            </li>
            <li>
              <a
                routerLink="/admin/users"
                class="flex items-center gap-2 p-2 rounded hover:bg-gray-800"
              >
                <i-lucide [img]="UsersIcon" class="w-5 h-5"></i-lucide>
                Usuários
              </a>
            </li>
            <li>
              <a
                routerLink="/admin/plans"
                class="flex items-center gap-2 p-2 rounded hover:bg-gray-800"
              >
                <i-lucide [img]="PackageIcon" class="w-5 h-5"></i-lucide>
                Planos
              </a>
            </li>
            <li>
              <a
                routerLink="/admin/settings"
                class="flex items-center gap-2 p-2 rounded hover:bg-gray-800"
              >
                <i-lucide [img]="SettingsIcon" class="w-5 h-5"></i-lucide>
                Configurações
              </a>
            </li>
            <li class="pt-4 mt-4 border-t border-gray-800">
              <a
                routerLink="/"
                class="flex items-center gap-2 p-2 rounded hover:bg-gray-800"
              >
                <i-lucide [img]="HomeIcon" class="w-5 h-5"></i-lucide>
                Voltar ao Site
              </a>
            </li>
            <li>
              <a
                href="#"
                class="flex items-center gap-2 p-2 rounded hover:bg-gray-800 text-red-400"
              >
                <i-lucide [img]="LogOutIcon" class="w-5 h-5"></i-lucide>
                Sair
              </a>
            </li>
          </ul>
        </nav>
      </aside>

      <!-- Main Content -->
      <main class="flex-grow bg-gray-100">
        <header class="bg-white shadow-sm p-4">
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold">Painel Administrativo</h2>
            <div class="flex items-center gap-4">
              <span class="text-gray-600">Admin</span>
            </div>
          </div>
        </header>
        <div class="p-6">
          <router-outlet></router-outlet>
        </div>
      </main>
    </div>
  `,
  styles: [],
})
export class AdminLayoutComponent {
  readonly HomeIcon = HomeIcon;
  readonly BarChartIcon = ChartBar;
  readonly UsersIcon = UsersIcon;
  readonly PackageIcon = PackageIcon;
  readonly SettingsIcon = SettingsIcon;
  readonly LogOutIcon = LogOutIcon;
}
