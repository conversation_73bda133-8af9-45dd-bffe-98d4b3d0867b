import { Routes } from '@angular/router';
import { AdminGuard } from '../../core/guards/admin.guard';
import { AdminLayoutComponent } from './admin-layout.component';

export const ADMIN_ROUTES: Routes = [
  {
    path: 'admin',
    component: AdminLayoutComponent,
    canActivate: [AdminGuard], // Protege todas as rotas administrativas
    children: [
      {
        path: '',
        loadComponent: () =>
          import('../../features/admin').then((m) => m.AdminDashboardComponent),
        title: 'Admin Dashboard - CurrencyWise',
      },
      {
        path: 'users',
        loadComponent: () =>
          import('../../features/admin').then((m) => m.UsersComponent),
        title: 'Gerenciar Usuários - CurrencyWise',
      },
      {
        path: 'plans',
        loadComponent: () =>
          import('../../features/admin').then((m) => m.PlansComponent),
        title: 'Gerenciar Planos - CurrencyWise',
      },
    ],
  },
];
