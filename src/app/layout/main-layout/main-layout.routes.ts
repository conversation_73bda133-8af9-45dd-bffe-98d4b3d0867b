import { Routes } from '@angular/router';
import { AuthGuard } from '../../core/guards/auth.guard';
import { MainLayoutComponent } from './main-layout.component';

export const MAIN_ROUTES: Routes = [
  {
    path: '',
    component: MainLayoutComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('../../features/home').then((m) => m.HomeComponent),
        title: 'Início - CurrencyWise',
      },
      {
        path: 'dashboard',
        loadComponent: () =>
          import('../../features/dashboard').then((m) => m.DashboardComponent),
        title: 'Dashboard - CurrencyWise',
        canActivate: [AuthGuard],
      },
      {
        path: 'api-keys',
        loadComponent: () =>
          import('../../features/api-keys').then((m) => m.ApiKeysComponent),
        title: 'API Keys - CurrencyWise',
        canActivate: [AuthGuard],
      },
      {
        path: 'subscriptions',
        loadComponent: () =>
          import('../../features/subscriptions').then(
            (m) => m.SubscriptionsComponent
          ),
        title: 'Assinaturas - CurrencyWise',
        canActivate: [AuthGuard],
      },
      {
        path: 'documentation',
        loadComponent: () =>
          import('../../features/documentation').then(
            (m) => m.DocumentationComponent
          ),
        title: 'Documentação - CurrencyWise',
        // Documentação completa requer autenticação, mas podemos ter uma versão pública
        // com acesso limitado. Por enquanto, vamos proteger toda a documentação.
        canActivate: [AuthGuard],
      },
      {
        path: 'playground',
        loadComponent: () =>
          import('../../features/playground').then(
            (m) => m.PlaygroundComponent
          ),
        title: 'Playground - CurrencyWise',
        // Playground completo requer autenticação, mas podemos ter uma versão pública
        // com acesso limitado. Por enquanto, vamos proteger todo o playground.
        canActivate: [AuthGuard],
      },
      {
        path: 'notification-demo',
        loadComponent: () =>
          import('../../features/notification-demo').then(
            (m) => m.NotificationDemoComponent
          ),
        title: 'Demonstração de Notificações - CurrencyWise',
      },
      {
        path: 'faq',
        loadComponent: () =>
          import('../../features/faq').then((m) => m.FaqComponent),
        title: 'Perguntas Frequentes - CurrencyWise',
      },
    ],
  },
];
