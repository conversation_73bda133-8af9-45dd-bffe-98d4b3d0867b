import { Routes } from '@angular/router';
import { AuthLayoutComponent } from './auth-layout.component';

export const AUTH_ROUTES: Routes = [
  {
    path: 'auth',
    component: AuthLayoutComponent,
    children: [
      {
        path: 'login',
        loadComponent: () =>
          import('../../features/auth/login').then((m) => m.LoginComponent),
        title: 'Login - CurrencyWise',
      },
      {
        path: 'register',
        loadComponent: () =>
          import('../../features/auth/register').then(
            (m) => m.RegisterComponent
          ),
        title: 'Registro - CurrencyWise',
      },
      {
        path: 'forgot-password',
        loadComponent: () =>
          import('../../features/auth/forgot-password').then(
            (m) => m.ForgotPasswordComponent
          ),
        title: 'Recuperar Senha - CurrencyWise',
      },
      {
        path: 'verify-email',
        loadComponent: () =>
          import('../../features/auth').then((m) => m.VerifyEmailComponent),
        title: 'Verificar Email - CurrencyWise',
      },
      {
        path: 'terms',
        loadComponent: () =>
          import('../../features/auth').then((m) => m.TermsComponent),
        title: 'Termos de Serviço - CurrencyWise',
      },
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full',
      },
    ],
  },
];
