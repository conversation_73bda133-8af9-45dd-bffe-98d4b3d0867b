import { provideHttpClient, withInterceptors } from '@angular/common/http';
import {
  APP_INITIALIZER,
  ApplicationConfig,
  provideZoneChangeDetection,
} from '@angular/core';
import { Title } from '@angular/platform-browser';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter } from '@angular/router';

import { routes } from './app.routes';
import { I18nService } from './core/services/i18n.service';

// Função para inicializar o serviço de internacionalização
function initializeI18nService(i18nService: I18nService) {
  return () => {
    // Carrega o idioma inicial
    const currentLanguage = i18nService.getCurrentLanguage();
    document.documentElement.lang = currentLanguage;

    // Define a direção do texto (LTR ou RTL)
    const langOption = i18nService.getLanguageOption(currentLanguage);
    if (langOption && langOption.direction) {
      document.documentElement.dir = langOption.direction;
    }

    return Promise.resolve();
  };
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideHttpClient(withInterceptors([])),
    provideAnimations(),
    // Provedor para o serviço de título
    Title,
    // Inicializa o serviço de internacionalização
    {
      provide: APP_INITIALIZER,
      useFactory: initializeI18nService,
      deps: [I18nService],
      multi: true,
    },
  ],
};
