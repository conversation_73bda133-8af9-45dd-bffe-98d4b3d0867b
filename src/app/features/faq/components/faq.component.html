<div class="container-padded py-12">
  <div class="text-center mb-12">
    <h1 class="page-title">Perguntas Frequentes</h1>
    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
      Encontre respostas para as perguntas mais comuns sobre a CurrencyWise API
    </p>
  </div>

  <!-- Barr<PERSON> de pesquisa -->
  <div class="max-w-3xl mx-auto mb-12">
    <div class="relative">
      <input
        type="text"
        [(ngModel)]="searchTerm"
        placeholder="Buscar perguntas..."
        class="w-full p-4 pl-12 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      />
      <i-lucide
        [img]="SearchIcon"
        class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
      ></i-lucide>
      @if (searchTerm) {
      <button
        (click)="clearSearch()"
        class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
      >
        <span class="sr-only">Limpar</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
            clip-rule="evenodd"
          />
        </svg>
      </button>
      }
    </div>
  </div>

  <!-- Categorias -->
  <div class="max-w-5xl mx-auto mb-12">
    <div class="flex flex-wrap justify-center gap-4">
      @for (category of categories; track category.name) {
      <button
        (click)="selectCategory(category.name)"
        class="px-4 py-2 rounded-full text-sm font-medium transition-colors"
        [class.bg-blue-100]="selectedCategory === category.name"
        [class.text-blue-800]="selectedCategory === category.name"
        [class.bg-gray-100]="selectedCategory !== category.name"
        [class.text-gray-700]="selectedCategory !== category.name"
        [class.hover:bg-gray-200]="selectedCategory !== category.name"
      >
        <i-lucide
          [img]="category.icon"
          class="inline-block w-4 h-4 mr-1 align-text-bottom"
        ></i-lucide>
        {{ category.name === "all" ? "Todas" : (category.name | titlecase) }}
      </button>
      }
    </div>
  </div>

  <!-- Lista de FAQs -->
  <div class="max-w-3xl mx-auto">
    @if (filteredFaqItems.length === 0) {
    <div class="text-center py-12">
      <i-lucide
        [img]="HelpCircleIcon"
        class="w-16 h-16 text-gray-300 mx-auto mb-4"
      ></i-lucide>
      <h3 class="text-xl font-medium text-gray-700 mb-2">
        Nenhuma pergunta encontrada
      </h3>
      <p class="text-gray-500">
        Tente ajustar sua busca ou selecionar outra categoria
      </p>
    </div>
    } @else {
    <div class="space-y-4">
      @for (item of filteredFaqItems; track item.question) {
      <div class="border border-gray-200 rounded-lg overflow-hidden">
        <button
          (click)="toggleExpand(item)"
          class="w-full flex justify-between items-center p-4 text-left bg-white hover:bg-gray-50 transition-colors"
        >
          <span class="font-medium text-gray-900">{{ item.question }}</span>
          <i-lucide
            [img]="item.isExpanded ? ChevronUpIcon : ChevronDownIcon"
            class="flex-shrink-0 ml-2 text-gray-500 w-5 h-5"
          ></i-lucide>
        </button>
        @if (item.isExpanded) {
        <div class="p-4 bg-gray-50 border-t border-gray-200">
          <p class="text-gray-700">{{ item.answer }}</p>
        </div>
        }
      </div>
      }
    </div>
    }
  </div>

  <!-- Seção "Não encontrou o que procurava?" -->
  <div
    class="max-w-3xl mx-auto mt-16 p-8 bg-blue-50 rounded-lg border-l-4 border-blue-500"
  >
    <h2 class="text-xl font-semibold text-blue-800 mb-4">
      Não encontrou o que procurava?
    </h2>
    <p class="text-blue-700 mb-6">
      Nossa equipe de suporte está pronta para ajudar com qualquer dúvida que
      você possa ter sobre a CurrencyWise API.
    </p>
    <a
      routerLink="/support"
      class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
    >
      <i-lucide [img]="MessageCircleIcon" class="mr-2 w-5 h-5"></i-lucide>
      Entrar em contato com o suporte
    </a>
  </div>
</div>
