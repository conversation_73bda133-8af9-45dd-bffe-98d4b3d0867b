import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { 
  ChevronDownIcon, 
  ChevronUpIcon, 
  LucideAngularModule, 
  SearchIcon,
  HelpCircleIcon,
  MessageCircleIcon
} from 'lucide-angular';

interface FaqItem {
  question: string;
  answer: string;
  category: string;
  isExpanded: boolean;
}

interface FaqCategory {
  name: string;
  icon: any;
  description: string;
}

@Component({
  selector: 'app-faq',
  standalone: true,
  imports: [CommonModule, FormsModule, LucideAngularModule],
  templateUrl: './faq.component.html',
  styleUrls: ['./faq.component.css']
})
export class FaqComponent {
  readonly ChevronDownIcon = ChevronDownIcon;
  readonly ChevronUpIcon = ChevronUpIcon;
  readonly SearchIcon = SearchIcon;
  readonly HelpCircleIcon = HelpCircleIcon;
  readonly MessageCircleIcon = MessageCircleIcon;

  searchTerm: string = '';
  selectedCategory: string = 'all';

  categories: FaqCategory[] = [
    { 
      name: 'all', 
      icon: HelpCircleIcon, 
      description: 'Todas as perguntas' 
    },
    { 
      name: 'general', 
      icon: HelpCircleIcon, 
      description: 'Perguntas gerais sobre a API' 
    },
    { 
      name: 'pricing', 
      icon: HelpCircleIcon, 
      description: 'Perguntas sobre preços e planos' 
    },
    { 
      name: 'technical', 
      icon: HelpCircleIcon, 
      description: 'Perguntas técnicas sobre a API' 
    },
    { 
      name: 'account', 
      icon: HelpCircleIcon, 
      description: 'Perguntas sobre contas e autenticação' 
    },
  ];

  faqItems: FaqItem[] = [
    {
      question: 'O que é a CurrencyWise API?',
      answer: 'A CurrencyWise API é um serviço que fornece taxas de câmbio em tempo real e histórico para mais de 170 moedas. Nossa API é projetada para ser simples, rápida e confiável, permitindo que desenvolvedores integrem facilmente conversão de moedas em seus aplicativos e sites.',
      category: 'general',
      isExpanded: false
    },
    {
      question: 'Como posso começar a usar a API?',
      answer: 'Para começar a usar a API CurrencyWise, basta criar uma conta gratuita, obter sua chave API e começar a fazer requisições. Consulte nossa documentação para exemplos de código e guias de início rápido.',
      category: 'general',
      isExpanded: false
    },
    {
      question: 'Quais são os planos disponíveis?',
      answer: 'Oferecemos três planos: Básico (gratuito), Profissional (R$49/mês) e Empresarial (R$199/mês). Cada plano oferece diferentes limites de requisições, frequência de atualização de taxas e acesso a dados históricos. Visite nossa página de preços para mais detalhes.',
      category: 'pricing',
      isExpanded: false
    },
    {
      question: 'Posso mudar de plano a qualquer momento?',
      answer: 'Sim, você pode fazer upgrade ou downgrade do seu plano a qualquer momento. As mudanças entram em vigor imediatamente para upgrades e no final do período de cobrança atual para downgrades.',
      category: 'pricing',
      isExpanded: false
    },
    {
      question: 'Como são calculadas as taxas de câmbio?',
      answer: 'Nossas taxas de câmbio são obtidas de várias fontes financeiras confiáveis, incluindo bancos centrais e provedores de dados financeiros. Agregamos e validamos esses dados para garantir a precisão das taxas fornecidas.',
      category: 'technical',
      isExpanded: false
    },
    {
      question: 'Com que frequência as taxas são atualizadas?',
      answer: 'A frequência de atualização depende do seu plano. No plano Básico, as taxas são atualizadas a cada hora. No plano Profissional, a cada 10 minutos. No plano Empresarial, as taxas são atualizadas em tempo real.',
      category: 'technical',
      isExpanded: false
    },
    {
      question: 'Quais formatos de resposta são suportados?',
      answer: 'Todas as respostas da API são retornadas no formato JSON, que é facilmente processável por qualquer linguagem de programação moderna.',
      category: 'technical',
      isExpanded: false
    },
    {
      question: 'Como faço para obter minha chave API?',
      answer: 'Após criar uma conta, você pode gerar e gerenciar suas chaves API na seção "API Keys" do seu painel. Cada chave tem permissões e limites específicos que você pode configurar.',
      category: 'account',
      isExpanded: false
    },
    {
      question: 'Posso ter múltiplas chaves API?',
      answer: 'Sim, você pode criar múltiplas chaves API para diferentes aplicações ou ambientes (desenvolvimento, teste, produção). Isso ajuda a manter a segurança e a rastreabilidade do uso da API.',
      category: 'account',
      isExpanded: false
    },
    {
      question: 'O que acontece se eu exceder meu limite de requisições?',
      answer: 'Se você exceder o limite de requisições do seu plano, receberá um erro 429 (Too Many Requests). Recomendamos monitorar seu uso e fazer upgrade para um plano superior se necessário.',
      category: 'technical',
      isExpanded: false
    },
    {
      question: 'Vocês oferecem SLA?',
      answer: 'Sim, oferecemos SLA (Service Level Agreement) para clientes do plano Empresarial, garantindo 99,9% de uptime e suporte prioritário 24/7.',
      category: 'pricing',
      isExpanded: false
    },
    {
      question: 'Como posso entrar em contato com o suporte?',
      answer: 'Você pode entrar em contato com nossa equipe de suporte através do formulário de contato em nossa página de suporte, por <NAME_EMAIL>, ou pelo chat ao vivo disponível para clientes dos planos Profissional e Empresarial.',
      category: 'general',
      isExpanded: false
    }
  ];

  get filteredFaqItems(): FaqItem[] {
    return this.faqItems.filter(item => {
      // Filtrar por categoria
      const categoryMatch = this.selectedCategory === 'all' || item.category === this.selectedCategory;
      
      // Filtrar por termo de busca
      const searchMatch = this.searchTerm === '' || 
        item.question.toLowerCase().includes(this.searchTerm.toLowerCase()) || 
        item.answer.toLowerCase().includes(this.searchTerm.toLowerCase());
      
      return categoryMatch && searchMatch;
    });
  }

  toggleExpand(item: FaqItem): void {
    item.isExpanded = !item.isExpanded;
  }

  selectCategory(category: string): void {
    this.selectedCategory = category;
  }

  clearSearch(): void {
    this.searchTerm = '';
  }
}
