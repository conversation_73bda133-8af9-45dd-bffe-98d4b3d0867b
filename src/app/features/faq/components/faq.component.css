/* Estilos específicos para o componente FAQ */

/* Animação para expandir/colapsar */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Aplicar animação aos itens expandidos */
div[class*="border-t"] {
  animation: slideDown 0.2s ease-out;
}

/* Estilo para botões de categoria */
button[class*="rounded-full"] {
  transition: all 0.2s ease;
}

button[class*="rounded-full"]:hover {
  transform: translateY(-1px);
}

/* Estilo para o botão de contato */
a[class*="bg-blue-600"] {
  transition: all 0.2s ease;
}

a[class*="bg-blue-600"]:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
