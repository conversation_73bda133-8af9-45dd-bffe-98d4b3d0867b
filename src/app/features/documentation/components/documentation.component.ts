import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { 
  LucideAngularModule, 
  FileTextIcon, 
  CodeIcon, 
  BookOpenIcon,
  ServerIcon,
  DatabaseIcon,
  GlobeIcon,
  ClockIcon,
  ShieldIcon
} from 'lucide-angular';

interface DocSection {
  id: string;
  title: string;
  icon: any;
}

interface CodeExample {
  language: string;
  code: string;
}

@Component({
  selector: 'app-documentation',
  standalone: true,
  imports: [CommonModule, LucideAngularModule],
  templateUrl: './documentation.component.html',
  styleUrl: './documentation.component.css'
})
export class DocumentationComponent {
  readonly FileTextIcon = FileTextIcon;
  readonly CodeIcon = CodeIcon;
  readonly BookOpenIcon = BookOpenIcon;
  readonly ServerIcon = ServerIcon;
  readonly DatabaseIcon = DatabaseIcon;
  readonly GlobeIcon = GlobeIcon;
  readonly ClockIcon = ClockIcon;
  readonly ShieldIcon = ShieldIcon;
  
  activeSection: string = 'introduction';
  
  sections: DocSection[] = [
    { id: 'introduction', title: 'Introdução', icon: BookOpenIcon },
    { id: 'authentication', title: 'Autenticação', icon: ShieldIcon },
    { id: 'endpoints', title: 'Endpoints', icon: ServerIcon },
    { id: 'currencies', title: 'Moedas Suportadas', icon: GlobeIcon },
    { id: 'historical', title: 'Dados Históricos', icon: ClockIcon },
    { id: 'rate-limits', title: 'Limites de Uso', icon: DatabaseIcon },
    { id: 'examples', title: 'Exemplos de Código', icon: CodeIcon }
  ];
  
  codeExamples: { [key: string]: CodeExample } = {
    curl: {
      language: 'bash',
      code: `curl -X GET "https://api.currencywise.com/latest?base=USD&symbols=EUR,BRL,JPY" \\
-H "Authorization: Bearer sua_api_key"`
    },
    javascript: {
      language: 'javascript',
      code: `// Usando fetch
fetch('https://api.currencywise.com/latest?base=USD&symbols=EUR,BRL,JPY', {
  headers: {
    'Authorization': 'Bearer sua_api_key'
  }
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Erro:', error));`
    },
    python: {
      language: 'python',
      code: `import requests

url = "https://api.currencywise.com/latest"
headers = {"Authorization": "Bearer sua_api_key"}
params = {"base": "USD", "symbols": "EUR,BRL,JPY"}

response = requests.get(url, headers=headers, params=params)
data = response.json()
print(data)`
    },
    php: {
      language: 'php',
      code: `<?php
$url = 'https://api.currencywise.com/latest?base=USD&symbols=EUR,BRL,JPY';
$options = [
    'http' => [
        'header' => 'Authorization: Bearer sua_api_key'
    ]
];
$context = stream_context_create($options);
$response = file_get_contents($url, false, $context);
$data = json_decode($response, true);
print_r($data);
?>`
    }
  };
  
  setActiveSection(sectionId: string) {
    this.activeSection = sectionId;
  }
}
