<div class="bg-white shadow rounded-lg p-6">
  <h2 class="text-lg font-medium text-gray-900 mb-4">Tutoriais</h2>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    @for (tutorial of tutorials; track tutorial.id) {
    <div
      class="border rounded-lg p-4 hover:shadow cursor-pointer transition"
      (click)="selectTutorial(tutorial)"
    >
      <h3 class="text-base font-semibold text-blue-700">
        {{ tutorial.title }}
      </h3>
      <p class="text-gray-600 text-sm mt-1">{{ tutorial.description }}</p>
    </div>
    }
  </div>

  @if (selectedTutorial) {
  <div
    class="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50"
  >
    <div class="bg-white rounded-lg shadow-lg max-w-lg w-full p-6 relative">
      <button
        (click)="clearSelection()"
        class="absolute top-2 right-2 text-gray-400 hover:text-gray-700"
      >
        ✕
      </button>
      <h3 class="text-lg font-bold text-blue-700 mb-2">
        {{ selectedTutorial.title }}
      </h3>
      <p class="mb-4 text-gray-600">{{ selectedTutorial.description }}</p>
      <ol class="list-decimal list-inside text-gray-700 text-sm">
        @for (step of selectedTutorial.steps; track step) {
        <li class="mb-2">{{ step }}</li>
        }
      </ol>
    </div>
  </div>
  }
</div>
