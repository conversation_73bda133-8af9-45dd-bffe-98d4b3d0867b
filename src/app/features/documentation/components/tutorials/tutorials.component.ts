import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';

interface Tutorial {
  id: string;
  title: string;
  description: string;
  steps: string[];
}

@Component({
  selector: 'app-tutorials',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './tutorials.component.html',
})
export class TutorialsComponent {
  tutorials: Tutorial[] = [
    {
      id: 'auth-api',
      title: 'Como autenticar na API',
      description: 'Aprenda a obter e usar sua API Key para autenticação.',
      steps: [
        'Acesse o painel e gere uma nova API Key.',
        'Inclua a chave no header Authorization das suas requisições.',
        'Teste o acesso em um endpoint protegido.',
      ],
    },
    {
      id: 'convert-currency',
      title: 'Convertendo moedas com a API',
      description: 'Veja como realizar conversões de moeda usando a API.',
      steps: [
        'Envie uma requisição GET para /v1/convert.',
        'Informe os parâmetros de origem, destino e valor.',
        'Receba a resposta com o valor convertido.',
      ],
    },
  ];
  selectedTutorial: Tutorial | null = null;

  selectTutorial(tutorial: Tutorial) {
    this.selectedTutorial = tutorial;
  }

  clearSelection() {
    this.selectedTutorial = null;
  }
}
