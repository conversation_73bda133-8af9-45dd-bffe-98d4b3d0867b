import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';

interface FaqItem {
  question: string;
  answer: string;
  open?: boolean;
}

@Component({
  selector: 'app-docs-faq',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './docs-faq.component.html',
})
export class DocsFaqComponent {
  faqs: FaqItem[] = [
    {
      question: 'Como obtenho minha API Key?',
      answer:
        'Acesse o painel do usuário e clique em "Gerar nova API Key" na seção de chaves.',
    },
    {
      question: 'Como limito o uso da minha API Key?',
      answer:
        'Vá em configurações de segurança da chave e defina limites de requisições por período.',
    },
    {
      question: 'O que fazer em caso de erro 401?',
      answer:
        'Verifique se sua API Key está correta e ativa. Consulte o painel para mais detalhes.',
    },
  ];

  toggleFaq(faq: FaqItem) {
    faq.open = !faq.open;
  }
}
