<div class="bg-white shadow rounded-lg p-6">
  <h2 class="text-lg font-medium text-gray-900 mb-4">FAQ da Documentação</h2>
  <div class="divide-y divide-gray-200">
    @for (faq of faqs; track faq.question) {
    <div class="py-4">
      <button
        (click)="toggleFaq(faq)"
        class="w-full flex justify-between items-center text-left text-gray-800 font-medium focus:outline-none"
      >
        <span>{{ faq.question }}</span>
        <span class="ml-2">@if (faq.open) {−} @else {+}</span>
      </button>
      @if (faq.open) {
      <div class="mt-2 text-gray-600 text-sm">{{ faq.answer }}</div>
      }
    </div>
    }
  </div>
</div>
