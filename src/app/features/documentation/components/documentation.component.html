<div class="container-padded">
  <div class="flex-between mb-8">
    <h1 class="section-title">Documentação da API</h1>
    <a routerLink="/playground" class="btn-primary">
      <i-lucide [img]="CodeIcon" class="icon-sm"></i-lucide>
      Testar no Playground
    </a>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
    <!-- Sidebar de navegação -->
    <div class="lg:col-span-1">
      <div class="card sticky top-4">
        <h2 class="text-lg font-semibold mb-4">Conteúdo</h2>
        <nav>
          <ul class="space-y-1">
            @for (section of sections; track section.id) {
            <li>
              <button
                (click)="setActiveSection(section.id)"
                [class]="
                  activeSection === section.id
                    ? 'w-full flex items-center gap-2 p-2 rounded bg-blue-50 text-blue-700 font-medium'
                    : 'w-full flex items-center gap-2 p-2 rounded hover:bg-gray-100 text-gray-700'
                "
              >
                <i-lucide [img]="section.icon" class="icon-sm"></i-lucide>
                {{ section.title }}
              </button>
            </li>
            }
          </ul>
        </nav>
      </div>
    </div>

    <!-- Conteúdo principal -->
    <div class="lg:col-span-3">
      <!-- Introdução -->
      <div class="card mb-8" [hidden]="activeSection !== 'introduction'">
        <h2 class="card-title">Introdução à API CurrencyWise</h2>
        <p class="mb-4">
          A API CurrencyWise fornece acesso a taxas de câmbio em tempo real e
          histórico para mais de 170 moedas. Nossa API é projetada para ser
          simples, rápida e confiável.
        </p>
        <h3 class="text-lg font-semibold mb-2">URL Base</h3>
        <div class="code-block mb-4">
          <code>https://api.currencywise.com</code>
        </div>
        <h3 class="text-lg font-semibold mb-2">Formatos de Resposta</h3>
        <p class="mb-4">Todas as respostas são retornadas no formato JSON.</p>
        <h3 class="text-lg font-semibold mb-2">Requisitos</h3>
        <ul class="list-disc pl-5 mb-4 space-y-1">
          <li>Uma chave de API válida</li>
          <li>HTTPS para todas as solicitações</li>
          <li>Solicitações GET para endpoints de consulta</li>
        </ul>
      </div>

      <!-- Autenticação -->
      <div class="card mb-8" [hidden]="activeSection !== 'authentication'">
        <h2 class="card-title">Autenticação</h2>
        <p class="mb-4">
          Todas as solicitações à API CurrencyWise exigem autenticação usando
          uma chave de API. Você pode obter sua chave de API na seção
          <a routerLink="/api-keys" class="text-blue-600 hover:text-blue-800"
            >API Keys</a
          >
          do seu painel.
        </p>
        <h3 class="text-lg font-semibold mb-2">Autenticação via Header</h3>
        <p class="mb-2">Adicione sua chave de API no header de autorização:</p>
        <div class="code-block mb-4">
          <code>Authorization: Bearer sua_api_key</code>
        </div>
        <h3 class="text-lg font-semibold mb-2">
          Autenticação via Query Parameter
        </h3>
        <p class="mb-2">
          Alternativamente, você pode passar sua chave de API como um parâmetro
          de consulta:
        </p>
        <div class="code-block mb-4">
          <code>https://api.currencywise.com/latest?api_key=sua_api_key</code>
        </div>
        <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400">
          <h4 class="font-medium text-yellow-800 mb-1">
            Recomendação de Segurança
          </h4>
          <p class="text-yellow-700">
            Recomendamos usar o método de header para autenticação, pois é mais
            seguro do que incluir sua chave de API na URL.
          </p>
        </div>
      </div>

      <!-- Endpoints -->
      <div class="card mb-8" [hidden]="activeSection !== 'endpoints'">
        <h2 class="card-title">Endpoints Disponíveis</h2>

        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-2">GET /latest</h3>
          <p class="mb-2">Retorna as taxas de câmbio mais recentes.</p>
          <h4 class="font-medium mb-1">Parâmetros:</h4>
          <ul class="list-disc pl-5 mb-2 space-y-1">
            <li>
              <code>base</code> (opcional): Moeda base para as taxas (padrão:
              USD)
            </li>
            <li>
              <code>symbols</code> (opcional): Lista de moedas separadas por
              vírgula
            </li>
          </ul>
          <h4 class="font-medium mb-1">Exemplo de Resposta:</h4>
          <div class="code-block">
            <pre><code>&#123;
  "base": "USD",
  "date": "2024-06-10",
  "rates": &#123;
    "EUR": 0.92,
    "BRL": 5.08,
    "JPY": 149.32
  &#125;
&#125;</code></pre>
          </div>
        </div>

        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-2">GET /convert</h3>
          <p class="mb-2">Converte um valor de uma moeda para outra.</p>
          <h4 class="font-medium mb-1">Parâmetros:</h4>
          <ul class="list-disc pl-5 mb-2 space-y-1">
            <li><code>from</code> (obrigatório): Moeda de origem</li>
            <li><code>to</code> (obrigatório): Moeda de destino</li>
            <li><code>amount</code> (obrigatório): Valor a ser convertido</li>
          </ul>
          <h4 class="font-medium mb-1">Exemplo de Resposta:</h4>
          <div class="code-block">
            <pre><code>&#123;
  "from": "USD",
  "to": "EUR",
  "amount": 100,
  "result": 92.00,
  "rate": 0.92,
  "date": "2024-06-10"
&#125;</code></pre>
          </div>
        </div>

        <div>
          <h3 class="text-lg font-semibold mb-2">
            GET /historical/&#123;date&#125;
          </h3>
          <p class="mb-2">
            Retorna taxas de câmbio históricas para uma data específica.
          </p>
          <h4 class="font-medium mb-1">Parâmetros:</h4>
          <ul class="list-disc pl-5 mb-2 space-y-1">
            <li><code>date</code> (obrigatório): Data no formato YYYY-MM-DD</li>
            <li>
              <code>base</code> (opcional): Moeda base para as taxas (padrão:
              USD)
            </li>
            <li>
              <code>symbols</code> (opcional): Lista de moedas separadas por
              vírgula
            </li>
          </ul>
          <h4 class="font-medium mb-1">Exemplo de Resposta:</h4>
          <div class="code-block">
            <pre><code>&#123;
  "base": "USD",
  "date": "2023-06-10",
  "rates": &#123;
    "EUR": 0.93,
    "BRL": 4.95,
    "JPY": 139.45
  &#125;
&#125;</code></pre>
          </div>
        </div>
      </div>

      <!-- Moedas Suportadas -->
      <div class="card mb-8" [hidden]="activeSection !== 'currencies'">
        <h2 class="card-title">Moedas Suportadas</h2>
        <p class="mb-4">
          A API CurrencyWise suporta mais de 170 moedas de todo o mundo. Abaixo
          estão algumas das moedas mais comuns:
        </p>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <div class="p-3 bg-gray-50 rounded-lg">
            <div class="font-medium">USD</div>
            <div class="text-muted">Dólar Americano</div>
          </div>
          <div class="p-3 bg-gray-50 rounded-lg">
            <div class="font-medium">EUR</div>
            <div class="text-muted">Euro</div>
          </div>
          <div class="p-3 bg-gray-50 rounded-lg">
            <div class="font-medium">GBP</div>
            <div class="text-muted">Libra Esterlina</div>
          </div>
          <div class="p-3 bg-gray-50 rounded-lg">
            <div class="font-medium">JPY</div>
            <div class="text-muted">Iene Japonês</div>
          </div>
          <div class="p-3 bg-gray-50 rounded-lg">
            <div class="font-medium">BRL</div>
            <div class="text-muted">Real Brasileiro</div>
          </div>
          <div class="p-3 bg-gray-50 rounded-lg">
            <div class="font-medium">CAD</div>
            <div class="text-muted">Dólar Canadense</div>
          </div>
          <div class="p-3 bg-gray-50 rounded-lg">
            <div class="font-medium">AUD</div>
            <div class="text-muted">Dólar Australiano</div>
          </div>
          <div class="p-3 bg-gray-50 rounded-lg">
            <div class="font-medium">CHF</div>
            <div class="text-muted">Franco Suíço</div>
          </div>
          <div class="p-3 bg-gray-50 rounded-lg">
            <div class="font-medium">CNY</div>
            <div class="text-muted">Yuan Chinês</div>
          </div>
        </div>
        <p class="mb-2">
          Para obter a lista completa de moedas suportadas, use o endpoint:
        </p>
        <div class="code-block">
          <code>GET /currencies</code>
        </div>
      </div>

      <!-- Dados Históricos -->
      <div class="card mb-8" [hidden]="activeSection !== 'historical'">
        <h2 class="card-title">Dados Históricos</h2>
        <p class="mb-4">
          A API CurrencyWise fornece acesso a dados históricos de taxas de
          câmbio, permitindo que você consulte taxas para datas específicas ou
          intervalos de tempo.
        </p>

        <h3 class="text-lg font-semibold mb-2">Consulta por Data Específica</h3>
        <p class="mb-2">Para consultar taxas de uma data específica:</p>
        <div class="code-block mb-4">
          <code>GET /historical/2023-01-15</code>
        </div>

        <h3 class="text-lg font-semibold mb-2">
          Consulta por Intervalo de Datas
        </h3>
        <p class="mb-2">Para consultar taxas em um intervalo de datas:</p>
        <div class="code-block mb-4">
          <code>GET /timeseries?start_date=2023-01-01&end_date=2023-01-31</code>
        </div>

        <h3 class="text-lg font-semibold mb-2">Disponibilidade de Dados</h3>
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50 text-left">
              <tr>
                <th class="px-4 py-3 text-sm font-medium text-gray-500">
                  Plano
                </th>
                <th class="px-4 py-3 text-sm font-medium text-gray-500">
                  Histórico Disponível
                </th>
                <th class="px-4 py-3 text-sm font-medium text-gray-500">
                  Granularidade
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <tr>
                <td class="px-4 py-3">Básico</td>
                <td class="px-4 py-3">1 mês</td>
                <td class="px-4 py-3">Diária</td>
              </tr>
              <tr>
                <td class="px-4 py-3">Profissional</td>
                <td class="px-4 py-3">1 ano</td>
                <td class="px-4 py-3">Diária</td>
              </tr>
              <tr>
                <td class="px-4 py-3">Empresarial</td>
                <td class="px-4 py-3">Desde 1999</td>
                <td class="px-4 py-3">Diária, Horária</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Limites de Uso -->
      <div class="card mb-8" [hidden]="activeSection !== 'rate-limits'">
        <h2 class="card-title">Limites de Uso</h2>
        <p class="mb-4">
          Para garantir a qualidade do serviço para todos os usuários, a API
          CurrencyWise implementa limites de uso baseados no seu plano.
        </p>

        <h3 class="text-lg font-semibold mb-2">Limites por Plano</h3>
        <div class="overflow-x-auto mb-6">
          <table class="w-full">
            <thead class="bg-gray-50 text-left">
              <tr>
                <th class="px-4 py-3 text-sm font-medium text-gray-500">
                  Plano
                </th>
                <th class="px-4 py-3 text-sm font-medium text-gray-500">
                  Requisições/Mês
                </th>
                <th class="px-4 py-3 text-sm font-medium text-gray-500">
                  Requisições/Segundo
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <tr>
                <td class="px-4 py-3">Básico</td>
                <td class="px-4 py-3">1.000</td>
                <td class="px-4 py-3">1</td>
              </tr>
              <tr>
                <td class="px-4 py-3">Profissional</td>
                <td class="px-4 py-3">100.000</td>
                <td class="px-4 py-3">10</td>
              </tr>
              <tr>
                <td class="px-4 py-3">Empresarial</td>
                <td class="px-4 py-3">1.000.000</td>
                <td class="px-4 py-3">100</td>
              </tr>
            </tbody>
          </table>
        </div>

        <h3 class="text-lg font-semibold mb-2">Headers de Limite</h3>
        <p class="mb-2">
          Cada resposta da API inclui headers que informam sobre o seu uso
          atual:
        </p>
        <div class="code-block mb-4">
          <pre><code>X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 950
X-RateLimit-Reset: 1623456789</code></pre>
        </div>

        <h3 class="text-lg font-semibold mb-2">Excedendo Limites</h3>
        <p class="mb-2">
          Se você exceder o limite de requisições, receberá um erro 429 (Too
          Many Requests):
        </p>
        <div class="code-block">
          <pre><code>&#123;
  "error": &#123;
    "code": "rate_limit_exceeded",
    "message": "Limite de requisições excedido. Tente novamente em 3600 segundos."
  &#125;
&#125;</code></pre>
        </div>
      </div>

      <!-- Exemplos de Código -->
      <div class="card" [hidden]="activeSection !== 'examples'">
        <h2 class="card-title">Exemplos de Código</h2>
        <p class="mb-4">
          Abaixo estão exemplos de como usar a API CurrencyWise em diferentes
          linguagens de programação.
        </p>

        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-2">cURL</h3>
          <div class="code-block">
            <pre><code>{{ codeExamples['curl'].code }}</code></pre>
          </div>
        </div>

        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-2">JavaScript</h3>
          <div class="code-block">
            <pre><code>{{ codeExamples['javascript'].code }}</code></pre>
          </div>
        </div>

        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-2">Python</h3>
          <div class="code-block">
            <pre><code>{{ codeExamples['python'].code }}</code></pre>
          </div>
        </div>

        <div>
          <h3 class="text-lg font-semibold mb-2">PHP</h3>
          <div class="code-block">
            <pre><code>{{ codeExamples['php'].code }}</code></pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
