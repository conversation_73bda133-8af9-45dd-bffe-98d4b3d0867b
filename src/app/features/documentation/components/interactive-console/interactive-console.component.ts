import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

interface ApiResponse {
  status: number;
  body: any;
  headers: Record<string, string>;
}

@Component({
  selector: 'app-interactive-console',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './interactive-console.component.html',
})
export class InteractiveConsoleComponent {
  requestForm: FormGroup;
  response: ApiResponse | null = null;
  loading = false;
  error: string | null = null;

  constructor(private fb: FormBuilder) {
    this.requestForm = this.fb.group({
      method: ['GET', Validators.required],
      endpoint: ['', Validators.required],
      headers: [''],
      body: [''],
    });
  }

  sendRequest() {
    if (this.requestForm.invalid) return;
    this.loading = true;
    this.error = null;
    this.response = null;
    // Simulação de resposta fake
    setTimeout(() => {
      this.loading = false;
      if (this.requestForm.value.endpoint.includes('error')) {
        this.error = 'Erro ao processar requisição.';
      } else {
        this.response = {
          status: 200,
          body: { message: 'Requisição bem-sucedida!', data: { exemplo: 123 } },
          headers: { 'content-type': 'application/json' },
        };
      }
    }, 1200);
  }
}
