<div class="bg-white shadow rounded-lg p-6">
  <h2 class="text-lg font-medium text-gray-900 mb-4">Console Interativo</h2>
  <form [formGroup]="requestForm" (ngSubmit)="sendRequest()" class="space-y-4">
    <div class="flex flex-col md:flex-row md:space-x-4">
      <div class="w-full md:w-1/5">
        <label class="block text-sm font-medium text-gray-700">Método</label>
        <select
          formControlName="method"
          class="mt-1 block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
        >
          <option value="GET">GET</option>
          <option value="POST">POST</option>
          <option value="PUT">PUT</option>
          <option value="DELETE">DELETE</option>
          <option value="PATCH">PATCH</option>
        </select>
      </div>
      <div class="w-full md:w-4/5 mt-2 md:mt-0">
        <label class="block text-sm font-medium text-gray-700">Endpoint</label>
        <input
          type="text"
          formControlName="endpoint"
          placeholder="/v1/exemplo"
          class="mt-1 block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
        />
      </div>
    </div>
    <div>
      <label class="block text-sm font-medium text-gray-700"
        >Headers (JSON)</label
      >
      <textarea
        formControlName="headers"
        rows="2"
        class="mt-1 block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
        placeholder='{"Authorization": "Bearer ..."}'
      ></textarea>
    </div>
    <div>
      <label class="block text-sm font-medium text-gray-700">Body (JSON)</label>
      <textarea
        formControlName="body"
        rows="3"
        class="mt-1 block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
        placeholder='{"campo": "valor"}'
      ></textarea>
    </div>
    <div class="flex justify-end">
      <button
        type="submit"
        [disabled]="loading || requestForm.invalid"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        @if (loading) {<span class="animate-spin mr-2">⏳</span>}Enviar
      </button>
    </div>
  </form>

  @if (error) {
  <div class="mt-6 p-4 bg-red-50 text-red-700 rounded">{{ error }}</div>
  } @if (response) {
  <div class="mt-6">
    <h3 class="text-base font-semibold text-gray-900 mb-2">Resposta</h3>
    <div class="bg-gray-100 rounded p-4 text-sm font-mono">
      <div><span class="font-bold">Status:</span> {{ response.status }}</div>
      <div>
        <span class="font-bold">Headers:</span> {{ response.headers | json }}
      </div>
      <div><span class="font-bold">Body:</span></div>
      <pre class="whitespace-pre-wrap">{{ response.body | json }}</pre>
    </div>
  </div>
  }
</div>
