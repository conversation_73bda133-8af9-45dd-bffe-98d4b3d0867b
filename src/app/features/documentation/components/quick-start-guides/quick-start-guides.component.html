<div class="bg-white shadow rounded-lg p-6">
  <h2 class="text-lg font-medium text-gray-900 mb-4"><PERSON><PERSON><PERSON> <PERSON></h2>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    @for (guide of guides; track guide.id) {
    <div
      class="border rounded-lg p-4 hover:shadow cursor-pointer transition"
      (click)="selectGuide(guide)"
    >
      <h3 class="text-base font-semibold text-blue-700">{{ guide.title }}</h3>
      <ol class="list-decimal list-inside mt-2 text-gray-700 text-sm">
        @for (step of guide.steps; track step) {
        <li>{{ step }}</li>
        }
      </ol>
    </div>
    }
  </div>

  @if (selectedGuide) {
  <div
    class="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50"
  >
    <div class="bg-white rounded-lg shadow-lg max-w-lg w-full p-6 relative">
      <button
        (click)="clearSelection()"
        class="absolute top-2 right-2 text-gray-400 hover:text-gray-700"
      >
        ✕
      </button>
      <h3 class="text-lg font-bold text-blue-700 mb-2">
        {{ selectedGuide.title }}
      </h3>
      <ol class="list-decimal list-inside mb-4 text-gray-700 text-sm">
        @for (step of selectedGuide.steps; track step) {
        <li>{{ step }}</li>
        }
      </ol>
      <div>
        <label class="block text-xs font-medium text-gray-500 mb-1"
          >Exemplo de Código:</label
        >
        <pre
          class="bg-gray-100 rounded p-3 text-xs overflow-x-auto"
        ><code>{{ selectedGuide.codeExample }}</code></pre>
      </div>
    </div>
  </div>
  }
</div>
