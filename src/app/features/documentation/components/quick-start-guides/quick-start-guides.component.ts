import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';

interface QuickStartGuide {
  id: string;
  title: string;
  steps: string[];
  codeExample: string;
}

@Component({
  selector: 'app-quick-start-guides',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './quick-start-guides.component.html',
})
export class QuickStartGuidesComponent {
  guides: QuickStartGuide[] = [
    {
      id: 'nodejs',
      title: 'Node.js - Consumindo a API',
      steps: [
        'Instale o axios: npm install axios',
        'Configure sua API Key',
        'Faça uma requisição para o endpoint desejado',
      ],
      codeExample: `const axios = require('axios');\naxios.get('https://api.currencywise.com/v1/rates', {\n  headers: { Authorization: 'Bearer SUA_API_KEY' }\n}).then(res => console.log(res.data));`,
    },
    {
      id: 'python',
      title: 'Python - Primeira Requisição',
      steps: [
        'Instale o requests: pip install requests',
        'Configure sua API Key',
        'Envie uma requisição GET',
      ],
      codeExample: `import requests\nresp = requests.get('https://api.currencywise.com/v1/rates',\n  headers={'Authorization': 'Bearer SUA_API_KEY'})\nprint(resp.json())`,
    },
  ];
  selectedGuide: QuickStartGuide | null = null;

  selectGuide(guide: QuickStartGuide) {
    this.selectedGuide = guide;
  }

  clearSelection() {
    this.selectedGuide = null;
  }
}
