import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, catchError, map, of, tap } from 'rxjs';
import { environment } from '../../../../environments/environment';
import {
  CotacaoQueryParams,
  CotacaoResponse,
  CurrencyRate,
  CurrencyResponse,
  HistoricalRate,
} from '../interfaces/currency.interface';

// Chaves para o localStorage
const MOEDAS_CACHE_KEY = 'currencywise_moedas_cache';
const COTACOES_CACHE_KEY = 'currencywise_cotacoes_cache';
const LAST_UPDATED_KEY = 'currencywise_last_updated';
const HISTORICAL_RATES_KEY = 'currencywise_historical_rates';

@Injectable({
  providedIn: 'root',
})
export class CotacaoService {
  private readonly API_URL = `${environment.apiUrl}/cotacao`;

  // Cache para armazenar as moedas
  private moedasCache: CurrencyResponse | null = null;

  // Cache para armazenar as cotações recentes
  private cotacoesCache: Map<string, CotacaoResponse> = new Map();

  // Data da última atualização
  private lastUpdated: Date = new Date();

  // Histórico de taxas de câmbio
  private historicalRates: Map<string, HistoricalRate[]> = new Map();

  constructor(private http: HttpClient) {
    // Carrega os dados do localStorage ao inicializar o serviço
    this.loadFromLocalStorage();
  }

  /**
   * Carrega os dados do localStorage
   */
  private loadFromLocalStorage(): void {
    try {
      // Carrega as moedas do localStorage
      const moedasJson = localStorage.getItem(MOEDAS_CACHE_KEY);
      if (moedasJson) {
        this.moedasCache = JSON.parse(moedasJson);
      }

      // Carrega as cotações do localStorage
      const cotacoesJson = localStorage.getItem(COTACOES_CACHE_KEY);
      if (cotacoesJson) {
        const cotacoes = JSON.parse(cotacoesJson);
        this.cotacoesCache = new Map(Object.entries(cotacoes));
      }

      // Carrega a data da última atualização
      const lastUpdatedJson = localStorage.getItem(LAST_UPDATED_KEY);
      if (lastUpdatedJson) {
        this.lastUpdated = new Date(JSON.parse(lastUpdatedJson));
      }

      // Carrega o histórico de taxas
      const historicalRatesJson = localStorage.getItem(HISTORICAL_RATES_KEY);
      if (historicalRatesJson) {
        const historicalRatesObj = JSON.parse(historicalRatesJson);

        // Converte as datas de string para objeto Date
        Object.keys(historicalRatesObj).forEach((key) => {
          const rates = historicalRatesObj[key].map((rate: any) => ({
            ...rate,
            timestamp: new Date(rate.timestamp),
          }));
          this.historicalRates.set(key, rates);
        });
      }
    } catch (error) {
      console.error('Erro ao carregar dados do localStorage:', error);
      // Em caso de erro, limpa o cache
      this.clearCache();
    }
  }

  /**
   * Salva os dados no localStorage
   */
  private saveToLocalStorage(): void {
    try {
      // Salva as moedas no localStorage
      if (this.moedasCache) {
        localStorage.setItem(
          MOEDAS_CACHE_KEY,
          JSON.stringify(this.moedasCache)
        );
      }

      // Converte o Map para um objeto para salvar no localStorage
      const cotacoesObj = Object.fromEntries(this.cotacoesCache);
      localStorage.setItem(COTACOES_CACHE_KEY, JSON.stringify(cotacoesObj));

      // Salva a data da última atualização
      localStorage.setItem(
        LAST_UPDATED_KEY,
        JSON.stringify(this.lastUpdated.toISOString())
      );

      // Salva o histórico de taxas
      if (this.historicalRates.size > 0) {
        const historicalRatesObj = Object.fromEntries(this.historicalRates);
        localStorage.setItem(
          HISTORICAL_RATES_KEY,
          JSON.stringify(historicalRatesObj)
        );
      }
    } catch (error) {
      console.error('Erro ao salvar dados no localStorage:', error);
    }
  }

  /**
   * Adiciona uma taxa ao histórico
   * @param from Moeda de origem
   * @param to Moeda de destino
   * @param rate Taxa de câmbio
   */
  private addToHistory(from: string, to: string, rate: number): void {
    // Cria a chave para o histórico
    const historyKey = `${from}-${to}`;

    // Obtém o histórico atual ou cria um novo array
    const history = this.historicalRates.get(historyKey) || [];

    // Adiciona a nova taxa ao histórico
    const newEntry: HistoricalRate = {
      from,
      to,
      rate,
      timestamp: new Date(),
    };

    // Limita o histórico a 30 entradas (aproximadamente 1 mês com atualização diária)
    history.push(newEntry);
    if (history.length > 30) {
      history.shift(); // Remove a entrada mais antiga
    }

    // Atualiza o histórico
    this.historicalRates.set(historyKey, history);

    // Salva no localStorage
    this.saveToLocalStorage();
  }

  /**
   * Obtém o histórico de taxas para um par de moedas
   * @param from Moeda de origem
   * @param to Moeda de destino
   * @returns Array com o histórico de taxas
   */
  getHistoricalRates(from: string, to: string): HistoricalRate[] {
    const historyKey = `${from}-${to}`;
    return this.historicalRates.get(historyKey) || [];
  }

  /**
   * Calcula a variação percentual entre a taxa atual e a anterior
   * @param from Moeda de origem
   * @param to Moeda de destino
   * @param currentRate Taxa atual
   * @returns Variação percentual
   */
  calculateRateChange(from: string, to: string, currentRate: number): number {
    const history = this.getHistoricalRates(from, to);

    // Se não houver histórico, retorna 0
    if (history.length === 0) {
      return 0;
    }

    // Obtém a taxa anterior (a penúltima entrada no histórico)
    const previousEntry =
      history.length > 1 ? history[history.length - 2] : null;

    // Se não houver entrada anterior, retorna 0
    if (!previousEntry) {
      return 0;
    }

    // Calcula a variação percentual
    const previousRate = previousEntry.rate;
    const change = ((currentRate - previousRate) / previousRate) * 100;

    return change;
  }

  /**
   * Obtém a data da última atualização
   */
  getLastUpdated(): Date {
    return this.lastUpdated;
  }

  /**
   * Obtém a lista de moedas disponíveis
   * @param tipo Tipo de moeda (fiat ou crypto)
   * @returns Observable com a lista de moedas
   */
  getMoedas(tipo?: 'fiat' | 'crypto'): Observable<CurrencyResponse> {
    // Se já temos as moedas em cache e não foi especificado um tipo, retorna do cache
    if (this.moedasCache && !tipo) {
      return of(this.moedasCache);
    }

    // Constrói os parâmetros da requisição
    const params: any = {};
    if (tipo) {
      params.tipo = tipo;
    }

    // Faz a requisição para a API
    return this.http
      .get<CurrencyResponse>(`${this.API_URL}/moedas`, { params })
      .pipe(
        tap((response) => {
          // Armazena no cache apenas se não foi especificado um tipo
          if (!tipo) {
            this.moedasCache = response;
            // Atualiza a data da última atualização
            this.lastUpdated = new Date();
            // Salva no localStorage
            this.saveToLocalStorage();
          }
        }),
        catchError((error) => {
          console.error('Erro ao obter moedas:', error);
          // Retorna um array vazio em caso de erro
          return of({ moedas: [], timestamp: new Date().toISOString() });
        })
      );
  }

  /**
   * Obtém a cotação de uma moeda
   * @param params Parâmetros da cotação
   * @returns Observable com a cotação
   */
  getCotacao(params: CotacaoQueryParams): Observable<CotacaoResponse> {
    // Constrói a chave para o cache
    const cacheKey = `${params.from}-${params.to}-${params.amount || 1}`;

    // Se já temos a cotação em cache e não foi especificada uma data, retorna do cache
    if (this.cotacoesCache.has(cacheKey) && !params.date) {
      return of(this.cotacoesCache.get(cacheKey)!);
    }

    // Faz a requisição para a API
    return this.http
      .get<CotacaoResponse>(`${this.API_URL}`, { params: params as any })
      .pipe(
        tap((response) => {
          // Armazena no cache apenas se não foi especificada uma data
          if (!params.date) {
            // Adiciona a taxa ao histórico
            this.addToHistory(
              params.from || '',
              params.to || '',
              response.rate
            );

            // Calcula a variação percentual
            const change = this.calculateRateChange(
              params.from || '',
              params.to || '',
              response.rate
            );

            // Atualiza a resposta com a variação calculada
            response.change_24h = change;

            // Armazena no cache
            this.cotacoesCache.set(cacheKey, response);

            // Atualiza a data da última atualização
            this.lastUpdated = new Date();

            // Salva no localStorage
            this.saveToLocalStorage();
          }
        }),
        catchError((error) => {
          console.error('Erro ao obter cotação:', error);
          // Retorna uma cotação vazia em caso de erro
          return of({
            from: params.from || '',
            to: params.to || '',
            amount: params.amount || 1,
            result: 0,
            rate: 0,
            timestamp: new Date().toISOString(),
          });
        })
      );
  }

  /**
   * Obtém as taxas de câmbio para as moedas especificadas em relação ao BRL
   * @param currencyCodes Códigos das moedas
   * @returns Observable com as taxas de câmbio
   */
  getCurrencyRates(currencyCodes: string[]): Observable<CurrencyRate[]> {
    // Obtém as moedas para ter os nomes
    return this.getMoedas().pipe(
      // Mapeia para um array de observables de cotações
      map((response) => {
        const moedas = response.moedas;

        // Para cada código de moeda, cria um objeto CurrencyRate
        return currencyCodes.map((code) => {
          // Encontra a moeda correspondente
          const moeda = moedas.find((m) => m.code === code);

          // Retorna um objeto CurrencyRate com valores iniciais
          return {
            code,
            name: moeda?.name || code,
            rate: 0,
            change: 0,
          };
        });
      }),
      // Para cada CurrencyRate, obtém a cotação atual
      map((rates) => {
        // Para cada taxa, obtém a cotação em relação ao BRL
        rates.forEach((rate) => {
          this.getCotacao({ from: 'BRL', to: rate.code, amount: 1 }).subscribe(
            (cotacao) => {
              rate.rate = cotacao.rate;
              // A variação é calculada com base na taxa atual e na taxa de 24h atrás
              rate.change = cotacao.change_24h || 0;
            }
          );
        });

        return rates;
      })
    );
  }

  /**
   * Limpa o cache de cotações
   */
  clearCache(): void {
    this.moedasCache = null;
    this.cotacoesCache.clear();

    // Não limpa o histórico de taxas, apenas os caches de moedas e cotações

    // Limpa os dados do localStorage
    localStorage.removeItem(MOEDAS_CACHE_KEY);
    localStorage.removeItem(COTACOES_CACHE_KEY);

    // Atualiza a data da última atualização
    this.lastUpdated = new Date();
    localStorage.setItem(
      LAST_UPDATED_KEY,
      JSON.stringify(this.lastUpdated.toISOString())
    );
  }
}
