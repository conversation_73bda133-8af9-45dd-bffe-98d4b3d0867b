import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import {
  DollarSignIcon,
  EuroIcon,
  LucideAngularModule,
  RefreshCwIcon,
  TrendingDownIcon,
  TrendingUpIcon,
} from 'lucide-angular';
import { Subscription, interval } from 'rxjs';
import { I18nService } from '../../../core/services/i18n.service';
import { TranslatePipe } from '../../../shared/pipes/translate.pipe';
import {
  CurrencyRate,
  HistoricalRate,
  RecentConversion,
} from '../interfaces/currency.interface';
import { CotacaoService } from '../services/cotacao.service';
import { UsageChartComponent } from './usage-chart/usage-chart.component';

// Intervalo de atualização automática em milissegundos (6 horas)
const AUTO_REFRESH_INTERVAL = 6 * 60 * 60 * 1000;

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, LucideAngularModule, TranslatePipe, UsageChartComponent],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css',
})
export class DashboardComponent implements OnInit, OnDestroy {
  readonly TrendingUpIcon = TrendingUpIcon;
  readonly TrendingDownIcon = TrendingDownIcon;
  readonly RefreshCwIcon = RefreshCwIcon;
  readonly DollarSignIcon = DollarSignIcon;
  readonly EuroIcon = EuroIcon;

  // Data da última atualização
  lastUpdated = new Date();

  // Moedas exibidas no dashboard
  currencies: CurrencyRate[] = [];

  // Conversões recentes
  recentConversions: RecentConversion[] = [];
  
  // Dados históricos para o gráfico
  historicalRates: HistoricalRate[] = [];
  
  // Par de moedas para o gráfico
  currencyPair = { from: 'BRL', to: 'USD' };

  // Estado de carregamento
  isLoading = false;

  // Subscriptions para gerenciar a limpeza
  private subscriptions: Subscription[] = [];

  // Subscription para atualização automática
  private autoRefreshSubscription?: Subscription;

  constructor(
    public i18nService: I18nService,
    private cotacaoService: CotacaoService
  ) {}

  ngOnInit(): void {
    // Inicializa as moedas com base no idioma atual
    this.initializeCurrencies();

    // Obtém a data da última atualização do serviço
    this.lastUpdated = this.cotacaoService.getLastUpdated();

    // Carrega as cotações reais da API
    this.loadRealRates();
    
    // Carrega os dados históricos
    this.loadHistoricalRates();

    // Inicializa as conversões recentes (simuladas por enquanto)
    this.initializeRecentConversions();

    // Inscreve-se para receber atualizações de idioma
    const languageSub = this.i18nService.currentLanguage$.subscribe(() => {
      // Reinicializa as moedas quando o idioma mudar
      this.initializeCurrencies();
      
      // Atualiza o par de moedas para o gráfico
      this.updateCurrencyPair();
      
      // Recarrega as taxas de câmbio com base no novo idioma
      this.loadRealRates();
      
      // Recarrega os dados históricos
      this.loadHistoricalRates();
    });

    // Adiciona a subscription à lista para limpeza posterior
    this.subscriptions.push(languageSub);

    // Configura a atualização automática a cada 6 horas
    this.setupAutoRefresh();
  }

  ngOnDestroy(): void {
    // Cancela todas as subscriptions ao destruir o componente
    this.subscriptions.forEach((sub) => sub.unsubscribe());

    // Cancela a atualização automática
    if (this.autoRefreshSubscription) {
      this.autoRefreshSubscription.unsubscribe();
    }
  }

  /**
   * Configura a atualização automática das taxas a cada 6 horas
   */
  private setupAutoRefresh(): void {
    // Cancela qualquer subscription existente
    if (this.autoRefreshSubscription) {
      this.autoRefreshSubscription.unsubscribe();
    }

    // Cria uma nova subscription para atualização automática
    this.autoRefreshSubscription = interval(AUTO_REFRESH_INTERVAL).subscribe(
      () => {
        console.log('Atualizando taxas automaticamente...');
        this.refreshRates();
      }
    );
  }
  
  /**
   * Atualiza o par de moedas com base no idioma atual
   */
  private updateCurrencyPair(): void {
    const currentLanguage = this.i18nService.getCurrentLanguage();
    
    if (currentLanguage === 'pt') {
      this.currencyPair = { from: 'BRL', to: 'USD' };
    } else {
      this.currencyPair = { from: 'USD', to: 'EUR' };
    }
  }

  /**
   * Inicializa as moedas com base no idioma atual
   */
  private initializeCurrencies(): void {
    const currentLanguage = this.i18nService.getCurrentLanguage();

    // Nomes das moedas em português
    const currencyNamesPT = {
      USD: 'Dólar Americano',
      EUR: 'Euro',
    };

    // Nomes das moedas em inglês
    const currencyNamesEN = {
      USD: 'US Dollar',
      EUR: 'Euro',
    };

    // Seleciona os nomes das moedas com base no idioma atual
    const currencyNames =
      currentLanguage === 'pt' ? currencyNamesPT : currencyNamesEN;

    this.currencies = [
      {
        code: 'USD',
        name: currencyNames.USD,
        rate: 0,
        change: 0,
        icon: DollarSignIcon,
      },
      {
        code: 'EUR',
        name: currencyNames.EUR,
        rate: 0,
        change: 0,
        icon: EuroIcon,
      },
    ];
    
    // Atualiza o par de moedas para o gráfico
    this.updateCurrencyPair();
  }
  
  /**
   * Carrega os dados históricos para o gráfico
   */
  private loadHistoricalRates(): void {
    // Obtém os dados históricos do serviço
    this.historicalRates = this.cotacaoService.getHistoricalRates(
      this.currencyPair.from,
      this.currencyPair.to
    );
    
    // Se não houver dados históricos, gera dados de exemplo
    if (this.historicalRates.length === 0) {
      this.generateFakeHistoricalRates();
    }
  }
  
  /**
   * Gera dados históricos de exemplo
   */
  private generateFakeHistoricalRates(): void {
    const today = new Date();
    const rates: HistoricalRate[] = [];
    
    // Gera dados para os últimos 30 dias
    for (let i = 29; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      // Gera uma taxa aleatória entre 4.8 e 5.2 para BRL/USD
      // ou entre 0.9 e 1.1 para USD/EUR
      let baseRate = this.currencyPair.from === 'BRL' ? 5.0 : 0.92;
      const variation = (Math.random() - 0.5) * 0.4;
      const rate = baseRate + variation;
      
      rates.push({
        from: this.currencyPair.from,
        to: this.currencyPair.to,
        rate,
        timestamp: date
      });
    }
    
    this.historicalRates = rates;
  }

  /**
   * Carrega as taxas de câmbio reais da API
   */
  private loadRealRates(): void {
    this.isLoading = true;
    const currentLanguage = this.i18nService.getCurrentLanguage();

    if (currentLanguage === 'pt') {
      // Para português: Mostrar o valor do dólar e euro em comparação ao real (BRL)

      // Cotação BRL -> USD (quanto vale 1 BRL em USD)
      this.cotacaoService.getCotacao({ from: 'BRL', to: 'USD' }).subscribe({
        next: (response) => {
          const usdCurrency = this.currencies.find((c) => c.code === 'USD');
          if (usdCurrency) {
            // Invertemos a taxa para mostrar quanto vale 1 USD em BRL
            usdCurrency.rate = 1 / response.rate;
            usdCurrency.change = response.change_24h || 0;
          }
          this.updateLoadingState();
        },
        error: (error) => {
          console.error('Erro ao obter cotação USD:', error);
          this.updateLoadingState();
        },
      });

      // Cotação BRL -> EUR (quanto vale 1 BRL em EUR)
      this.cotacaoService.getCotacao({ from: 'BRL', to: 'EUR' }).subscribe({
        next: (response) => {
          const eurCurrency = this.currencies.find((c) => c.code === 'EUR');
          if (eurCurrency) {
            // Invertemos a taxa para mostrar quanto vale 1 EUR em BRL
            eurCurrency.rate = 1 / response.rate;
            eurCurrency.change = response.change_24h || 0;
          }
          this.updateLoadingState();
        },
        error: (error) => {
          console.error('Erro ao obter cotação EUR:', error);
          this.updateLoadingState();
        },
      });
    } else {
      // Para inglês: Mostrar o valor do dólar (USD) e euro (EUR) em comparação ao dólar

      // Para USD, a taxa é sempre 1 (1 USD = 1 USD)
      const usdCurrency = this.currencies.find((c) => c.code === 'USD');
      if (usdCurrency) {
        usdCurrency.rate = 1.0;
        usdCurrency.change = 0.0;
      }

      // Cotação USD -> EUR (quanto vale 1 USD em EUR)
      this.cotacaoService.getCotacao({ from: 'USD', to: 'EUR' }).subscribe({
        next: (response) => {
          const eurCurrency = this.currencies.find((c) => c.code === 'EUR');
          if (eurCurrency) {
            eurCurrency.rate = response.rate;
            eurCurrency.change = response.change_24h || 0;
          }
          this.updateLoadingState();
        },
        error: (error) => {
          console.error('Erro ao obter cotação EUR:', error);
          this.updateLoadingState();
        },
      });
    }

    // Atualiza a data da última atualização
    this.lastUpdated = this.cotacaoService.getLastUpdated();
  }

  /**
   * Atualiza o estado de carregamento após receber respostas da API
   */
  private updateLoadingState(): void {
    // Verifica se todas as moedas têm taxas válidas
    const allRatesLoaded = this.currencies.every((c) => c.rate > 0);

    // Se todas as taxas foram carregadas, desativa o estado de carregamento
    if (allRatesLoaded) {
      this.isLoading = false;
    }
  }

  /**
   * Inicializa as conversões recentes (simuladas por enquanto)
   */
  private initializeRecentConversions(): void {
    // Aqui seriam carregadas as conversões recentes do usuário
    // Por enquanto, vamos simular com dados estáticos
    this.recentConversions = [
      {
        fromCurrency: 'USD',
        toCurrency: 'EUR',
        originalAmount: 100,
        convertedAmount: 92,
        timestamp: new Date(2024, 5, 10, 14, 30),
      },
      {
        fromCurrency: 'EUR',
        toCurrency: 'BRL',
        originalAmount: 50,
        convertedAmount: 276.09,
        timestamp: new Date(2024, 5, 10, 13, 15),
      },
      {
        fromCurrency: 'GBP',
        toCurrency: 'USD',
        originalAmount: 200,
        convertedAmount: 256.41,
        timestamp: new Date(2024, 5, 10, 11, 45),
      },
    ];
  }

  /**
   * Atualiza as taxas de câmbio
   */
  refreshRates(): void {
    // Limpa o cache do serviço para garantir que obteremos dados atualizados
    this.cotacaoService.clearCache();

    // Carrega as taxas atualizadas
    this.loadRealRates();
    
    // Recarrega os dados históricos
    this.loadHistoricalRates();
  }
}
