<div class="bg-white shadow rounded-lg p-6">
  <h2 class="text-lg font-medium text-gray-900 mb-6">Acesso Rápido</h2>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
    @for (item of quickAccessItems; track item.title) {
    <button
      (click)="navigateTo(item.route)"
      class="flex flex-col items-center p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
    >
      <div class="p-3 bg-blue-50 rounded-full mb-4">
        <ng-container *ngComponentOutlet="item.icon" />
      </div>
      <h3 class="text-lg font-medium text-gray-900">{{ item.title }}</h3>
      <p class="mt-2 text-sm text-gray-500 text-center">
        {{ item.description }}
      </p>
    </button>
    }
  </div>
</div>
