import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { Book, Key, LucideAngularModule, Play, Settings } from 'lucide-angular';

interface QuickAccessItem {
  title: string;
  description: string;
  icon: any;
  route: string;
}

@Component({
  selector: 'app-quick-access',
  standalone: true,
  imports: [CommonModule, LucideAngularModule],
  templateUrl: './quick-access.component.html',
})
export class QuickAccessComponent {
  quickAccessItems: QuickAccessItem[] = [
    {
      title: 'API Keys',
      description: 'Gerencie suas chaves de API',
      icon: Key,
      route: '/dashboard/api-keys',
    },
    {
      title: 'Documentação',
      description: 'Acesse a documentação completa',
      icon: Book,
      route: '/docs',
    },
    {
      title: 'Playground',
      description: 'Teste a API em tempo real',
      icon: Play,
      route: '/playground',
    },
    {
      title: 'Configurações',
      description: 'Configure suas preferências',
      icon: Settings,
      route: '/dashboard/settings',
    },
  ];

  constructor(private router: Router) {}

  navigateTo(route: string): void {
    this.router.navigate([route]);
  }
}
