<!-- Overlay de carregamento para toda a página -->
<div
  *ngIf="isLoading"
  class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center backdrop-blur-sm"
>
  <div
    class="bg-white p-8 rounded-xl shadow-2xl flex flex-col items-center animate-pulse"
  >
    <i-lucide
      [img]="RefreshCwIcon"
      class="icon-xl animate-spin text-blue-600 mb-4"
    ></i-lucide>
    <p class="text-lg font-medium text-gray-700">
      {{ "dashboard.loading" | translate }}
    </p>
  </div>
</div>

<div class="container-padded">
  <!-- Header com indicador de atualização melhorado -->
  <div
    class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-8 gap-4"
  >
    <h1 class="section-title">{{ "dashboard.title" | translate }}</h1>
    <div
      class="flex items-center bg-gray-50 px-4 py-2 rounded-full border shadow-sm"
    >
      <span class="text-sm text-gray-600 mr-3">
        {{ "dashboard.lastUpdated" | translate }}:
        <span class="font-medium text-gray-800">
          {{ lastUpdated | date : "dd/MM/yyyy HH:mm:ss" }}
        </span>
      </span>
      <button
        (click)="refreshRates()"
        class="p-2 rounded-full transition-all duration-200 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
        [title]="'dashboard.refresh' | translate"
        [disabled]="isLoading"
        [class.animate-pulse]="isLoading"
      >
        <i-lucide
          [img]="RefreshCwIcon"
          [class]="
            isLoading
              ? 'icon-md animate-spin text-blue-600'
              : 'icon-md text-gray-600 hover:text-blue-600'
          "
        ></i-lucide>
      </button>
    </div>
  </div>

  <!-- Cards de moedas com destaque melhorado -->
  <div
    class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8"
  >
    @for (currency of currencies; track currency.code) {
    <div
      class="bg-white rounded-xl p-6 shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100"
    >
      <div class="flex justify-between items-start mb-4">
        <div>
          <h3 class="text-lg font-bold text-gray-900">{{ currency.code }}</h3>
          <p class="text-sm text-gray-500">{{ currency.name }}</p>
        </div>
        <div class="p-2 bg-blue-50 rounded-lg">
          <i-lucide
            [img]="currency.icon"
            class="icon-lg text-blue-600"
          ></i-lucide>
        </div>
      </div>
      <div class="flex justify-between items-end">
        <div class="text-2xl font-bold text-gray-900">
          <!-- Exibe o valor com o símbolo da moeda de acordo com o idioma -->
          <ng-container
            *ngIf="translationService.getCurrentLanguage() === 'pt'"
          >
            R$ {{ currency.rate | number : "1.2-4" }}
          </ng-container>
          <ng-container
            *ngIf="translationService.getCurrentLanguage() === 'en'"
          >
            {{ currency.code === "USD" ? "$" : "€" }}
            {{ currency.rate | number : "1.2-4" }}
          </ng-container>
        </div>
        <div
          class="flex items-center px-2 py-1 rounded-full text-sm font-medium"
          [class]="
            currency.change > 0
              ? 'bg-emerald-100 text-emerald-700'
              : currency.change < 0
              ? 'bg-red-100 text-red-700'
              : 'bg-gray-100 text-gray-600'
          "
        >
          <i-lucide
            *ngIf="currency.change !== 0"
            [img]="currency.change > 0 ? TrendingUpIcon : TrendingDownIcon"
            class="icon-sm mr-1"
          ></i-lucide>
          <span
            >{{ currency.change > 0 ? "+" : currency.change < 0 ? "" : "±"
            }}{{ currency.change | number : "1.2-2" }}%</span
          >
        </div>
      </div>
    </div>
    }
  </div>

  <!-- Gráfico -->
  <app-currency-chart
    [historicalRates]="historicalRates"
    [currencyPair]="currencyPair"
    class="mb-8"
  ></app-currency-chart>

  <!-- Cards inferiores -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Card de conversões recentes -->
    <div
      class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
    >
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-bold text-gray-900">
          {{ "dashboard.recentConversions" | translate }}
        </h2>
        <div class="p-2 bg-blue-50 rounded-lg">
          <i-lucide
            [img]="RefreshCwIcon"
            class="icon-md text-blue-600"
          ></i-lucide>
        </div>
      </div>

      <div class="space-y-3">
        @for (conversion of recentConversions; track conversion.timestamp) {
        <div
          class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 hover:from-blue-50 hover:to-blue-100 transition-all duration-200 border border-gray-200 hover:border-blue-200"
        >
          <div class="flex justify-between items-center">
            <div class="flex-1">
              <div class="font-semibold text-gray-900 mb-1">
                <span class="text-blue-600">{{
                  conversion.originalAmount
                }}</span>
                <span class="text-gray-500 mx-2">{{
                  conversion.fromCurrency
                }}</span>
                <span class="text-gray-400">→</span>
                <span class="text-gray-500 mx-2">{{
                  conversion.toCurrency
                }}</span>
              </div>
              <div class="text-sm text-gray-600 flex items-center">
                <i-lucide [img]="RefreshCwIcon" class="icon-xs mr-1"></i-lucide>
                {{ conversion.timestamp | date : "dd/MM/yyyy HH:mm" }}
              </div>
            </div>
            <div class="text-right">
              <div class="text-xl font-bold text-emerald-600">
                {{ conversion.convertedAmount.toFixed(2) }}
              </div>
              <div class="text-sm text-gray-500">
                {{ conversion.toCurrency }}
              </div>
            </div>
          </div>
        </div>
        } @if (recentConversions.length === 0) {
        <div
          class="text-center py-12 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border border-gray-200"
        >
          <div
            class="p-4 bg-gray-200 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center"
          >
            <i-lucide
              [img]="RefreshCwIcon"
              class="icon-lg text-gray-400"
            ></i-lucide>
          </div>
          <p class="text-gray-600 font-medium">
            {{ "dashboard.noRecentConversions" | translate }}
          </p>
          <p class="text-gray-500 text-sm mt-2">
            Suas conversões aparecerão aqui
          </p>
        </div>
        }
      </div>
    </div>

    <!-- Card de uso da API -->
    <div
      class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 relative overflow-hidden"
    >
      <!-- Overlay de carregamento para o card -->
      <div
        *ngIf="isLoading"
        class="absolute inset-0 bg-white/90 backdrop-blur-sm rounded-xl flex items-center justify-center z-10"
      >
        <div class="flex flex-col items-center">
          <div class="relative w-10 h-10 mb-3">
            <div
              class="absolute inset-0 border-3 border-blue-200 rounded-full"
            ></div>
            <div
              class="absolute inset-0 border-3 border-blue-600 rounded-full border-t-transparent animate-spin"
            ></div>
          </div>
          <span class="text-sm text-gray-600 font-medium">Atualizando...</span>
        </div>
      </div>

      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-bold text-gray-900">
          {{ "dashboard.apiUsage" | translate }}
        </h2>
        <div
          class="p-2 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg border border-emerald-200"
        >
          <i-lucide
            [img]="RefreshCwIcon"
            class="icon-md text-emerald-600"
          ></i-lucide>
        </div>
      </div>

      <!-- Barra de progresso melhorada -->
      <div
        class="mb-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 border border-gray-200"
      >
        <div class="flex justify-between items-center mb-3">
          <span class="text-sm font-semibold text-gray-700">{{
            "dashboard.requestsThisMonth" | translate
          }}</span>
          <span
            class="text-sm font-bold text-gray-900 bg-white px-3 py-1 rounded-full shadow-sm"
            >45% (4.500/10.000)</span
          >
        </div>
        <div
          class="w-full bg-gray-300 rounded-full h-4 overflow-hidden shadow-inner"
        >
          <div
            class="bg-gradient-to-r from-blue-500 via-blue-600 to-cyan-500 h-4 rounded-full transition-all duration-1000 ease-out shadow-sm"
            style="width: 45%"
          ></div>
        </div>
      </div>

      <!-- Informações do plano -->
      <div class="space-y-3">
        <div
          class="flex justify-between items-center py-3 px-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl border border-blue-200"
        >
          <div class="text-sm font-medium text-blue-800">
            {{ "dashboard.currentPlan" | translate }}
          </div>
          <div
            class="font-bold text-blue-900 bg-white px-3 py-1 rounded-full shadow-sm"
          >
            Profissional
          </div>
        </div>

        <div
          class="flex justify-between items-center py-3 px-4 bg-gradient-to-r from-amber-50 to-amber-100 rounded-xl border border-amber-200"
        >
          <div class="text-sm font-medium text-amber-800">
            {{ "dashboard.nextRenewal" | translate }}
          </div>
          <div
            class="font-bold text-amber-900 bg-white px-3 py-1 rounded-full shadow-sm"
          >
            15/07/2024
          </div>
        </div>

        <div
          class="flex justify-between items-center py-3 px-4 bg-gradient-to-r from-emerald-50 to-emerald-100 rounded-xl border border-emerald-200"
        >
          <div class="text-sm font-medium text-emerald-800">
            {{ "dashboard.status" | translate }}
          </div>
          <div
            class="px-4 py-2 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white rounded-full text-sm font-bold shadow-md"
          >
            {{ "dashboard.active" | translate }}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
