import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  ElementRef,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { TranslationService } from '../../../../core/services/translation.service';
import { HistoricalRate } from '../../interfaces/currency.interface';

// Importação global do ApexCharts
declare var ApexCharts: any;

interface WeeklyData {
  weekStart: Date;
  weekEnd: Date;
  averageRate: number;
  highestRate: number;
  lowestRate: number;
}

@Component({
  selector: 'app-currency-chart',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './usage-chart.component.html',
})
export class UsageChartComponent
  implements OnInit, OnChanges, AfterViewInit, OnDestroy
{
  @Input() historicalRates: HistoricalRate[] = [];
  @Input() currencyPair: { from: string; to: string } = {
    from: 'BRL',
    to: 'USD',
  };

  // Tipo de visualização (diária ou semanal)
  viewType: 'daily' | 'weekly' = 'daily';

  // Estado de carregamento
  isLoading = false;

  // Estado para verificar se há dados suficientes
  hasInsufficientData = false;

  // Instância do gráfico
  private chart: any;

  // Opções do gráfico
  chartOptions: any;

  constructor(
    private translationService: TranslationService,
    private elementRef: ElementRef
  ) {
    // Inicializa as opções do gráfico com valores padrão
    this.chartOptions = {
      series: [{ name: 'Taxa', data: [] }],
      chart: {
        height: '100%',
        type: 'line',
        zoom: { enabled: false },
        toolbar: { show: false },
      },
    };
  }

  ngOnInit(): void {
    console.log(
      'UsageChartComponent ngOnInit - dados recebidos:',
      this.historicalRates.length
    );
    // Se não houver dados históricos, gera dados de exemplo
    if (this.historicalRates.length === 0) {
      console.log('Gerando dados históricos de exemplo');
      this.generateFakeHistoricalRates();
    }
  }

  ngAfterViewInit(): void {
    // Usamos setTimeout para evitar o erro ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      this.initChart();
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    console.log('ngOnChanges chamado:', changes);
    // Quando os dados históricos mudarem, atualiza o gráfico
    if (changes['historicalRates'] && !changes['historicalRates'].firstChange) {
      console.log('Dados históricos mudaram, atualizando gráfico');
      this.updateChartData();
    }
  }

  ngOnDestroy(): void {
    // Destrói o gráfico quando o componente for destruído
    if (this.chart) {
      try {
        this.chart.destroy();
        this.chart = null;
      } catch (error) {
        console.error('Erro ao destruir gráfico:', error);
      }
    }
  }

  /**
   * Inicializa o gráfico
   */
  private initChart(): void {
    this.isLoading = true;

    // Verifica se o ApexCharts está disponível
    if (typeof ApexCharts === 'undefined') {
      console.error('ApexCharts não está disponível');
      this.isLoading = false;
      return;
    }

    // Prepara os dados para o gráfico
    this.prepareChartOptions();

    // Verifica se o elemento existe antes de criar o gráfico
    const chartElement = this.elementRef.nativeElement.querySelector('#chart');
    if (!chartElement) {
      this.isLoading = false;
      return;
    }

    // Cria o gráfico com tratamento de erro
    if (this.chartOptions) {
      try {
        this.chart = new ApexCharts(chartElement, this.chartOptions);
        this.chart
          .render()
          .then(() => {
            this.isLoading = false;
          })
          .catch((error: any) => {
            console.error('Erro ao renderizar gráfico:', error);
            this.isLoading = false;
          });
      } catch (error) {
        console.error('Erro ao criar gráfico:', error);
        this.isLoading = false;
      }
    } else {
      this.isLoading = false;
    }
  }

  /**
   * Prepara as opções do gráfico
   */
  private prepareChartOptions(): void {
    console.log(`prepareChartOptions chamado para viewType: ${this.viewType}`);

    if (this.historicalRates.length === 0) {
      console.log('Nenhum dado histórico para processar');
      return;
    }

    let processedData: { categories: string[]; seriesData: number[] };

    if (this.viewType === 'weekly') {
      // Verifica se há dados suficientes para visualização semanal
      if (this.historicalRates.length < 7) {
        console.log(
          `Dados insuficientes para visualização semanal: ${this.historicalRates.length} registros`
        );
        this.hasInsufficientData = true;
        return;
      }
      processedData = this.prepareWeeklyData();
    } else {
      console.log('Preparando dados para visualização diária');
      this.hasInsufficientData = false;
      processedData = this.prepareDailyData();
    }

    console.log('Dados processados:', processedData);

    // Verifica se os dados processados são válidos
    if (
      !processedData ||
      processedData.categories.length === 0 ||
      processedData.seriesData.length === 0
    ) {
      console.log('Dados processados são inválidos');
      this.hasInsufficientData = true;
      return;
    }

    console.log('Dados processados são válidos, criando opções do gráfico');
    this.hasInsufficientData = false;

    // Define as opções do gráfico
    this.chartOptions = {
      series: [
        {
          name: `${this.currencyPair.from}/${this.currencyPair.to}`,
          data: processedData.seriesData,
        },
      ],
      chart: {
        height: '100%',
        type: 'line',
        zoom: {
          enabled: true,
          type: 'x',
        },
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: true,
            zoom: true,
            zoomin: true,
            zoomout: true,
            pan: true,
            reset: true,
          },
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800,
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        curve: 'smooth',
        width: 3,
      },
      colors: ['#3B82F6'],
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          gradientToColors: ['#06B6D4'],
          shadeIntensity: 1,
          type: 'horizontal',
          opacityFrom: 0.7,
          opacityTo: 0.9,
        },
      },
      grid: {
        borderColor: '#e7e7e7',
        row: {
          colors: ['#f3f3f3', 'transparent'],
          opacity: 0.1,
        },
        column: {
          colors: ['#f3f3f3', 'transparent'],
          opacity: 0.1,
        },
      },
      xaxis: {
        categories: processedData.categories,
        labels: {
          style: {
            colors: '#64748b',
            fontSize: '11px',
            fontWeight: 500,
          },
          rotate: this.viewType === 'daily' ? -45 : 0,
        },
        axisBorder: {
          show: true,
          color: '#e7e7e7',
        },
        axisTicks: {
          show: true,
          color: '#e7e7e7',
        },
      },
      yaxis: {
        labels: {
          style: {
            colors: '#64748b',
            fontSize: '11px',
            fontWeight: 500,
          },
          formatter: (value: number) => value.toFixed(4),
        },
      },
      tooltip: {
        theme: 'light',
        style: {
          fontSize: '12px',
        },
        x: {
          formatter: (_val: any, opts: any) => {
            const categoryIndex = opts.dataPointIndex;
            const category = processedData.categories[categoryIndex];
            return this.viewType === 'weekly'
              ? `Semana: ${category}`
              : `Data: ${category}`;
          },
        },
        y: {
          formatter: (value: number) =>
            `${value.toFixed(4)} ${this.currencyPair.to}`,
        },
      },
      markers: {
        size: 5,
        colors: ['#fff'],
        strokeColors: '#3B82F6',
        strokeWidth: 2,
        hover: {
          size: 7,
        },
      },
      legend: {
        show: false,
      },
    };
  }

  /**
   * Atualiza os dados do gráfico
   */
  private updateChartData(): void {
    console.log(`updateChartData chamado para viewType: ${this.viewType}`);
    console.log(
      `Número de registros históricos: ${this.historicalRates.length}`
    );

    if (this.historicalRates.length === 0) {
      console.log('Nenhum dado histórico disponível');
      this.isLoading = false;
      return;
    }

    // Verifica se o elemento do gráfico ainda existe
    const chartElement = this.elementRef.nativeElement.querySelector('#chart');
    if (!chartElement) {
      console.log('Elemento #chart não encontrado');
      this.isLoading = false;
      return;
    }

    // Prepara as opções do gráfico
    this.prepareChartOptions();

    // Se os dados são insuficientes, para por aqui
    if (this.hasInsufficientData) {
      console.log('Dados insuficientes detectados');
      this.isLoading = false;
      return;
    }

    // Destrói o gráfico existente e cria um novo para evitar problemas de atualização
    if (this.chart) {
      try {
        this.chart.destroy();
        this.chart = null;
      } catch (error) {
        console.error('Erro ao destruir gráfico existente:', error);
      }
    }

    // Verifica se o ApexCharts está disponível
    if (typeof ApexCharts === 'undefined') {
      console.error('ApexCharts não está disponível');
      this.isLoading = false;
      return;
    }

    // Cria um novo gráfico
    if (this.chartOptions) {
      try {
        this.chart = new ApexCharts(chartElement, this.chartOptions);
        this.chart
          .render()
          .then(() => {
            this.isLoading = false;
          })
          .catch((error: any) => {
            console.error('Erro ao renderizar gráfico:', error);
            this.isLoading = false;
          });
      } catch (error) {
        console.error('Erro ao criar gráfico:', error);
        this.isLoading = false;
      }
    } else {
      this.isLoading = false;
    }
  }

  /**
   * Formata a data de acordo com o tipo de visualização
   */
  private formatDate(date: Date): string {
    const locale =
      this.translationService.getCurrentLanguage() === 'pt' ? 'pt-BR' : 'en-US';

    if (this.viewType === 'daily') {
      return date.toLocaleDateString(locale, {
        day: '2-digit',
        month: '2-digit',
      });
    } else {
      // Para visualização semanal, mostra o dia da semana e a data
      return date.toLocaleDateString(locale, {
        weekday: 'short',
        day: '2-digit',
        month: '2-digit',
      });
    }
  }

  /**
   * Gera dados históricos de exemplo
   */
  private generateFakeHistoricalRates(): void {
    const today = new Date();
    const rates: HistoricalRate[] = [];

    // Gera dados para os últimos 30 dias
    for (let i = 29; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      // Gera uma taxa aleatória entre 4.8 e 5.2
      const baseRate = 5.0;
      const variation = (Math.random() - 0.5) * 0.4;
      const rate = baseRate + variation;

      rates.push({
        from: this.currencyPair.from,
        to: this.currencyPair.to,
        rate,
        timestamp: date,
      });
    }

    this.historicalRates = rates;
  }

  /**
   * Alterna entre visualização diária e semanal
   */
  toggleViewType(type: 'daily' | 'weekly'): void {
    if (this.viewType === type) {
      return; // Evita reprocessamento desnecessário
    }

    console.log(`Alternando para visualização: ${type}`);

    this.viewType = type;
    this.isLoading = true;
    this.hasInsufficientData = false;

    // Força a destruição do gráfico atual antes de criar o novo
    if (this.chart) {
      try {
        this.chart.destroy();
        this.chart = null;
        console.log('Gráfico anterior destruído');
      } catch (error) {
        console.error('Erro ao destruir gráfico anterior:', error);
      }
    }

    // Usa requestAnimationFrame para garantir que a UI seja atualizada antes de processar o gráfico
    requestAnimationFrame(() => {
      console.log(`Iniciando criação do gráfico para: ${type}`);
      this.forceChartRecreation();
    });
  }

  /**
   * Força a recriação completa do gráfico
   */
  private forceChartRecreation(): void {
    console.log('forceChartRecreation iniciado');

    // Verifica se há dados históricos
    if (this.historicalRates.length === 0) {
      console.log('Nenhum dado histórico disponível para recriação');
      this.isLoading = false;
      return;
    }

    // Verifica se o elemento do gráfico existe
    const chartElement = this.elementRef.nativeElement.querySelector('#chart');
    if (!chartElement) {
      console.log('Elemento #chart não encontrado para recriação');
      this.isLoading = false;
      return;
    }

    // Limpa completamente o elemento
    chartElement.innerHTML = '';

    // Prepara as opções do gráfico
    this.prepareChartOptions();

    // Se os dados são insuficientes, para por aqui
    if (this.hasInsufficientData) {
      console.log('Dados insuficientes para recriação');
      this.isLoading = false;
      return;
    }

    // Verifica se o ApexCharts está disponível
    if (typeof ApexCharts === 'undefined') {
      console.error('ApexCharts não está disponível para recriação');
      this.isLoading = false;
      return;
    }

    // Cria um novo gráfico
    if (this.chartOptions) {
      try {
        console.log('Criando novo gráfico com opções:', this.chartOptions);
        this.chart = new ApexCharts(chartElement, this.chartOptions);
        this.chart
          .render()
          .then(() => {
            console.log('Gráfico recriado com sucesso');
            this.isLoading = false;
          })
          .catch((error: any) => {
            console.error('Erro ao renderizar gráfico recriado:', error);
            this.isLoading = false;
          });
      } catch (error) {
        console.error('Erro ao criar gráfico recriado:', error);
        this.isLoading = false;
      }
    } else {
      console.log('Nenhuma opção de gráfico disponível para recriação');
      this.isLoading = false;
    }
  }

  /**
   * Calcula a variação percentual entre a primeira e a última taxa
   */
  getChangePercent(): number {
    if (this.historicalRates.length < 2) {
      return 0;
    }

    // Ordena os dados por data
    const sortedRates = [...this.historicalRates].sort(
      (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
    );

    // Obtém a primeira e a última taxa
    const firstRate = sortedRates[0].rate;
    const lastRate = sortedRates[sortedRates.length - 1].rate;

    // Calcula a variação percentual
    return ((lastRate - firstRate) / firstRate) * 100;
  }

  /**
   * Calcula a taxa média
   */
  getAverageRate(): string {
    if (this.historicalRates.length === 0) {
      return '0.00';
    }

    const sum = this.historicalRates.reduce((acc, item) => acc + item.rate, 0);
    const average = sum / this.historicalRates.length;

    return average.toFixed(4);
  }

  /**
   * Formata a variação percentual com sinal
   */
  getFormattedChangePercent(): string {
    const change = this.getChangePercent();
    const sign = change > 0 ? '+' : '';
    return `${sign}${change.toFixed(2)}%`;
  }

  private prepareDailyData(): { categories: string[]; seriesData: number[] } {
    // Ordena os dados por data
    const sortedRates = [...this.historicalRates].sort(
      (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
    );

    // Prepara os dados para o gráfico
    const categories = sortedRates.map((rate) =>
      this.formatDate(rate.timestamp)
    );
    const seriesData = sortedRates.map((rate) => rate.rate);

    return { categories, seriesData };
  }

  private prepareWeeklyData(): { categories: string[]; seriesData: number[] } {
    // Ordena os dados por data
    const sortedRates = [...this.historicalRates].sort(
      (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
    );

    if (sortedRates.length === 0) {
      return { categories: [], seriesData: [] };
    }

    // Agrupa dados por semana usando um período de 7 dias
    const weeklyGroups: {
      [key: string]: { rates: number[]; startDate: Date };
    } = {};

    sortedRates.forEach((rate) => {
      // Calcula o início da semana (segunda-feira)
      const date = new Date(rate.timestamp);
      const dayOfWeek = date.getDay(); // 0 = domingo, 1 = segunda, etc.
      const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Ajusta para segunda-feira

      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - daysToMonday);
      weekStart.setHours(0, 0, 0, 0);

      // Cria uma chave única para a semana baseada na data de início
      const weekKey = weekStart.toISOString().split('T')[0];

      if (!weeklyGroups[weekKey]) {
        weeklyGroups[weekKey] = {
          rates: [],
          startDate: new Date(weekStart),
        };
      }

      weeklyGroups[weekKey].rates.push(rate.rate);
    });

    const categories: string[] = [];
    const seriesData: number[] = [];

    // Converte o objeto em arrays ordenados
    const sortedWeeks = Object.entries(weeklyGroups).sort(([a], [b]) =>
      a.localeCompare(b)
    );

    sortedWeeks.forEach(([, data]) => {
      // Calcula a média semanal
      const averageRate =
        data.rates.reduce((sum, rate) => sum + rate, 0) / data.rates.length;

      const locale =
        this.translationService.getCurrentLanguage() === 'pt'
          ? 'pt-BR'
          : 'en-US';

      // Cria label baseado na data de início da semana
      const weekStart = data.startDate;
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);

      const startStr = weekStart.toLocaleDateString(locale, {
        day: '2-digit',
        month: '2-digit',
      });
      const endStr = weekEnd.toLocaleDateString(locale, {
        day: '2-digit',
        month: '2-digit',
      });

      categories.push(`${startStr} - ${endStr}`);
      seriesData.push(averageRate);
    });

    return { categories, seriesData };
  }

  /**
   * Obtém a taxa atual (mais recente)
   */
  getCurrentRate(): number {
    if (this.historicalRates.length === 0) {
      return 0;
    }

    const sortedRates = [...this.historicalRates].sort(
      (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
    );

    return sortedRates[0].rate;
  }

  /**
   * Obtém a maior taxa do período
   */
  getHighestRate(): number {
    if (this.historicalRates.length === 0) {
      return 0;
    }

    return Math.max(...this.historicalRates.map((rate) => rate.rate));
  }

  /**
   * Obtém a data da maior taxa
   */
  getHighestRateDate(): string {
    if (this.historicalRates.length === 0) {
      return '';
    }

    const highestRate = this.getHighestRate();
    const rateWithHighest = this.historicalRates.find(
      (rate) => rate.rate === highestRate
    );

    if (!rateWithHighest) {
      return '';
    }

    const locale =
      this.translationService.getCurrentLanguage() === 'pt' ? 'pt-BR' : 'en-US';

    return rateWithHighest.timestamp.toLocaleDateString(locale, {
      day: '2-digit',
      month: '2-digit',
      year: '2-digit',
    });
  }
}
