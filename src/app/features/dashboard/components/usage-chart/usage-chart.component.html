<div class="bg-white shadow-lg rounded-xl p-6 border border-gray-100">
  <div
    class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4"
  >
    <h2 class="text-xl font-bold text-gray-900">Histórico de Taxas</h2>
    <div class="flex bg-gray-100 rounded-lg p-1">
      <button
        class="px-4 py-2 text-sm font-medium rounded-md transition-all duration-200"
        [class]="
          viewType === 'daily'
            ? 'bg-white text-blue-600 shadow-sm'
            : 'text-gray-600 hover:text-gray-900'
        "
        (click)="toggleViewType('daily')"
      >
        Diário
      </button>
      <button
        class="px-4 py-2 text-sm font-medium rounded-md transition-all duration-200"
        [class]="
          viewType === 'weekly'
            ? 'bg-white text-blue-600 shadow-sm'
            : 'text-gray-600 hover:text-gray-900'
        "
        (click)="toggleViewType('weekly')"
      >
        Semanal
      </button>
    </div>
  </div>

  <!-- Estado de carregamento -->
  <div *ngIf="isLoading" class="h-[400px] flex items-center justify-center">
    <div class="flex flex-col items-center">
      <div class="relative w-12 h-12 mb-4">
        <div
          class="absolute inset-0 border-4 border-blue-200 rounded-full"
        ></div>
        <div
          class="absolute inset-0 border-4 border-blue-600 rounded-full border-t-transparent animate-spin"
        ></div>
      </div>
      <p class="text-gray-600">Carregando dados do gráfico...</p>
    </div>
  </div>

  <!-- Estado de dados insuficientes -->
  <div
    *ngIf="!isLoading && hasInsufficientData"
    class="h-[400px] flex items-center justify-center"
  >
    <div
      class="flex flex-col items-center text-center bg-gradient-to-br from-amber-50 to-orange-50 rounded-xl p-8 border border-amber-200 max-w-md"
    >
      <div class="p-4 bg-amber-100 rounded-full mb-4">
        <svg
          class="w-12 h-12 text-amber-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 18.5c-.77.833.192 2.5 1.732 2.5z"
          ></path>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-amber-900 mb-2">
        Dados Insuficientes
      </h3>
      <p class="text-amber-800 mb-3" *ngIf="viewType === 'weekly'">
        Para visualização semanal são necessários pelo menos 7 dias de dados
        históricos.
      </p>
      <p class="text-amber-800 mb-3" *ngIf="viewType === 'daily'">
        Nenhum dado histórico disponível para exibir o gráfico.
      </p>
      <p class="text-sm text-amber-700">
        Atualmente você tem {{ historicalRates.length }}
        {{ historicalRates.length === 1 ? "registro" : "registros" }}.
        <span *ngIf="viewType === 'weekly' && historicalRates.length > 0">
          Tente a visualização diária ou aguarde mais dados.
        </span>
        <span *ngIf="historicalRates.length === 0">
          Os dados aparecerão conforme você usar a API de cotações.
        </span>
      </p>
    </div>
  </div>

  <!-- Gráfico -->
  <div class="h-[400px] w-full mb-6">
    <div class="h-full w-full">
      <div
        id="chart"
        class="h-full w-full"
        [style.display]="isLoading || hasInsufficientData ? 'none' : 'block'"
      ></div>
    </div>
  </div>

  <!-- Estatísticas -->
  <div
    *ngIf="!isLoading && !hasInsufficientData"
    class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4"
  >
    <div
      class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200"
    >
      <h3 class="text-sm font-medium text-blue-800 mb-1">Taxa Atual</h3>
      <p class="text-2xl font-bold text-blue-900">
        {{
          historicalRates.length > 0 ? getCurrentRate().toFixed(4) : "0.0000"
        }}
      </p>
      <p class="text-xs text-blue-600 mt-1">
        {{ currencyPair.from }}/{{ currencyPair.to }}
      </p>
    </div>

    <div
      class="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-xl p-4 border border-emerald-200"
    >
      <h3 class="text-sm font-medium text-emerald-800 mb-1">Taxa Média</h3>
      <p class="text-2xl font-bold text-emerald-900">
        {{ getAverageRate() }}
      </p>
      <p class="text-xs text-emerald-600 mt-1">
        Período {{ viewType === "daily" ? "diário" : "semanal" }}
      </p>
    </div>

    <div
      class="bg-gradient-to-br from-violet-50 to-violet-100 rounded-xl p-4 border border-violet-200"
    >
      <h3 class="text-sm font-medium text-violet-800 mb-1">Variação</h3>
      <p
        class="text-2xl font-bold"
        [class.text-emerald-700]="getChangePercent() > 0"
        [class.text-rose-700]="getChangePercent() < 0"
        [class.text-violet-900]="getChangePercent() === 0"
      >
        {{ getFormattedChangePercent() }}
      </p>
      <p class="text-xs text-violet-600 mt-1">Comparado ao início</p>
    </div>

    <div
      class="bg-gradient-to-br from-amber-50 to-amber-100 rounded-xl p-4 border border-amber-200"
    >
      <h3 class="text-sm font-medium text-amber-800 mb-1">
        {{ viewType === "daily" ? "Maior Alta" : "Melhor Semana" }}
      </h3>
      <p class="text-2xl font-bold text-amber-900">
        {{ getHighestRate().toFixed(4) }}
      </p>
      <p class="text-xs text-amber-600 mt-1">
        {{ getHighestRateDate() }}
      </p>
    </div>
  </div>
</div>
