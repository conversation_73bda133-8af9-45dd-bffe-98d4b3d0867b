<div class="bg-white shadow rounded-lg p-6">
  <h2 class="text-lg font-medium text-gray-900 mb-4"><PERSON>sumo de Uso</h2>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
    @for (metric of metrics; track metric.title) {
    <div class="bg-gray-50 rounded-lg p-4">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm text-gray-600">{{ metric.title }}</p>
          <p
            class="text-2xl font-semibold mt-1"
            [ngClass]="getStatusColor(metric.status)"
          >
            {{ metric.value }}
          </p>
        </div>
        <div class="p-2 bg-white rounded-full">
          <ng-container *ngComponentOutlet="metric.icon" />
        </div>
      </div>
      <p class="text-sm text-gray-500 mt-2">{{ metric.description }}</p>
    </div>
    }
  </div>

  <div class="mt-6">
    <div class="flex items-center justify-between">
      <h3 class="text-sm font-medium text-gray-900">Limite de Uso</h3>
      <span class="text-sm text-gray-500">12.34% do limite mensal</span>
    </div>
    <div class="mt-2 w-full bg-gray-200 rounded-full h-2">
      <div class="bg-blue-600 h-2 rounded-full" style="width: 12.34%"></div>
    </div>
  </div>
</div>
