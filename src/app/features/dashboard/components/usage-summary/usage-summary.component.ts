import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import {
  Activity,
  AlertCircle,
  Clock,
  LucideAngularModule,
  TrendingUp,
} from 'lucide-angular';

interface UsageMetric {
  title: string;
  value: string;
  description: string;
  icon: any;
  status: 'normal' | 'warning' | 'critical';
}

@Component({
  selector: 'app-usage-summary',
  standalone: true,
  imports: [CommonModule, LucideAngularModule],
  templateUrl: './usage-summary.component.html',
})
export class UsageSummaryComponent {
  metrics: UsageMetric[] = [
    {
      title: 'Requisições Hoje',
      value: '1,234',
      description: 'De um total de 10,000',
      icon: Activity,
      status: 'normal',
    },
    {
      title: 'Tempo Médio de Resposta',
      value: '45ms',
      description: 'Últimas 24 horas',
      icon: Clock,
      status: 'normal',
    },
    {
      title: 'Erros',
      value: '12',
      description: 'Taxa de erro: 0.97%',
      icon: AlertCircle,
      status: 'warning',
    },
    {
      title: 'Crescimento',
      value: '+15%',
      description: 'Comparado ao mês anterior',
      icon: TrendingUp,
      status: 'normal',
    },
  ];

  getStatusColor(status: string): string {
    switch (status) {
      case 'normal':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'critical':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  }
}
