/* Estilos específicos para o componente dashboard */

/* Animação suave para a barra de progresso */
.progress-bar {
  animation: progressAnimation 1s ease-out;
}

@keyframes progressAnimation {
  from {
    width: 0%;
  }
}

/* Animação de fade-in para o loading */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Transições suaves para cards */
.card-hover-effect {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Animação para o spinner customizado */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Gradient background para cards de estatísticas */
.stat-card-gradient {
  background: linear-gradient(
    135deg,
    var(--from-color) 0%,
    var(--to-color) 100%
  );
}
