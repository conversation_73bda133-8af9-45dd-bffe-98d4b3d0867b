/**
 * Interface para representar uma moeda
 */
export interface Currency {
  /** <PERSON><PERSON><PERSON> da moeda (ex: USD, EUR, BRL) */
  code: string;

  /** Nome da moeda */
  name: string;

  /** Símbolo da moeda */
  symbol: string;

  /** T<PERSON>o da moeda (fiat ou crypto) */
  type: 'fiat' | 'crypto';
}

/**
 * Interface para representar a resposta da API de moedas
 */
export interface CurrencyResponse {
  /** Lista de moedas */
  moedas: Currency[];

  /** Timestamp da última atualização */
  timestamp: string;
}

/**
 * Interface para representar uma taxa de câmbio
 */
export interface CurrencyRate {
  /** Código da moeda (ex: USD, EUR, BRL) */
  code: string;

  /** Nome da moeda */
  name: string;

  /** Taxa de câmbio em relação à moeda base */
  rate: number;

  /** Variação percentual da taxa */
  change: number;

  /** <PERSON>cone da moeda */
  icon?: any;

  /** Taxa anterior (para cálculo de variação) */
  previousRate?: number;

  /** Data da taxa anterior */
  previousRateDate?: Date;
}

/**
 * Interface para representar um registro histórico de taxa de câmbio
 */
export interface HistoricalRate {
  /** Código da moeda de origem */
  from: string;

  /** Código da moeda de destino */
  to: string;

  /** Taxa de câmbio */
  rate: number;

  /** Data e hora do registro */
  timestamp: Date;
}

/**
 * Interface para representar os parâmetros de consulta de cotação
 */
export interface CotacaoQueryParams {
  /** Moeda de origem */
  from?: string;

  /** Moeda de destino */
  to?: string;

  /** Valor a ser convertido */
  amount?: number;

  /** Data da cotação (formato: YYYY-MM-DD) */
  date?: string;
}

/**
 * Interface para representar a resposta da API de cotação
 */
export interface CotacaoResponse {
  /** Moeda de origem */
  from: string;

  /** Moeda de destino */
  to: string;

  /** Valor a ser convertido */
  amount: number;

  /** Valor convertido */
  result: number;

  /** Taxa de câmbio */
  rate: number;

  /** Timestamp da cotação */
  timestamp: string;

  /** Variação percentual da taxa nas últimas 24h */
  change_24h?: number;
}

/**
 * Interface para representar uma conversão recente
 */
export interface RecentConversion {
  /** Moeda de origem */
  fromCurrency: string;

  /** Moeda de destino */
  toCurrency: string;

  /** Valor original */
  originalAmount: number;

  /** Valor convertido */
  convertedAmount: number;

  /** Data e hora da conversão */
  timestamp: Date;
}
