<div class="container-padded">
  <div class="flex-between mb-8">
    <h1 class="section-title">Gerenciar Chaves de API</h1>
    <button (click)="createNewKey()" class="btn-primary">
      <i-lucide [img]="PlusIcon" class="icon-sm"></i-lucide>
      Nova Chave API
    </button>
  </div>

  <div class="card mb-6">
    <div class="flex-between mb-4">
      <h2 class="card-title mb-0">Suas Chaves de API</h2>
      <p class="text-muted">Total: {{ apiKeys.length }}</p>
    </div>
    
    <p class="text-muted mb-6">
      As chaves de API são usadas para autenticar suas solicitações à API CurrencyWise. 
      Mantenha suas chaves seguras e não as compartilhe publicamente.
    </p>

    @if (apiKeys.length === 0) {
      <div class="bg-gray-50 rounded-lg p-8 text-center">
        <i-lucide [img]="KeyIcon" class="w-12 h-12 text-gray-400 mx-auto mb-4"></i-lucide>
        <h3 class="text-lg font-medium mb-2">Nenhuma chave API encontrada</h3>
        <p class="text-muted mb-4">Você ainda não criou nenhuma chave API.</p>
        <button (click)="createNewKey()" class="btn-primary mx-auto">
          <i-lucide [img]="PlusIcon" class="icon-sm"></i-lucide>
          Criar Primeira Chave
        </button>
      </div>
    } @else {
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 text-left">
            <tr>
              <th class="px-4 py-3 text-sm font-medium text-gray-500">Nome</th>
              <th class="px-4 py-3 text-sm font-medium text-gray-500">Chave</th>
              <th class="px-4 py-3 text-sm font-medium text-gray-500">Criada em</th>
              <th class="px-4 py-3 text-sm font-medium text-gray-500">Último uso</th>
              <th class="px-4 py-3 text-sm font-medium text-gray-500">Ações</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            @for (key of apiKeys; track key.id) {
              <tr class="hover:bg-gray-50">
                <td class="px-4 py-4">
                  <div class="font-medium">{{ key.name }}</div>
                </td>
                <td class="px-4 py-4">
                  <div class="flex-center gap-2">
                    @if (visibleKeys[key.id]) {
                      <code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono">{{ key.key }}</code>
                    } @else {
                      <code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono">•••••••••••••••••••••••••••••••</code>
                    }
                    <button 
                      (click)="toggleKeyVisibility(key.id)" 
                      class="text-gray-500 hover:text-gray-700"
                      [title]="visibleKeys[key.id] ? 'Ocultar chave' : 'Mostrar chave'"
                    >
                      <i-lucide [img]="visibleKeys[key.id] ? EyeOffIcon : EyeIcon" class="icon-sm"></i-lucide>
                    </button>
                    <button 
                      (click)="copyKey(key.id, key.key)" 
                      class="text-gray-500 hover:text-gray-700"
                      title="Copiar chave"
                    >
                      <i-lucide [img]="copiedKeys[key.id] ? CheckIcon : CopyIcon" class="icon-sm"></i-lucide>
                    </button>
                  </div>
                </td>
                <td class="px-4 py-4 text-muted">
                  {{ key.created | date:'dd/MM/yyyy' }}
                </td>
                <td class="px-4 py-4 text-muted">
                  {{ key.lastUsed ? (key.lastUsed | date:'dd/MM/yyyy HH:mm') : 'Nunca usada' }}
                </td>
                <td class="px-4 py-4">
                  <button 
                    (click)="deleteKey(key.id)" 
                    class="text-red-500 hover:text-red-700"
                    title="Excluir chave"
                  >
                    <i-lucide [img]="TrashIcon" class="icon-sm"></i-lucide>
                  </button>
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>
    }
  </div>

  <div class="card">
    <h2 class="card-title">Segurança das Chaves API</h2>
    <div class="space-y-4">
      <div class="bg-blue-50 p-4 rounded-lg">
        <h3 class="font-medium text-blue-800 mb-2">Melhores Práticas</h3>
        <ul class="list-disc pl-5 space-y-1 text-blue-700">
          <li>Nunca compartilhe suas chaves API em repositórios públicos ou código-fonte</li>
          <li>Utilize variáveis de ambiente para armazenar suas chaves API</li>
          <li>Revogue imediatamente chaves comprometidas</li>
          <li>Use chaves diferentes para ambientes de produção e desenvolvimento</li>
        </ul>
      </div>
      
      <div>
        <h3 class="font-medium mb-2">Limites de Uso</h3>
        <p class="text-muted mb-2">
          Cada chave API está sujeita aos limites do seu plano atual. Monitore o uso para evitar interrupções.
        </p>
        <a href="/subscriptions" class="text-blue-600 hover:text-blue-800 font-medium">
          Ver detalhes do plano atual →
        </a>
      </div>
    </div>
  </div>
</div>
