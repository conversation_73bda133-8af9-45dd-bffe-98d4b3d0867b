import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Globe, Lock, Shield } from 'lucide-angular';

interface SecurityRule {
  id: string;
  type: 'ip' | 'domain' | 'referer';
  value: string;
  description: string;
}

@Component({
  selector: 'app-api-key-security-settings',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './api-key-security-settings.component.html',
})
export class ApiKeySecuritySettingsComponent implements OnInit {
  securityForm: FormGroup;
  securityRules: SecurityRule[] = [];
  isAddingRule = false;
  newRuleType: 'ip' | 'domain' | 'referer' = 'ip';
  newRuleValue = '';

  constructor(private fb: FormBuilder) {
    this.securityForm = this.fb.group({
      rateLimit: ['1000', [Validators.required, Validators.min(1)]],
      maxRequestsPerMinute: ['60', [Validators.required, Validators.min(1)]],
      requireHttps: [true],
      enableCors: [true],
      allowedOrigins: ['*'],
    });
  }

  ngOnInit(): void {
    this.loadSecurityRules();
  }

  private loadSecurityRules(): void {
    // Dados fake para demonstração
    this.securityRules = [
      {
        id: '1',
        type: 'ip',
        value: '***********',
        description: 'IP do servidor de produção',
      },
      {
        id: '2',
        type: 'domain',
        value: 'api.meudominio.com',
        description: 'Domínio principal da API',
      },
      {
        id: '3',
        type: 'referer',
        value: 'https://meudominio.com',
        description: 'Site principal',
      },
    ];
  }

  addRule(): void {
    if (!this.newRuleValue.trim()) return;

    const newRule: SecurityRule = {
      id: Date.now().toString(),
      type: this.newRuleType,
      value: this.newRuleValue,
      description: this.getDefaultDescription(this.newRuleType),
    };

    this.securityRules.push(newRule);
    this.isAddingRule = false;
    this.newRuleValue = '';
  }

  removeRule(ruleId: string): void {
    this.securityRules = this.securityRules.filter(
      (rule) => rule.id !== ruleId
    );
  }

  private getDefaultDescription(type: 'ip' | 'domain' | 'referer'): string {
    switch (type) {
      case 'ip':
        return 'Endereço IP permitido';
      case 'domain':
        return 'Domínio permitido';
      case 'referer':
        return 'Referer permitido';
    }
  }

  getRuleIcon(type: 'ip' | 'domain' | 'referer'): any {
    switch (type) {
      case 'ip':
        return Globe;
      case 'domain':
        return Shield;
      case 'referer':
        return Lock;
    }
  }

  onSubmit(): void {
    if (this.securityForm.valid) {
      console.log('Configurações salvas:', this.securityForm.value);
      // Aqui você implementaria a lógica para salvar as configurações
    }
  }
}
