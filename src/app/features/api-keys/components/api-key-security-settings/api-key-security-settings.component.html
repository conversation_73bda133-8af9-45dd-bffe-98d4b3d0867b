<div class="bg-white shadow rounded-lg p-6">
  <h2 class="text-lg font-medium text-gray-900 mb-6">
    Configurações de Segurança
  </h2>

  <form [formGroup]="securityForm" (ngSubmit)="onSubmit()" class="space-y-6">
    <!-- Limites de Requisição -->
    <div class="space-y-4">
      <h3 class="text-base font-medium text-gray-900">Limites de Requisição</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="rateLimit" class="block text-sm font-medium text-gray-700"
            >Limite Diário</label
          >
          <div class="mt-1 relative rounded-md shadow-sm">
            <input
              type="number"
              id="rateLimit"
              formControlName="rateLimit"
              class="block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>
        </div>
        <div>
          <label
            for="maxRequestsPerMinute"
            class="block text-sm font-medium text-gray-700"
            >Máximo por Minuto</label
          >
          <div class="mt-1 relative rounded-md shadow-sm">
            <input
              type="number"
              id="maxRequestsPerMinute"
              formControlName="maxRequestsPerMinute"
              class="block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Configurações de Segurança -->
    <div class="space-y-4">
      <h3 class="text-base font-medium text-gray-900">
        Configurações de Segurança
      </h3>
      <div class="space-y-4">
        <div class="flex items-center">
          <input
            type="checkbox"
            id="requireHttps"
            formControlName="requireHttps"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label for="requireHttps" class="ml-2 block text-sm text-gray-900">
            Exigir HTTPS para todas as requisições
          </label>
        </div>
        <div class="flex items-center">
          <input
            type="checkbox"
            id="enableCors"
            formControlName="enableCors"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label for="enableCors" class="ml-2 block text-sm text-gray-900">
            Habilitar CORS
          </label>
        </div>
        <div>
          <label
            for="allowedOrigins"
            class="block text-sm font-medium text-gray-700"
            >Origens Permitidas</label
          >
          <div class="mt-1">
            <input
              type="text"
              id="allowedOrigins"
              formControlName="allowedOrigins"
              class="block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="* ou lista de domínios separados por vírgula"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Regras de Segurança -->
    <div class="space-y-4">
      <div class="flex justify-between items-center">
        <h3 class="text-base font-medium text-gray-900">Regras de Segurança</h3>
        <button
          type="button"
          (click)="isAddingRule = true"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Adicionar Regra
        </button>
      </div>

      @if (isAddingRule) {
      <div class="bg-gray-50 p-4 rounded-lg">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700"
              >Tipo de Regra</label
            >
            <select
              [(ngModel)]="newRuleType"
              [ngModelOptions]="{ standalone: true }"
              class="mt-1 block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            >
              <option value="ip">Endereço IP</option>
              <option value="domain">Domínio</option>
              <option value="referer">Referer</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Valor</label>
            <input
              type="text"
              [(ngModel)]="newRuleValue"
              [ngModelOptions]="{ standalone: true }"
              class="mt-1 block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="Digite o valor..."
            />
          </div>
        </div>
        <div class="mt-4 flex justify-end space-x-3">
          <button
            type="button"
            (click)="isAddingRule = false"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancelar
          </button>
          <button
            type="button"
            (click)="addRule()"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Adicionar
          </button>
        </div>
      </div>
      }

      <div class="space-y-3">
        @for (rule of securityRules; track rule.id) {
        <div
          class="flex items-center justify-between bg-gray-50 p-4 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <ng-container *ngComponentOutlet="getRuleIcon(rule.type)" />
            <div>
              <p class="text-sm font-medium text-gray-900">{{ rule.value }}</p>
              <p class="text-sm text-gray-500">{{ rule.description }}</p>
            </div>
          </div>
          <button
            type="button"
            (click)="removeRule(rule.id)"
            class="text-red-600 hover:text-red-900"
          >
            <AlertCircle class="h-5 w-5" />
          </button>
        </div>
        }
      </div>
    </div>

    <div class="flex justify-end">
      <button
        type="submit"
        [disabled]="!securityForm.valid"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Salvar Configurações
      </button>
    </div>
  </form>
</div>
