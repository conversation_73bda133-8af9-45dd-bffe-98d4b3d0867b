import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import {
  CheckIcon,
  CopyIcon,
  EyeIcon,
  EyeOffIcon,
  KeyIcon,
  LucideAngularModule,
  PlusIcon,
  TrashIcon,
} from 'lucide-angular';
import { ApiKey } from '../interfaces/api-key.interface';

@Component({
  selector: 'app-api-keys',
  standalone: true,
  imports: [CommonModule, LucideAngularModule],
  templateUrl: './api-keys.component.html',
  styleUrl: './api-keys.component.css',
})
export class ApiKeysComponent {
  readonly KeyIcon = KeyIcon;
  readonly PlusIcon = PlusIcon;
  readonly CopyIcon = CopyIcon;
  readonly TrashIcon = TrashIcon;
  readonly EyeIcon = EyeIcon;
  readonly EyeOffIcon = EyeOffIcon;
  readonly CheckIcon = CheckIcon;

  apiKeys: ApiKey[] = [
    {
      id: '1',
      name: 'Produção',
      key: 'pk_live_51NxSamCEwQnBPjTZGU6wYON8KvEqLzSUwPJgCEwQnBPjTZ',
      created: new Date('2024-01-15'),
      lastUsed: new Date('2024-06-09'),
    },
    {
      id: '2',
      name: 'Desenvolvimento',
      key: 'pk_test_51NxSamCEwQnBPjTZGU6wYON8KvEqLzSUwPJgCEwQnBPjTZ',
      created: new Date('2024-02-20'),
      lastUsed: new Date('2024-06-10'),
    },
    {
      id: '3',
      name: 'Testes',
      key: 'pk_test_51NxSamCEwQnBPjTZGU6wYON8KvEqLzSUwPJgCEwQnBPjTZ',
      created: new Date('2024-03-05'),
      lastUsed: null,
    },
  ];

  visibleKeys: { [key: string]: boolean } = {};
  copiedKeys: { [key: string]: boolean } = {};

  toggleKeyVisibility(id: string) {
    this.visibleKeys[id] = !this.visibleKeys[id];
  }

  copyKey(id: string, key: string) {
    navigator.clipboard.writeText(key).then(() => {
      this.copiedKeys[id] = true;
      setTimeout(() => {
        this.copiedKeys[id] = false;
      }, 2000);
    });
  }

  deleteKey(id: string) {
    if (
      confirm(
        'Tem certeza que deseja excluir esta chave API? Esta ação não pode ser desfeita.'
      )
    ) {
      this.apiKeys = this.apiKeys.filter((key) => key.id !== id);
    }
  }

  createNewKey() {
    // Aqui seria implementada a lógica para criar uma nova chave API
    alert(
      'Funcionalidade de criação de nova chave API será implementada em breve.'
    );
  }
}
