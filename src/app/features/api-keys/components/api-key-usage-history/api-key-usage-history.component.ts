import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { NgxChartsModule } from '@swimlane/ngx-charts';

interface UsageRecord {
  date: string;
  requests: number;
  errors: number;
  avgResponseTime: number;
}

@Component({
  selector: 'app-api-key-usage-history',
  standalone: true,
  imports: [CommonModule, NgxChartsModule],
  templateUrl: './api-key-usage-history.component.html',
})
export class ApiKeyUsageHistoryComponent implements OnInit {
  usageHistory: UsageRecord[] = [];
  selectedTimeRange: 'day' | 'week' | 'month' = 'week';

  // Configurações do gráfico
  view: [number, number] = [700, 400];
  showXAxis = true;
  showYAxis = true;
  gradient = false;
  showLegend = true;
  showXAxisLabel = true;
  xAxisLabel = 'Data';
  showYAxisLabel = true;
  yAxisLabel = 'Requisições';
  colorScheme = {
    domain: ['#3B82F6', '#EF4444'],
  };

  ngOnInit(): void {
    this.generateFakeData();
  }

  private generateFakeData(): void {
    const today = new Date();
    const days =
      this.selectedTimeRange === 'day'
        ? 24
        : this.selectedTimeRange === 'week'
        ? 7
        : 30;

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      if (this.selectedTimeRange === 'day') {
        date.setHours(date.getHours() - i);
      } else {
        date.setDate(date.getDate() - i);
      }

      this.usageHistory.push({
        date:
          this.selectedTimeRange === 'day'
            ? date.toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit',
              })
            : date.toLocaleDateString('pt-BR', { weekday: 'short' }),
        requests: Math.floor(Math.random() * 1000) + 100,
        errors: Math.floor(Math.random() * 50),
        avgResponseTime: Math.floor(Math.random() * 200) + 50,
      });
    }
  }

  changeTimeRange(range: 'day' | 'week' | 'month'): void {
    this.selectedTimeRange = range;
    this.usageHistory = [];
    this.generateFakeData();
  }

  getTotalRequests(): number {
    return this.usageHistory.reduce((sum, record) => sum + record.requests, 0);
  }

  getTotalErrors(): number {
    return this.usageHistory.reduce((sum, record) => sum + record.errors, 0);
  }

  getAverageResponseTime(): number {
    const total = this.usageHistory.reduce(
      (sum, record) => sum + record.avgResponseTime,
      0
    );
    return Math.round(total / this.usageHistory.length);
  }
}
