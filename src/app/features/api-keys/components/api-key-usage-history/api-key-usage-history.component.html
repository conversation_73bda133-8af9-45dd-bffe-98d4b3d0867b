<div class="bg-white shadow rounded-lg p-6">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-lg font-medium text-gray-900">Histórico de Uso</h2>
    <div class="flex space-x-2">
      <button
        (click)="changeTimeRange('day')"
        [class.bg-blue-50]="selectedTimeRange === 'day'"
        [class.text-blue-600]="selectedTimeRange === 'day'"
        class="px-4 py-2 text-sm font-medium rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        Hoje
      </button>
      <button
        (click)="changeTimeRange('week')"
        [class.bg-blue-50]="selectedTimeRange === 'week'"
        [class.text-blue-600]="selectedTimeRange === 'week'"
        class="px-4 py-2 text-sm font-medium rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        Semana
      </button>
      <button
        (click)="changeTimeRange('month')"
        [class.bg-blue-50]="selectedTimeRange === 'month'"
        [class.text-blue-600]="selectedTimeRange === 'month'"
        class="px-4 py-2 text-sm font-medium rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        Mês
      </button>
    </div>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
    <div class="bg-gray-50 rounded-lg p-4">
      <h3 class="text-sm font-medium text-gray-900">Total de Requisições</h3>
      <p class="mt-2 text-3xl font-semibold text-blue-600">
        {{ getTotalRequests() | number }}
      </p>
    </div>
    <div class="bg-gray-50 rounded-lg p-4">
      <h3 class="text-sm font-medium text-gray-900">Total de Erros</h3>
      <p class="mt-2 text-3xl font-semibold text-red-600">
        {{ getTotalErrors() | number }}
      </p>
    </div>
    <div class="bg-gray-50 rounded-lg p-4">
      <h3 class="text-sm font-medium text-gray-900">Tempo Médio de Resposta</h3>
      <p class="mt-2 text-3xl font-semibold text-green-600">
        {{ getAverageResponseTime() }}ms
      </p>
    </div>
  </div>

  <div class="h-[400px]">
    <ngx-charts-bar-vertical-2d
      [view]="view"
      [scheme]="colorScheme"
      [results]="usageHistory"
      [gradient]="gradient"
      [xAxis]="showXAxis"
      [yAxis]="showYAxis"
      [legend]="showLegend"
      [showXAxisLabel]="showXAxisLabel"
      [showYAxisLabel]="showYAxisLabel"
      [xAxisLabel]="xAxisLabel"
      [yAxisLabel]="yAxisLabel"
    >
    </ngx-charts-bar-vertical-2d>
  </div>

  <div class="mt-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Detalhes por Período</h3>
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Data
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Requisições
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Erros
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Tempo Médio
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          @for (record of usageHistory; track record.date) {
          <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ record.date }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ record.requests | number }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ record.errors | number }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ record.avgResponseTime }}ms
            </td>
          </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
</div>
