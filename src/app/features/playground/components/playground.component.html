<div class="container-padded">
  <div class="flex-between mb-8">
    <h1 class="section-title">API Playground</h1>
    <a routerLink="/documentation" class="btn-primary">
      <i-lucide [img]="CodeIcon" class="icon-sm"></i-lucide>
      Ver Documentação
    </a>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Painel de Requisição -->
    <div class="card">
      <h2 class="card-title">Requisição</h2>

      <!-- Seleção de Endpoint -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-1"
          >Endpoint</label
        >
        <select
          [(ngModel)]="selectedEndpoint"
          (change)="onEndpointChange()"
          class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
        >
          @for (endpoint of endpoints; track endpoint.id) {
          <option [value]="endpoint.id">
            {{ endpoint.method }} {{ endpoint.path }} - {{ endpoint.name }}
          </option>
          }
        </select>
        <p class="mt-1 text-muted">
          {{ getSelectedEndpointDescription() }}
        </p>
      </div>

      <!-- Chave API -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-1"
          >Chave API</label
        >
        <input
          type="text"
          [(ngModel)]="apiKey"
          class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          placeholder="Sua chave API"
        />
        <p class="mt-1 text-muted">
          Você pode obter sua chave API na seção
          <a routerLink="/api-keys" class="text-blue-600 hover:text-blue-800"
            >API Keys</a
          >.
        </p>
      </div>

      <!-- Parâmetros -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-3">Parâmetros</h3>

        @for (param of getSelectedEndpointParams(); track param.name) {
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">
            {{ param.name }}
            @if (param.required) {
            <span class="text-red-500">*</span>
            }
          </label>

          @if (param.options) {
          <select
            [(ngModel)]="paramValues[param.name]"
            class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            @for (option of param.options; track option) {
            <option [value]="option">{{ option }}</option>
            }
          </select>
          } @else {
          <input
            [type]="param.type === 'number' ? 'number' : 'text'"
            [(ngModel)]="paramValues[param.name]"
            class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            [placeholder]="param.default || ''"
          />
          }

          <p class="mt-1 text-muted">{{ param.description }}</p>
        </div>
        }
      </div>

      <!-- URL da Requisição -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-2">URL da Requisição</h3>
        <div class="flex-between">
          <div class="code-block flex-grow">
            <code>{{ getRequestUrl() }}</code>
          </div>
          <button
            (click)="copyRequestCode()"
            class="ml-2 p-2 text-gray-500 hover:text-gray-700 rounded-md"
            title="Copiar código cURL"
          >
            <i-lucide
              [img]="copied ? CheckIcon : CopyIcon"
              class="icon-md"
            ></i-lucide>
          </button>
        </div>
      </div>

      <!-- Botão Executar -->
      <button
        (click)="executeRequest()"
        class="btn-primary w-full"
        [disabled]="isLoading"
      >
        @if (isLoading) {
        <i-lucide [img]="RefreshCwIcon" class="icon-sm animate-spin"></i-lucide>
        Executando... } @else {
        <i-lucide [img]="PlayIcon" class="icon-sm"></i-lucide>
        Executar Requisição }
      </button>
    </div>

    <!-- Painel de Resposta -->
    <div class="card">
      <h2 class="card-title">Resposta</h2>

      @if (isLoading) {
      <div class="flex-center h-64">
        <div class="text-center">
          <i-lucide
            [img]="RefreshCwIcon"
            class="w-12 h-12 text-blue-600 animate-spin mx-auto mb-4"
          ></i-lucide>
          <p class="text-gray-600">Executando requisição...</p>
        </div>
      </div>
      } @else if (response) {
      <div class="mb-4 flex-between">
        <div>
          <span
            [class]="
              response.status >= 200 && response.status < 300
                ? 'badge-success'
                : response.status >= 400
                ? 'badge-danger'
                : 'badge-warning'
            "
          >
            {{ response.status }} {{ response.statusText }}
          </span>
          <span class="text-muted ml-2">{{ response.time }}ms</span>
        </div>
        <div class="text-muted">
          {{ currentDate | date : "dd/MM/yyyy HH:mm:ss" }}
        </div>
      </div>

      <div class="code-block overflow-auto max-h-[500px]">
        <pre><code>{{ response.data | json }}</code></pre>
      </div>
      } @else {
      <div class="flex-center h-64">
        <div class="text-center">
          <i-lucide
            [img]="CodeIcon"
            class="w-12 h-12 text-gray-400 mx-auto mb-4"
          ></i-lucide>
          <p class="text-gray-600">
            Execute uma requisição para ver a resposta aqui
          </p>
        </div>
      </div>
      }
    </div>
  </div>

  <div class="mt-8 bg-blue-50 p-6 rounded-lg border-l-4 border-blue-500">
    <h3 class="text-lg font-medium text-blue-800 mb-2">Sobre o Playground</h3>
    <p class="text-blue-700 mb-4">
      Este playground permite testar as APIs do CurrencyWise diretamente no
      navegador. As respostas mostradas são simuladas para fins de demonstração.
    </p>
    <p class="text-blue-700">
      Para usar a API em produção, consulte a
      <a routerLink="/documentation" class="text-blue-800 underline"
        >documentação completa</a
      >
      e obtenha sua chave API na seção
      <a routerLink="/api-keys" class="text-blue-800 underline">API Keys</a>.
    </p>
  </div>
</div>
