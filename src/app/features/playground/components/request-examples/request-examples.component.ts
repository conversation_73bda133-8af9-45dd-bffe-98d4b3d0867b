import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { RequestExample } from '../../interfaces/request-example.interface';

@Component({
  selector: 'app-request-examples',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './request-examples.component.html',
})
export class RequestExamplesComponent {
  examples: RequestExample[] = [
    {
      id: '1',
      name: 'Obter Taxas de Câmbio',
      description: 'Exemplo de como obter taxas de câmbio entre duas moedas',
      method: 'GET',
      endpoint: '/v1/rates?from=USD&to=EUR',
      headers: { Authorization: 'Bearer YOUR_API_KEY' },
      response: {
        from: 'USD',
        to: 'EUR',
        rate: 0.85,
        timestamp: '2024-03-20T10:00:00Z',
      },
    },
    {
      id: '2',
      name: 'Converter <PERSON>da',
      description: 'Exemplo de conversão de valores entre moedas',
      method: 'POST',
      endpoint: '/v1/convert',
      headers: { Authorization: 'Bearer YOUR_API_KEY' },
      body: {
        from: 'BRL',
        to: 'USD',
        amount: 100,
      },
      response: {
        from: 'BRL',
        to: 'USD',
        amount: 100,
        converted: 20.5,
        rate: 0.205,
        timestamp: '2024-03-20T10:00:00Z',
      },
    },
  ];

  selectedExample: RequestExample | null = null;

  selectExample(example: RequestExample) {
    this.selectedExample = example;
  }

  clearSelection() {
    this.selectedExample = null;
  }

  useExample(example: RequestExample) {
    // Lógica para usar o exemplo
    console.log('Usar exemplo:', example);
  }
}
