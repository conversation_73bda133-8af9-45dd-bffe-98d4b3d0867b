<div class="bg-white shadow rounded-lg p-6">
  <h2 class="text-lg font-medium text-gray-900 mb-4">
    Exemplos de Requisições
  </h2>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    @for (example of examples; track example.id) {
    <div
      class="border rounded-lg p-4 hover:shadow-md transition cursor-pointer"
      (click)="selectExample(example)"
    >
      <div class="flex items-start justify-between">
        <div>
          <h3 class="text-base font-semibold text-blue-700">
            {{ example.name }}
          </h3>
          <p class="text-sm text-gray-600 mt-1">{{ example.description }}</p>
        </div>
        <span
          class="inline-block px-2 py-1 rounded text-xs font-semibold"
          [ngClass]="{
            'bg-blue-100 text-blue-800': example.method === 'GET',
            'bg-green-100 text-green-800': example.method === 'POST',
            'bg-yellow-100 text-yellow-800': example.method === 'PUT',
            'bg-red-100 text-red-800': example.method === 'DELETE',
            'bg-purple-100 text-purple-800': example.method === 'PATCH'
          }"
        >
          {{ example.method }}
        </span>
      </div>

      <div class="mt-3 text-sm">
        <div class="text-gray-600 font-mono">{{ example.endpoint }}</div>
      </div>
    </div>
    }
  </div>

  @if (selectedExample) {
  <div
    class="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50"
  >
    <div class="bg-white rounded-lg shadow-lg max-w-2xl w-full p-6 relative">
      <button
        (click)="clearSelection()"
        class="absolute top-2 right-2 text-gray-400 hover:text-gray-700"
      >
        ✕
      </button>

      <h3 class="text-lg font-bold text-blue-700 mb-4">
        {{ selectedExample.name }}
      </h3>

      <div class="space-y-4">
        <div>
          <h4 class="text-sm font-medium text-gray-700 mb-1">Endpoint</h4>
          <div class="bg-gray-50 rounded p-2 text-sm font-mono">
            {{ selectedExample.method }} {{ selectedExample.endpoint }}
          </div>
        </div>

        <div>
          <h4 class="text-sm font-medium text-gray-700 mb-1">Headers</h4>
          <div class="bg-gray-50 rounded p-2 text-sm font-mono">
            {{ selectedExample.headers | json }}
          </div>
        </div>

        @if (selectedExample.body) {
        <div>
          <h4 class="text-sm font-medium text-gray-700 mb-1">Body</h4>
          <div class="bg-gray-50 rounded p-2 text-sm font-mono">
            {{ selectedExample.body | json }}
          </div>
        </div>
        } @if (selectedExample.response) {
        <div>
          <h4 class="text-sm font-medium text-gray-700 mb-1">Resposta</h4>
          <div class="bg-gray-50 rounded p-2 text-sm font-mono">
            {{ selectedExample.response | json }}
          </div>
        </div>
        }
      </div>

      <div class="mt-6 flex justify-end">
        <button
          (click)="useExample(selectedExample)"
          class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Usar Exemplo
        </button>
      </div>
    </div>
  </div>
  }
</div>
