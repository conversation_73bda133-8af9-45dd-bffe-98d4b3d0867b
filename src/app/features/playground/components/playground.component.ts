import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import {
  CheckIcon,
  CodeIcon,
  CopyIcon,
  LucideAngularModule,
  PlayIcon,
  RefreshCwIcon,
} from 'lucide-angular';
import {
  ApiEndpoint,
  ApiParam,
  ApiResponse,
} from '../../../shared/interfaces/api.interface';

@Component({
  selector: 'app-playground',
  standalone: true,
  imports: [CommonModule, FormsModule, LucideAngularModule],
  templateUrl: './playground.component.html',
  styleUrl: './playground.component.css',
})
export class PlaygroundComponent {
  readonly PlayIcon = PlayIcon;
  readonly CodeIcon = CodeIcon;
  readonly CopyIcon = CopyIcon;
  readonly CheckIcon = CheckIcon;
  readonly RefreshCwIcon = RefreshCwIcon;

  currentDate = new Date();

  selectedEndpoint: string = 'latest';
  apiKey: string = 'sua_api_key';
  paramValues: { [key: string]: string } = {};
  response: ApiResponse | null = null;
  isLoading: boolean = false;
  copied: boolean = false;

  endpoints: ApiEndpoint[] = [
    {
      id: 'latest',
      name: 'Taxas Atuais',
      method: 'GET',
      path: '/latest',
      description: 'Retorna as taxas de câmbio mais recentes',
      params: [
        {
          name: 'base',
          type: 'string',
          required: false,
          description: 'Moeda base para as taxas',
          default: 'USD',
          options: ['USD', 'EUR', 'BRL', 'GBP', 'JPY'],
        },
        {
          name: 'symbols',
          type: 'string',
          required: false,
          description: 'Lista de moedas separadas por vírgula',
          default: 'EUR,BRL,JPY',
        },
      ],
    },
    {
      id: 'convert',
      name: 'Converter',
      method: 'GET',
      path: '/convert',
      description: 'Converte um valor de uma moeda para outra',
      params: [
        {
          name: 'from',
          type: 'string',
          required: true,
          description: 'Moeda de origem',
          default: 'USD',
          options: ['USD', 'EUR', 'BRL', 'GBP', 'JPY'],
        },
        {
          name: 'to',
          type: 'string',
          required: true,
          description: 'Moeda de destino',
          default: 'EUR',
          options: ['USD', 'EUR', 'BRL', 'GBP', 'JPY'],
        },
        {
          name: 'amount',
          type: 'number',
          required: true,
          description: 'Valor a ser convertido',
          default: '100',
        },
      ],
    },
    {
      id: 'historical',
      name: 'Dados Históricos',
      method: 'GET',
      path: '/historical/{date}',
      description:
        'Retorna taxas de câmbio históricas para uma data específica',
      params: [
        {
          name: 'date',
          type: 'string',
          required: true,
          description: 'Data no formato YYYY-MM-DD',
          default: '2023-06-10',
        },
        {
          name: 'base',
          type: 'string',
          required: false,
          description: 'Moeda base para as taxas',
          default: 'USD',
          options: ['USD', 'EUR', 'BRL', 'GBP', 'JPY'],
        },
        {
          name: 'symbols',
          type: 'string',
          required: false,
          description: 'Lista de moedas separadas por vírgula',
          default: 'EUR,BRL,JPY',
        },
      ],
    },
  ];

  constructor() {
    // Inicializa os valores padrão dos parâmetros
    this.resetParams();
  }

  resetParams() {
    const endpoint = this.endpoints.find((e) => e.id === this.selectedEndpoint);
    if (endpoint) {
      this.paramValues = {};
      endpoint.params.forEach((param) => {
        if (param.default) {
          this.paramValues[param.name] = param.default;
        }
      });
    }
  }

  onEndpointChange() {
    this.resetParams();
    this.response = null;
  }

  getSelectedEndpoint(): ApiEndpoint | undefined {
    return this.endpoints.find((e) => e.id === this.selectedEndpoint);
  }

  getSelectedEndpointParams(): ApiParam[] {
    return this.getSelectedEndpoint()?.params || [];
  }

  getSelectedEndpointDescription(): string {
    return this.getSelectedEndpoint()?.description || '';
  }

  executeRequest() {
    this.isLoading = true;

    // Simula uma chamada de API com dados fake
    setTimeout(() => {
      const endpoint = this.endpoints.find(
        (e) => e.id === this.selectedEndpoint
      );

      if (endpoint) {
        // Gera uma resposta fake baseada no endpoint selecionado
        this.response = this.generateFakeResponse(endpoint);
      }

      this.isLoading = false;
    }, 800); // Simula um pequeno delay para mostrar o loading
  }

  generateFakeResponse(endpoint: ApiEndpoint): ApiResponse {
    let data: any;

    switch (endpoint.id) {
      case 'latest':
        data = {
          base: this.paramValues['base'] || 'USD',
          date: new Date().toISOString().split('T')[0],
          rates: {
            EUR: 0.92,
            BRL: 5.08,
            JPY: 149.32,
            GBP: 0.78,
          },
        };
        break;

      case 'convert':
        const amount = parseFloat(this.paramValues['amount'] || '100');
        const from = this.paramValues['from'] || 'USD';
        const to = this.paramValues['to'] || 'EUR';

        // Taxas fake para simulação
        const rates: { [key: string]: number } = {
          USD: 1,
          EUR: 0.92,
          BRL: 5.08,
          JPY: 149.32,
          GBP: 0.78,
        };

        // Calcula a conversão
        const rate = rates[to] / rates[from];
        const result = amount * rate;

        data = {
          from,
          to,
          amount,
          result: parseFloat(result.toFixed(2)),
          rate: parseFloat(rate.toFixed(4)),
          date: new Date().toISOString().split('T')[0],
        };
        break;

      case 'historical':
        data = {
          base: this.paramValues['base'] || 'USD',
          date: this.paramValues['date'] || '2023-06-10',
          rates: {
            EUR: 0.93,
            BRL: 4.95,
            JPY: 139.45,
            GBP: 0.79,
          },
        };
        break;

      default:
        data = { message: 'Endpoint não implementado' };
    }

    return {
      status: 200,
      statusText: 'OK',
      data,
      time: Math.floor(Math.random() * 100) + 50, // Tempo de resposta simulado entre 50-150ms
    };
  }

  getRequestUrl(): string {
    const endpoint = this.endpoints.find((e) => e.id === this.selectedEndpoint);
    if (!endpoint) return '';

    let url = 'https://api.currencywise.com' + endpoint.path;

    // Substitui parâmetros de caminho como {date}
    Object.keys(this.paramValues).forEach((key) => {
      url = url.replace(`{${key}}`, this.paramValues[key]);
    });

    // Adiciona parâmetros de consulta
    const queryParams: string[] = [];
    endpoint.params.forEach((param) => {
      // Pula parâmetros que já foram substituídos no caminho
      if (!url.includes(`{${param.name}}`) && this.paramValues[param.name]) {
        queryParams.push(`${param.name}=${this.paramValues[param.name]}`);
      }
    });

    if (queryParams.length > 0) {
      url += '?' + queryParams.join('&');
    }

    return url;
  }

  getRequestCode(): string {
    const url = this.getRequestUrl();
    return `curl -X GET "${url}" \\
-H "Authorization: Bearer ${this.apiKey}"`;
  }

  copyRequestCode() {
    navigator.clipboard.writeText(this.getRequestCode()).then(() => {
      this.copied = true;
      setTimeout(() => {
        this.copied = false;
      }, 2000);
    });
  }
}
