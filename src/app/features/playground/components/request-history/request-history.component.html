<div class="bg-white shadow rounded-lg p-6">
  <h2 class="text-lg font-medium text-gray-900 mb-4">
    Histórico de Requisições
  </h2>
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Data
          </th>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Método
          </th>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Endpoint
          </th>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Status
          </th>
          <th
            class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            A<PERSON><PERSON>es
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        @for (record of history; track record.id) {
        <tr>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {{ record.date | date : "dd/MM/yyyy HH:mm" }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm">
            <span
              class="inline-block px-2 py-1 rounded text-xs font-semibold"
              [ngClass]="{
                'bg-blue-100 text-blue-800': record.method === 'GET',
                'bg-green-100 text-green-800': record.method === 'POST',
                'bg-yellow-100 text-yellow-800': record.method === 'PUT',
                'bg-red-100 text-red-800': record.method === 'DELETE',
                'bg-purple-100 text-purple-800': record.method === 'PATCH'
              }"
            >
              {{ record.method }}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {{ record.endpoint }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm">
            <span
              class="inline-block px-2 py-1 rounded text-xs font-semibold"
              [ngClass]="{
                'bg-green-100 text-green-800':
                  record.status >= 200 && record.status < 300,
                'bg-yellow-100 text-yellow-800':
                  record.status >= 400 && record.status < 500,
                'bg-red-100 text-red-800': record.status >= 500
              }"
            >
              {{ record.status }}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
            <button
              (click)="repeatRequest(record)"
              class="text-blue-600 hover:text-blue-900"
            >
              Repetir
            </button>
          </td>
        </tr>
        }
      </tbody>
    </table>
  </div>
  @if (history.length === 0) {
  <div class="text-center py-8 text-gray-500">
    Nenhuma requisição encontrada.
  </div>
  }
</div>
