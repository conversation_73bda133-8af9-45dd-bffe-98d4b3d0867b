import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';

interface RequestRecord {
  id: string;
  method: string;
  endpoint: string;
  date: Date;
  status: number;
}

@Component({
  selector: 'app-request-history',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './request-history.component.html',
})
export class RequestHistoryComponent {
  history: RequestRecord[] = [
    {
      id: '1',
      method: 'GET',
      endpoint: '/v1/rates',
      date: new Date(),
      status: 200,
    },
    {
      id: '2',
      method: 'POST',
      endpoint: '/v1/convert',
      date: new Date(Date.now() - 86400000),
      status: 201,
    },
    {
      id: '3',
      method: 'GET',
      endpoint: '/v1/balance',
      date: new Date(Date.now() - 2 * 86400000),
      status: 401,
    },
  ];

  repeatRequest(record: RequestRecord) {
    // Lógica para repetir requisição
    console.log('Repetir requisição:', record);
  }
}
