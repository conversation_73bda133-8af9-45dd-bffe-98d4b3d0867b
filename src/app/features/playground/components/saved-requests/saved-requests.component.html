<div class="bg-white shadow rounded-lg p-6">
  <h2 class="text-lg font-medium text-gray-900 mb-4">Requisi<PERSON><PERSON><PERSON></h2>

  @if (savedRequests.length === 0) {
  <div class="text-center py-8 text-gray-500">
    Nenhuma requisição salva encontrada.
  </div>
  } @else {
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    @for (request of savedRequests; track request.id) {
    <div class="border rounded-lg p-4 hover:shadow-md transition">
      <div class="flex justify-between items-start mb-2">
        <h3 class="text-base font-semibold text-blue-700">
          {{ request.name }}
        </h3>
        <span class="text-xs text-gray-500">
          Último uso: {{ request.lastUsed | date : "dd/MM/yyyy HH:mm" }}
        </span>
      </div>

      <div class="space-y-2 text-sm">
        <div class="flex items-center">
          <span
            class="inline-block px-2 py-1 rounded text-xs font-semibold mr-2"
            [ngClass]="{
              'bg-blue-100 text-blue-800': request.method === 'GET',
              'bg-green-100 text-green-800': request.method === 'POST',
              'bg-yellow-100 text-yellow-800': request.method === 'PUT',
              'bg-red-100 text-red-800': request.method === 'DELETE',
              'bg-purple-100 text-purple-800': request.method === 'PATCH'
            }"
          >
            {{ request.method }}
          </span>
          <span class="text-gray-600">{{ request.endpoint }}</span>
        </div>

        @if (request.body) {
        <div class="bg-gray-50 rounded p-2 text-xs font-mono">
          {{ request.body | json }}
        </div>
        }
      </div>

      <div class="mt-4 flex justify-end space-x-2">
        <button
          (click)="useRequest(request)"
          class="px-3 py-1 text-sm text-blue-600 hover:text-blue-800"
        >
          Usar
        </button>
        <button
          (click)="editRequest(request)"
          class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
        >
          Editar
        </button>
        <button
          (click)="deleteRequest(request.id)"
          class="px-3 py-1 text-sm text-red-600 hover:text-red-800"
        >
          Excluir
        </button>
      </div>
    </div>
    }
  </div>
  }
</div>
