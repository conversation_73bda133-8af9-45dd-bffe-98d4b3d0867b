import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { SavedRequest } from '../../interfaces/saved-request.interface';

@Component({
  selector: 'app-saved-requests',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './saved-requests.component.html',
})
export class SavedRequestsComponent {
  savedRequests: SavedRequest[] = [
    {
      id: '1',
      name: 'Obter Taxas USD/EUR',
      method: 'GET',
      endpoint: '/v1/rates?from=USD&to=EUR',
      headers: { Authorization: 'Bearer YOUR_API_KEY' },
      lastUsed: new Date(),
    },
    {
      id: '2',
      name: 'Converter BRL/USD',
      method: 'POST',
      endpoint: '/v1/convert',
      headers: { Authorization: 'Bearer YOUR_API_KEY' },
      body: { from: 'BRL', to: 'USD', amount: 100 },
      lastUsed: new Date(Date.now() - 86400000),
    },
  ];

  deleteRequest(id: string) {
    this.savedRequests = this.savedRequests.filter((req) => req.id !== id);
  }

  editRequest(request: SavedRequest) {
    // Lógica para editar requisição
    console.log('Editar requisição:', request);
  }

  useRequest(request: SavedRequest) {
    // Lógica para usar requisição
    console.log('Usar requisição:', request);
  }
}
