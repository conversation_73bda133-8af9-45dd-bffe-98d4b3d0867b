<div class="container-padded">
  <div class="mb-8">
    <h1 class="section-title">Assinaturas</h1>
  </div>

  <!-- Assinatura Atual -->
  <div class="card mb-8">
    <div class="flex-between mb-6">
      <h2 class="card-title mb-0">Sua Assinatura Atual</h2>
      <span
        [class]="
          'flex-center gap-1 ' +
          getSubscriptionStatusClass(currentSubscription.status)
        "
      >
        <i-lucide
          [img]="CheckIcon"
          class="icon-sm"
          aria-hidden="true"
        ></i-lucide>
        {{ getSubscriptionStatusText(currentSubscription.status) }}
      </span>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <div>
        <h3 class="text-lg font-medium mb-4">Detalhes do Plano</h3>
        <div class="space-y-3">
          <div class="flex-between">
            <span class="text-muted">Plano</span>
            <span class="font-medium">{{ currentSubscription.plan.name }}</span>
          </div>
          <div class="flex-between">
            <span class="text-muted">Preço</span>
            <span class="font-medium">
              {{
                currentSubscription.plan.price === 0
                  ? "Grátis"
                  : "R$" +
                    currentSubscription.plan.price +
                    "/" +
                    currentSubscription.plan.billingPeriod
              }}
            </span>
          </div>
          <div class="flex-between">
            <span class="text-muted">Data de início</span>
            <span class="font-medium">{{
              currentSubscription.startDate | date : "dd/MM/yyyy"
            }}</span>
          </div>
          <div class="flex-between">
            <span class="text-muted">Próxima cobrança</span>
            <span class="font-medium">{{
              currentSubscription.nextBillingDate | date : "dd/MM/yyyy"
            }}</span>
          </div>
          <div class="flex-between">
            <span class="text-muted">Limite de requisições</span>
            <span class="font-medium"
              >{{
                currentSubscription.plan.requestLimit.toLocaleString()
              }}/mês</span
            >
          </div>
        </div>
      </div>

      <div>
        <h3 class="text-lg font-medium mb-4">Método de Pagamento</h3>
        @if (currentSubscription.plan.price > 0) {
        <div class="flex items-center gap-3 mb-4">
          <div class="bg-gray-100 p-2 rounded">
            <i-lucide
              [img]="CreditCardIcon"
              class="icon-md text-gray-700"
              aria-hidden="true"
            ></i-lucide>
          </div>
          <div>
            <div class="font-medium">
              {{
                currentSubscription.paymentMethod.type.charAt(0).toUpperCase() +
                  currentSubscription.paymentMethod.type.slice(1)
              }}
              terminando em {{ currentSubscription.paymentMethod.last4 }}
            </div>
            <div class="text-muted">
              Expira em {{ currentSubscription.paymentMethod.expMonth }}/{{
                currentSubscription.paymentMethod.expYear
              }}
            </div>
          </div>
        </div>
        <button
          (click)="updatePaymentMethod()"
          class="text-blue-600 hover:text-blue-800 font-medium"
        >
          Atualizar método de pagamento
        </button>
        } @else {
        <div class="text-muted">
          Nenhum método de pagamento necessário para o plano gratuito.
        </div>
        }
      </div>
    </div>

    <div class="mb-6">
      <h3 class="text-lg font-medium mb-2">Uso Atual</h3>
      <div class="mb-2 flex-between">
        <span class="text-muted"
          >{{ currentSubscription.usage.current.toLocaleString() }} de
          {{ currentSubscription.plan.requestLimit.toLocaleString() }}
          requisições</span
        >
        <span class="font-medium"
          >{{ currentSubscription.usage.percentage }}%</span
        >
      </div>
      <div
        class="w-full bg-gray-200 rounded-full h-2.5"
        role="progressbar"
        [attr.aria-valuenow]="currentSubscription.usage.percentage"
        aria-valuemin="0"
        aria-valuemax="100"
      >
        <div
          class="bg-blue-600 h-2.5 rounded-full progress-bar"
          [style.width.%]="currentSubscription.usage.percentage"
          [class.bg-yellow-500]="currentSubscription.usage.percentage > 70"
          [class.bg-red-500]="currentSubscription.usage.percentage > 90"
        ></div>
      </div>
    </div>

    <div class="flex-between">
      <button
        (click)="cancelSubscription()"
        class="text-red-600 hover:text-red-800 font-medium"
      >
        Cancelar assinatura
      </button>
      <button (click)="scrollToPlans()" class="btn-primary">
        <i-lucide
          [img]="ArrowRightIcon"
          class="icon-sm"
          aria-hidden="true"
        ></i-lucide>
        Alterar Plano
      </button>
    </div>
  </div>

  <!-- Histórico de Faturas -->
  <div class="card mb-8">
    <h2 class="card-title">Histórico de Faturas</h2>

    @if (invoices.length === 0) {
    <div class="text-center py-8">
      <i-lucide
        [img]="CreditCardIcon"
        class="w-12 h-12 text-gray-400 mx-auto mb-4"
        aria-hidden="true"
      ></i-lucide>
      <p class="text-gray-600 mb-2">Nenhuma fatura encontrada</p>
      <p class="text-muted">
        Suas faturas aparecerão aqui quando estiverem disponíveis.
      </p>
    </div>
    } @else {
    <div class="responsive-table">
      <table class="w-full">
        <thead class="bg-gray-50 text-left">
          <tr>
            <th class="px-4 py-3 text-sm font-medium text-gray-500">Número</th>
            <th class="px-4 py-3 text-sm font-medium text-gray-500">Data</th>
            <th class="px-4 py-3 text-sm font-medium text-gray-500">Valor</th>
            <th class="px-4 py-3 text-sm font-medium text-gray-500">Status</th>
            <th class="px-4 py-3 text-sm font-medium text-gray-500">Ações</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          @for (invoice of invoices; track invoice.id) {
          <tr class="hover:bg-gray-50">
            <td class="px-4 py-4 font-medium">{{ invoice.id }}</td>
            <td class="px-4 py-4 text-muted">
              {{ invoice.date | date : "dd/MM/yyyy" }}
            </td>
            <td class="px-4 py-4">R$ {{ invoice.amount }}</td>
            <td class="px-4 py-4">
              <span [class]="getInvoiceStatusClass(invoice.status)">
                {{ getInvoiceStatusText(invoice.status) }}
              </span>
            </td>
            <td class="px-4 py-4">
              <button
                (click)="downloadInvoice(invoice.id)"
                class="text-blue-600 hover:text-blue-800"
                title="Baixar fatura"
              >
                <i-lucide
                  [img]="DownloadIcon"
                  class="icon-sm"
                  aria-hidden="true"
                ></i-lucide>
                <span class="sr-only">Baixar fatura {{ invoice.id }}</span>
              </button>
            </td>
          </tr>
          }
        </tbody>
      </table>
    </div>
    }
  </div>

  <!-- Planos Disponíveis -->
  <div id="plans" class="mb-8">
    <h2 class="section-title text-center">Nossos Planos</h2>
    <p class="text-center text-gray-600 max-w-3xl mx-auto mb-8">
      Escolha o plano que melhor atende às suas necessidades. Todos os planos
      incluem acesso à nossa API confiável e suporte técnico.
    </p>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      @for (plan of availablePlans; track plan.id) {
      <div
        [class]="
          plan.isPopular
            ? 'bg-blue-50 p-6 rounded-lg shadow-md border-2 border-blue-600 transform scale-105 plan-card'
            : 'card border border-gray-200 plan-card'
        "
        [class.current-plan]="plan.id === currentSubscription.plan.id"
      >
        @if (plan.isPopular) {
        <div
          class="bg-blue-600 text-white text-sm font-bold py-1 px-3 rounded-full inline-block mb-2"
        >
          POPULAR
        </div>
        }
        <h3 class="text-xl font-bold mb-2">{{ plan.name }}</h3>
        <p class="text-4xl font-bold mb-4">
          {{ plan.price === 0 ? "Grátis" : "R$" + plan.price }}
          @if (plan.price > 0) {
          <span class="text-lg font-normal">/{{ plan.billingPeriod }}</span>
          }
        </p>
        <p class="text-muted mb-4">{{ plan.description }}</p>
        <ul class="space-y-2 mb-6">
          @for (feature of plan.features; track feature) {
          <li class="flex items-start gap-2">
            <i-lucide
              [img]="CheckIcon"
              class="icon-sm text-green-600 mt-1"
              aria-hidden="true"
            ></i-lucide>
            <span>{{ feature }}</span>
          </li>
          }
        </ul>
        <button
          (click)="upgradePlan(plan.id)"
          [class]="
            plan.id === currentSubscription.plan.id
              ? 'w-full py-2 rounded-md border border-gray-300 text-gray-500 cursor-not-allowed'
              : 'btn-primary w-full'
          "
          [disabled]="plan.id === currentSubscription.plan.id"
        >
          {{
            plan.id === currentSubscription.plan.id
              ? "Plano Atual"
              : "Escolher Plano"
          }}
        </button>
      </div>
      }
    </div>
  </div>

  <!-- FAQ -->
  <div class="card">
    <h2 class="card-title">Perguntas Frequentes</h2>

    <div class="space-y-6">
      <div>
        <h3 class="font-medium text-lg mb-2">Como funciona a cobrança?</h3>
        <p class="text-muted">
          A cobrança é feita mensalmente no dia em que você assinou o plano.
          Você pode cancelar a qualquer momento, e sua assinatura permanecerá
          ativa até o final do período de cobrança atual.
        </p>
      </div>

      <div>
        <h3 class="font-medium text-lg mb-2">
          O que acontece se eu exceder o limite de requisições?
        </h3>
        <p class="text-muted">
          Se você exceder o limite de requisições do seu plano, as requisições
          adicionais serão rejeitadas com um erro 429 (Too Many Requests).
          Recomendamos monitorar seu uso e fazer upgrade para um plano superior
          se necessário.
        </p>
      </div>

      <div>
        <h3 class="font-medium text-lg mb-2">
          Posso mudar de plano a qualquer momento?
        </h3>
        <p class="text-muted">
          Sim, você pode fazer upgrade ou downgrade do seu plano a qualquer
          momento. Se fizer upgrade, a cobrança será proporcional ao tempo
          restante do período atual. Se fizer downgrade, a alteração entrará em
          vigor no próximo período de cobrança.
        </p>
      </div>

      <div>
        <h3 class="font-medium text-lg mb-2">
          Como posso obter um plano personalizado?
        </h3>
        <p class="text-muted">
          Para necessidades específicas ou volumes maiores, entre em contato com
          nossa equipe de vendas em
          <button
            (click)="contactSales()"
            class="text-blue-600 hover:text-blue-800 font-medium"
          >
            vendas&#64;currencywise.com
          </button>
          para discutir um plano personalizado.
        </p>
      </div>
    </div>
  </div>
</div>
