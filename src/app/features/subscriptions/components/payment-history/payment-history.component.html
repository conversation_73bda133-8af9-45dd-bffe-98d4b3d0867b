<div class="bg-white shadow rounded-lg p-6">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-lg font-medium text-gray-900">Histórico de Pagamentos</h2>
    <div class="flex space-x-4">
      <select
        [(ngModel)]="selectedStatus"
        class="rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
      >
        <option value="all">Todos os Status</option>
        <option value="success">Pagos</option>
        <option value="pending">Pendentes</option>
        <option value="failed">Falhos</option>
      </select>
      <select
        [(ngModel)]="selectedPeriod"
        class="rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
      >
        <option value="all">Todo Período</option>
        <option value="month">Este Mês</option>
        <option value="year">Este Ano</option>
      </select>
    </div>
  </div>

  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Data
          </th>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Descrição
          </th>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Valor
          </th>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Status
          </th>
          <th
            class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Ações
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        @for (payment of getFilteredPayments(); track payment.id) {
        <tr>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {{ payment.date | date : "dd/MM/yyyy" }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {{ payment.description }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {{ payment.amount | currency : "BRL" }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span
              class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
              [class]="getStatusColor(payment.status)"
            >
              {{ getStatusText(payment.status) }}
            </span>
          </td>
          <td
            class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
          >
            @if (payment.invoiceUrl) {
            <button
              (click)="downloadInvoice(payment.invoiceUrl!)"
              class="text-blue-600 hover:text-blue-900 flex items-center justify-end space-x-1"
            >
              <Download class="h-4 w-4" />
              <span>Baixar Fatura</span>
            </button>
            }
          </td>
        </tr>
        }
      </tbody>
    </table>
  </div>

  @if (getFilteredPayments().length === 0) {
  <div class="text-center py-12">
    <Receipt class="mx-auto h-12 w-12 text-gray-400" />
    <h3 class="mt-2 text-sm font-medium text-gray-900">
      Nenhum pagamento encontrado
    </h3>
    <p class="mt-1 text-sm text-gray-500">
      Não há pagamentos para o período e status selecionados.
    </p>
  </div>
  }
</div>
