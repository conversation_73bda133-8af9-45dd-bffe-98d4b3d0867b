import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';

interface PaymentRecord {
  id: string;
  date: Date;
  amount: number;
  status: 'success' | 'pending' | 'failed';
  description: string;
  invoiceUrl?: string;
}

@Component({
  selector: 'app-payment-history',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './payment-history.component.html',
})
export class PaymentHistoryComponent implements OnInit {
  paymentHistory: PaymentRecord[] = [];
  selectedStatus: 'all' | 'success' | 'pending' | 'failed' = 'all';
  selectedPeriod: 'all' | 'month' | 'year' = 'all';

  constructor() {}

  ngOnInit(): void {
    this.loadPaymentHistory();
  }

  private loadPaymentHistory(): void {
    // Dados fake para demonstração
    const today = new Date();
    this.paymentHistory = [
      {
        id: '1',
        date: new Date(today.getFullYear(), today.getMonth(), 1),
        amount: 99.9,
        status: 'success',
        description: 'Assinatura Mensal - Plano Pro',
        invoiceUrl: 'https://exemplo.com/invoice/1',
      },
      {
        id: '2',
        date: new Date(today.getFullYear(), today.getMonth() - 1, 1),
        amount: 99.9,
        status: 'success',
        description: 'Assinatura Mensal - Plano Pro',
        invoiceUrl: 'https://exemplo.com/invoice/2',
      },
      {
        id: '3',
        date: new Date(today.getFullYear(), today.getMonth() - 2, 1),
        amount: 99.9,
        status: 'failed',
        description: 'Assinatura Mensal - Plano Pro',
      },
      {
        id: '4',
        date: new Date(today.getFullYear(), today.getMonth() - 3, 1),
        amount: 99.9,
        status: 'success',
        description: 'Assinatura Mensal - Plano Pro',
        invoiceUrl: 'https://exemplo.com/invoice/4',
      },
    ];
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-50';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'failed':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'success':
        return 'Pago';
      case 'pending':
        return 'Pendente';
      case 'failed':
        return 'Falhou';
      default:
        return 'Desconhecido';
    }
  }

  getFilteredPayments(): PaymentRecord[] {
    return this.paymentHistory.filter((payment) => {
      const statusMatch =
        this.selectedStatus === 'all' || payment.status === this.selectedStatus;
      const periodMatch = this.filterByPeriod(payment.date);
      return statusMatch && periodMatch;
    });
  }

  private filterByPeriod(date: Date): boolean {
    const today = new Date();
    switch (this.selectedPeriod) {
      case 'month':
        return (
          date.getMonth() === today.getMonth() &&
          date.getFullYear() === today.getFullYear()
        );
      case 'year':
        return date.getFullYear() === today.getFullYear();
      default:
        return true;
    }
  }

  downloadInvoice(invoiceUrl: string): void {
    // Aqui você implementaria a lógica para baixar a fatura
    console.log('Baixando fatura:', invoiceUrl);
  }
}
