import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import {
  AlertCircleIcon,
  ArrowRightIcon,
  CalendarIcon,
  CheckIcon,
  ClockIcon,
  CreditCardIcon,
  DownloadIcon,
  LucideAngularModule,
} from 'lucide-angular';
import {
  Invoice,
  Plan,
  Subscription,
} from '../../../shared/interfaces/subscription.interface';

@Component({
  selector: 'app-subscriptions',
  standalone: true,
  imports: [CommonModule, LucideAngularModule],
  templateUrl: './subscriptions.component.html',
  styleUrl: './subscriptions.component.css',
})
export class SubscriptionsComponent {
  private router = inject(Router);
  readonly CreditCardIcon = CreditCardIcon;
  readonly CheckIcon = CheckIcon;
  readonly DownloadIcon = DownloadIcon;
  readonly AlertCircleIcon = AlertCircleIcon;
  readonly ArrowRightIcon = ArrowRightIcon;
  readonly CalendarIcon = CalendarIcon;
  readonly ClockIcon = ClockIcon;

  // Dados fake para planos disponíveis
  availablePlans: Plan[] = [
    {
      id: 'basic',
      name: 'Básico',
      price: 0,
      billingPeriod: 'mensal',
      description: 'Para desenvolvedores individuais e projetos pessoais',
      features: [
        '1.000 requisições/mês',
        'Taxas atualizadas a cada hora',
        'Suporte por email',
        'Acesso a 1 mês de dados históricos',
      ],
      requestLimit: 1000,
    },
    {
      id: 'pro',
      name: 'Profissional',
      price: 49,
      billingPeriod: 'mensal',
      description: 'Para startups e pequenas empresas',
      features: [
        '100.000 requisições/mês',
        'Taxas atualizadas a cada 10 minutos',
        'Suporte prioritário',
        'Acesso a 1 ano de dados históricos',
        'Webhooks para atualizações de taxas',
      ],
      requestLimit: 100000,
      isPopular: true,
    },
    {
      id: 'enterprise',
      name: 'Empresarial',
      price: 199,
      billingPeriod: 'mensal',
      description: 'Para empresas de médio e grande porte',
      features: [
        '1.000.000 requisições/mês',
        'Taxas em tempo real',
        'Suporte 24/7',
        'Acesso a dados históricos completos',
        'SLA garantido',
        'IP dedicado',
        'Personalização de endpoints',
      ],
      requestLimit: 1000000,
    },
  ];

  // Dados fake para a assinatura atual do usuário
  currentSubscription: Subscription = {
    id: 'sub_1234567890',
    plan: this.availablePlans.find((p) => p.id === 'pro') as Plan,
    startDate: new Date('2024-01-15'),
    nextBillingDate: new Date('2024-07-15'),
    status: 'active',
    paymentMethod: {
      type: 'visa',
      last4: '4242',
      expMonth: 12,
      expYear: 2025,
    },
    usage: {
      current: 45000,
      percentage: 45,
    },
  };

  // Dados fake para o histórico de faturas
  invoices: Invoice[] = [
    {
      id: 'inv_1234567890',
      date: new Date('2024-06-15'),
      amount: 49,
      status: 'paid',
      downloadUrl: '#',
    },
    {
      id: 'inv_1234567891',
      date: new Date('2024-05-15'),
      amount: 49,
      status: 'paid',
      downloadUrl: '#',
    },
    {
      id: 'inv_1234567892',
      date: new Date('2024-04-15'),
      amount: 49,
      status: 'paid',
      downloadUrl: '#',
    },
    {
      id: 'inv_1234567893',
      date: new Date('2024-03-15'),
      amount: 49,
      status: 'paid',
      downloadUrl: '#',
    },
    {
      id: 'inv_1234567894',
      date: new Date('2024-02-15'),
      amount: 49,
      status: 'paid',
      downloadUrl: '#',
    },
    {
      id: 'inv_1234567895',
      date: new Date('2024-01-15'),
      amount: 49,
      status: 'paid',
      downloadUrl: '#',
    },
  ];

  // Método para atualizar o plano (simulado)
  upgradePlan(planId: string) {
    // Em uma aplicação real, isso enviaria uma requisição para o backend
    // e atualizaria o plano do usuário
    console.log(`Upgrade para o plano ${planId}`);

    // Simular uma atualização bem-sucedida
    const selectedPlan = this.availablePlans.find((p) => p.id === planId);
    if (selectedPlan) {
      this.currentSubscription.plan = selectedPlan;
      // Aqui você poderia mostrar uma notificação de sucesso
      // usando um serviço de notificação como SweetAlert2
    }
  }

  // Método para cancelar a assinatura (simulado)
  cancelSubscription() {
    // Em uma aplicação real, isso abriria um modal de confirmação
    // e enviaria uma requisição para o backend para cancelar a assinatura
    const confirmCancel = confirm(
      'Tem certeza que deseja cancelar sua assinatura? Você perderá acesso aos recursos premium no final do período de cobrança atual.'
    );

    if (confirmCancel) {
      // Simular um cancelamento bem-sucedido
      this.currentSubscription.status = 'canceled';
      // Aqui você poderia mostrar uma notificação de sucesso
    }
  }

  // Método para atualizar o método de pagamento (simulado)
  updatePaymentMethod() {
    // Em uma aplicação real, isso redirecionaria para uma página
    // de atualização de método de pagamento ou abriria um modal
    this.router.navigate(['/payment-methods']);
  }

  // Método para baixar uma fatura (simulado)
  downloadInvoice(invoiceId: string) {
    // Em uma aplicação real, isso iniciaria o download da fatura
    console.log(`Baixando fatura ${invoiceId}`);

    // Simular um download bem-sucedido
    const invoice = this.invoices.find((inv) => inv.id === invoiceId);
    if (invoice) {
      // Aqui você poderia iniciar o download de um arquivo PDF
      // ou abrir uma nova aba com a fatura
      window.open(invoice.downloadUrl, '_blank');
    }
  }

  // Método para obter a classe CSS do status da fatura
  getInvoiceStatusClass(status: string): string {
    switch (status) {
      case 'paid':
        return 'badge-success';
      case 'pending':
        return 'badge-warning';
      case 'failed':
        return 'badge-danger';
      default:
        return '';
    }
  }

  // Método para obter o texto do status da fatura
  getInvoiceStatusText(status: string): string {
    switch (status) {
      case 'paid':
        return 'Pago';
      case 'pending':
        return 'Pendente';
      case 'failed':
        return 'Falhou';
      default:
        return status;
    }
  }

  // Método para obter a classe CSS do status da assinatura
  getSubscriptionStatusClass(status: string): string {
    switch (status) {
      case 'active':
        return 'badge-success';
      case 'canceled':
        return 'badge-danger';
      case 'past_due':
        return 'badge-warning';
      case 'trialing':
        return 'badge-warning';
      default:
        return '';
    }
  }

  // Método para obter o texto do status da assinatura
  getSubscriptionStatusText(status: string): string {
    switch (status) {
      case 'active':
        return 'Ativo';
      case 'canceled':
        return 'Cancelado';
      case 'past_due':
        return 'Pagamento Atrasado';
      case 'trialing':
        return 'Em Teste';
      default:
        return status;
    }
  }

  // Método para rolar até a seção de planos
  scrollToPlans(): void {
    const plansElement = document.getElementById('plans');
    if (plansElement) {
      plansElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }

  // Método para contatar a equipe de vendas
  contactSales(): void {
    // Em uma aplicação real, isso poderia abrir um modal de contato
    // ou redirecionar para uma página de contato
    this.router.navigate(['/sales']);
  }
}
