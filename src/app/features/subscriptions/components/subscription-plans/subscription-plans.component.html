<div class="bg-white py-12">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center">
      <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">
        Escolha o Plano Ideal para Você
      </h2>
      <p class="mt-4 text-lg text-gray-600">
        Comece gratuitamente e atualize conforme seu negócio cresce
      </p>
    </div>

    <!-- Toggle Mensal/Anual -->
    <div class="mt-8 flex justify-center">
      <div class="relative bg-gray-100 p-1 rounded-lg">
        <button
          type="button"
          (click)="toggleInterval()"
          class="relative py-2 px-6 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          [class.text-white]="selectedInterval === 'month'"
          [class.bg-blue-600]="selectedInterval === 'month'"
          [class.text-gray-900]="selectedInterval === 'year'"
        >
          Mensal
        </button>
        <button
          type="button"
          (click)="toggleInterval()"
          class="relative py-2 px-6 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          [class.text-white]="selectedInterval === 'year'"
          [class.bg-blue-600]="selectedInterval === 'year'"
          [class.text-gray-900]="selectedInterval === 'month'"
        >
          Anual
          <span class="ml-1 text-xs text-blue-600">-20%</span>
        </button>
      </div>
    </div>

    <!-- Planos -->
    <div
      class="mt-12 space-y-4 sm:mt-16 sm:space-y-0 sm:grid sm:grid-cols-2 sm:gap-6 lg:max-w-4xl lg:mx-auto xl:max-w-none xl:grid-cols-3"
    >
      @for (plan of plans; track plan.id) {
      <div
        class="border border-gray-200 rounded-lg shadow-sm divide-y divide-gray-200"
        [class.ring-2]="plan.isPopular"
        [class.ring-blue-500]="plan.isPopular"
      >
        <div class="p-6">
          @if (plan.isPopular) {
          <p
            class="absolute top-0 -translate-y-1/2 transform rounded-full bg-blue-600 py-1.5 px-4 text-sm font-semibold text-white"
          >
            Mais Popular
          </p>
          }
          <h3 class="text-lg font-medium text-gray-900">{{ plan.name }}</h3>
          <p class="mt-4">
            <span class="text-4xl font-extrabold text-gray-900"
              >R$ {{ plan.price }}</span
            >
            <span class="text-base font-medium text-gray-500"
              >/{{ plan.interval === "month" ? "mês" : "ano" }}</span
            >
          </p>
          <button
            type="button"
            (click)="selectPlan(plan.id)"
            class="mt-8 block w-full bg-blue-600 border border-transparent rounded-md py-2 text-sm font-semibold text-white text-center hover:bg-blue-700"
          >
            Começar Agora
          </button>
        </div>
        <div class="pt-6 pb-8 px-6">
          <h4 class="text-sm font-medium text-gray-900 tracking-wide">
            O que está incluído:
          </h4>
          <ul class="mt-6 space-y-4">
            @for (feature of plan.features; track feature) {
            <li class="flex space-x-3">
              <check class="flex-shrink-0 h-5 w-5 text-green-500" />
              <span class="text-sm text-gray-500">{{ feature }}</span>
            </li>
            }
          </ul>
        </div>
      </div>
      }
    </div>

    <!-- FAQ -->
    <div class="mt-12">
      <h3 class="text-lg font-medium text-gray-900">Perguntas Frequentes</h3>
      <div class="mt-6 space-y-6">
        <div>
          <h4 class="text-base font-medium text-gray-900">
            Posso mudar de plano a qualquer momento?
          </h4>
          <p class="mt-2 text-base text-gray-500">
            Sim, você pode atualizar ou fazer downgrade do seu plano a qualquer
            momento. As alterações serão aplicadas no próximo ciclo de
            faturamento.
          </p>
        </div>
        <div>
          <h4 class="text-base font-medium text-gray-900">
            Existe um limite de requisições?
          </h4>
          <p class="mt-2 text-base text-gray-500">
            Cada plano tem seu próprio limite de requisições. Você pode ver os
            detalhes acima. Se precisar de mais, entre em contato conosco.
          </p>
        </div>
        <div>
          <h4 class="text-base font-medium text-gray-900">
            Como funciona o suporte?
          </h4>
          <p class="mt-2 text-base text-gray-500">
            O nível de suporte varia de acordo com o plano escolhido. Do email
            básico ao suporte 24/7 para clientes empresariais.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
