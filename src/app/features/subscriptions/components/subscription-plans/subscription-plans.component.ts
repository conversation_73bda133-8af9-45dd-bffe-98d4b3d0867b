import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { Check, CreditCard, X } from 'lucide-angular';

interface Plan {
  id: string;
  name: string;
  price: number;
  interval: 'month' | 'year';
  features: string[];
  limits: {
    requests: number;
    endpoints: number;
    support: string;
  };
  isPopular?: boolean;
}

@Component({
  selector: 'app-subscription-plans',
  standalone: true,
  imports: [CommonModule, RouterModule, CreditCard, Check, X],
  templateUrl: './subscription-plans.component.html',
})
export class SubscriptionPlansComponent implements OnInit {
  plans: Plan[] = [];
  selectedInterval: 'month' | 'year' = 'month';

  ngOnInit() {
    this.loadPlans();
  }

  loadPlans() {
    this.plans = [
      {
        id: 'basic',
        name: 'Básico',
        price: 29.9,
        interval: 'month',
        features: [
          'Até 1.000 requisições/mês',
          'Acesso a 5 endpoints',
          'Suporte por email',
          'Documentação básica',
          'Atualizações mensais',
        ],
        limits: {
          requests: 1000,
          endpoints: 5,
          support: 'email',
        },
      },
      {
        id: 'pro',
        name: 'Profissional',
        price: 99.9,
        interval: 'month',
        features: [
          'Até 10.000 requisições/mês',
          'Acesso a todos os endpoints',
          'Suporte prioritário',
          'Documentação avançada',
          'Atualizações semanais',
          'API Keys ilimitadas',
          'Relatórios avançados',
        ],
        limits: {
          requests: 10000,
          endpoints: -1, // ilimitado
          support: 'priority',
        },
        isPopular: true,
      },
      {
        id: 'enterprise',
        name: 'Empresarial',
        price: 299.9,
        interval: 'month',
        features: [
          'Requisições ilimitadas',
          'Acesso a todos os endpoints',
          'Suporte 24/7',
          'Documentação personalizada',
          'Atualizações em tempo real',
          'API Keys ilimitadas',
          'Relatórios personalizados',
          'SLA garantido',
          'Integração dedicada',
        ],
        limits: {
          requests: -1, // ilimitado
          endpoints: -1, // ilimitado
          support: '24/7',
        },
      },
    ];
  }

  toggleInterval() {
    this.selectedInterval =
      this.selectedInterval === 'month' ? 'year' : 'month';
    this.updatePrices();
  }

  updatePrices() {
    this.plans = this.plans.map((plan) => {
      const newPlan = { ...plan };
      if (this.selectedInterval === 'year') {
        newPlan.price = Math.round(plan.price * 10 * 0.8); // 20% de desconto no plano anual
      } else {
        newPlan.price = plan.price;
      }
      newPlan.interval = this.selectedInterval;
      return newPlan;
    });
  }

  getYearlyPrice(monthlyPrice: number): number {
    return Math.round(monthlyPrice * 10 * 0.8);
  }

  selectPlan(planId: string) {
    // Implementar lógica de seleção de plano
    console.log('Plano selecionado:', planId);
  }
}
