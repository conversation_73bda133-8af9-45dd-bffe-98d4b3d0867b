/* Estilos específicos para o componente subscriptions */
@reference "tailwindcss";

/* Estilos para tabela responsiva */
@media (max-width: 768px) {
  .responsive-table {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .responsive-table thead {
    position: sticky;
    top: 0;
    z-index: 1;
  }
}

/* Estilos para badges */
.badge-success {
  @apply bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium;
}

.badge-danger {
  @apply bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium;
}

/* Estilos para barra de progresso */
.progress-bar {
  transition: width 0.5s ease-in-out;
}

/* Estilos para cards de planos */
.plan-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.plan-card:hover:not(.current-plan) {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.current-plan {
  cursor: default;
}
