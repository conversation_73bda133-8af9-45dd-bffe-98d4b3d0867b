<div class="bg-white shadow rounded-lg p-6">
  <h2 class="text-lg font-medium text-gray-900 mb-6">Pagamento</h2>

  <!-- <PERSON><PERSON><PERSON><PERSON> -->
  <div class="space-y-4">
    <div class="flex justify-between items-center">
      <h3 class="text-base font-medium text-gray-900">
        Métodos de Pagamento Salvos
      </h3>
      <button
        type="button"
        (click)="addNewCard()"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <PERSON><PERSON><PERSON><PERSON>
      </button>
    </div>

    <div class="space-y-3">
      @for (method of savedPaymentMethods; track method.id) {
      <div
        class="flex items-center justify-between p-4 border rounded-lg"
        [class.border-blue-500]="selectedPaymentMethod === method.id"
        [class.bg-blue-50]="selectedPaymentMethod === method.id"
      >
        <div class="flex items-center space-x-4">
          <input
            type="radio"
            [id]="'method-' + method.id"
            name="paymentMethod"
            [value]="method.id"
            [(ngModel)]="selectedPaymentMethod"
            (change)="selectPaymentMethod(method.id)"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
          />
          <div class="flex items-center space-x-3">
            <ng-container *ngComponentOutlet="getCardBrandIcon(method.brand)" />
            <div>
              <p class="text-sm font-medium text-gray-900">
                {{ method.brand }} terminando em {{ method.last4 }}
              </p>
              <p class="text-sm text-gray-500">
                Expira em {{ method.expiryMonth }}/{{ method.expiryYear }}
              </p>
            </div>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          @if (!method.isDefault) {
          <button
            type="button"
            (click)="setDefaultPaymentMethod(method.id)"
            class="text-sm text-blue-600 hover:text-blue-900"
          >
            Definir como Padrão
          </button>
          }
          <button
            type="button"
            (click)="removePaymentMethod(method.id)"
            class="text-sm text-red-600 hover:text-red-900"
          >
            Remover
          </button>
        </div>
      </div>
      }
    </div>
  </div>

  <!-- Formulário de Novo Cartão -->
  @if (isAddingNewCard) {
  <form
    [formGroup]="paymentForm"
    (ngSubmit)="onSubmit()"
    class="mt-6 space-y-6"
  >
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <label for="cardNumber" class="block text-sm font-medium text-gray-700">
          Número do Cartão
        </label>
        <input
          type="text"
          id="cardNumber"
          formControlName="cardNumber"
          class="mt-1 block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          placeholder="1234 5678 9012 3456"
        />
      </div>
      <div>
        <label for="cardHolder" class="block text-sm font-medium text-gray-700">
          Nome no Cartão
        </label>
        <input
          type="text"
          id="cardHolder"
          formControlName="cardHolder"
          class="mt-1 block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          placeholder="NOME COMO ESTÁ NO CARTÃO"
        />
      </div>
      <div>
        <label
          for="expiryMonth"
          class="block text-sm font-medium text-gray-700"
        >
          Mês de Expiração
        </label>
        <input
          type="number"
          id="expiryMonth"
          formControlName="expiryMonth"
          class="mt-1 block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          placeholder="MM"
        />
      </div>
      <div>
        <label for="expiryYear" class="block text-sm font-medium text-gray-700">
          Ano de Expiração
        </label>
        <input
          type="number"
          id="expiryYear"
          formControlName="expiryYear"
          class="mt-1 block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          placeholder="AAAA"
        />
      </div>
      <div>
        <label for="cvv" class="block text-sm font-medium text-gray-700">
          CVV
        </label>
        <input
          type="text"
          id="cvv"
          formControlName="cvv"
          class="mt-1 block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          placeholder="123"
        />
      </div>
    </div>

    <div class="flex items-center">
      <input
        type="checkbox"
        id="saveCard"
        formControlName="saveCard"
        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
      />
      <label for="saveCard" class="ml-2 block text-sm text-gray-900">
        Salvar cartão para futuras compras
      </label>
    </div>

    <div class="flex justify-end">
      <button
        type="submit"
        [disabled]="!paymentForm.valid"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Finalizar Pagamento
      </button>
    </div>
  </form>
  }
</div>
