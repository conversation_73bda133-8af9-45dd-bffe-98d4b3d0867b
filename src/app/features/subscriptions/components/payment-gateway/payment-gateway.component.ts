import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { CreditCard, Lock } from 'lucide-angular';

interface PaymentMethod {
  id: string;
  type: 'credit' | 'debit';
  last4: string;
  brand: string;
  expiryMonth: number;
  expiryYear: number;
  isDefault: boolean;
}

@Component({
  selector: 'app-payment-gateway',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './payment-gateway.component.html',
})
export class PaymentGatewayComponent implements OnInit {
  paymentForm: FormGroup;
  savedPaymentMethods: PaymentMethod[] = [];
  isAddingNewCard = false;
  selectedPaymentMethod: string | null = null;

  constructor(private fb: FormBuilder) {
    this.paymentForm = this.fb.group({
      cardNumber: ['', [Validators.required, Validators.pattern(/^\d{16}$/)]],
      cardHolder: ['', [Validators.required]],
      expiryMonth: [
        '',
        [Validators.required, Validators.min(1), Validators.max(12)],
      ],
      expiryYear: [
        '',
        [Validators.required, Validators.min(new Date().getFullYear())],
      ],
      cvv: ['', [Validators.required, Validators.pattern(/^\d{3,4}$/)]],
      saveCard: [false],
    });
  }

  ngOnInit(): void {
    this.loadSavedPaymentMethods();
  }

  private loadSavedPaymentMethods(): void {
    // Dados fake para demonstração
    this.savedPaymentMethods = [
      {
        id: '1',
        type: 'credit',
        last4: '4242',
        brand: 'Visa',
        expiryMonth: 12,
        expiryYear: 2025,
        isDefault: true,
      },
      {
        id: '2',
        type: 'credit',
        last4: '5555',
        brand: 'Mastercard',
        expiryMonth: 6,
        expiryYear: 2024,
        isDefault: false,
      },
    ];
    this.selectedPaymentMethod = this.savedPaymentMethods[0].id;
  }

  addNewCard(): void {
    this.isAddingNewCard = true;
    this.selectedPaymentMethod = null;
    this.paymentForm.reset();
  }

  selectPaymentMethod(methodId: string): void {
    this.selectedPaymentMethod = methodId;
    this.isAddingNewCard = false;
  }

  removePaymentMethod(methodId: string): void {
    this.savedPaymentMethods = this.savedPaymentMethods.filter(
      (method) => method.id !== methodId
    );
    if (this.selectedPaymentMethod === methodId) {
      this.selectedPaymentMethod = this.savedPaymentMethods[0]?.id || null;
    }
  }

  setDefaultPaymentMethod(methodId: string): void {
    this.savedPaymentMethods = this.savedPaymentMethods.map((method) => ({
      ...method,
      isDefault: method.id === methodId,
    }));
  }

  onSubmit(): void {
    if (this.paymentForm.valid) {
      // Aqui você implementaria a lógica para processar o pagamento
      console.log('Processando pagamento:', this.paymentForm.value);
    }
  }

  getCardBrandIcon(brand: string): any {
    switch (brand.toLowerCase()) {
      case 'visa':
      case 'mastercard':
      case 'amex':
        return CreditCard;
      default:
        return Lock;
    }
  }
}
