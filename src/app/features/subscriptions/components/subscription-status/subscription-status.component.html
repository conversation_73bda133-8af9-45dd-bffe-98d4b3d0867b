<div class="bg-white shadow rounded-lg p-6">
  <h2 class="text-lg font-medium text-gray-900 mb-4">Status da Assinatura</h2>
  @if (subscription) {
  <div
    class="flex flex-col md:flex-row md:items-center md:justify-between gap-6"
  >
    <div>
      <div class="flex items-center space-x-3 mb-2">
        <span
          class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
          [class]="getStatusColor(subscription.status)"
        >
          @if (subscription.status === 'active') {
          <check class="w-4 h-4 mr-1" />
          } @if (subscription.status === 'pending') {
          <refresh-cw class="w-4 h-4 mr-1" />
          } @if (subscription.status === 'canceled' || subscription.status ===
          'expired') {
          <x class="w-4 h-4 mr-1" />
          }
          {{
            subscription.status === "active"
              ? "Ativa"
              : subscription.status === "pending"
              ? "Pendente"
              : subscription.status === "canceled"
              ? "Cancelada"
              : "Expirada"
          }}
        </span>
      </div>
      <div class="text-gray-700 text-base">
        <span class="font-semibold">Plano:</span> {{ subscription.plan }}<br />
        <span class="font-semibold">Renovação:</span>
        {{ subscription.renewalDate | date : "dd/MM/yyyy" }}<br />
        <span class="font-semibold">Próximo Pagamento:</span> R$
        {{ subscription.nextPaymentAmount | number : "1.2-2" }}
      </div>
      <div class="mt-2">
        <span
          class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"
          [class]="getStatusColor(subscription.paymentStatus)"
        >
          <credit-card class="w-3 h-3 mr-1" />
          {{ getPaymentStatusText(subscription.paymentStatus) }}
        </span>
      </div>
    </div>
    <div class="flex flex-col space-y-2 md:space-y-0 md:flex-row md:space-x-3">
      <button
        type="button"
        (click)="onRenew()"
        class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        [disabled]="subscription.status !== 'active'"
      >
        Renovar Agora
      </button>
      <button
        type="button"
        (click)="onUpgrade()"
        class="inline-flex items-center justify-center px-4 py-2 border border-blue-600 text-sm font-medium rounded-md text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        Atualizar Plano
      </button>
      <button
        type="button"
        (click)="onCancel()"
        class="inline-flex items-center justify-center px-4 py-2 border border-red-600 text-sm font-medium rounded-md text-red-600 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        [disabled]="subscription.status !== 'active'"
      >
        Cancelar Assinatura
      </button>
    </div>
  </div>
  } @else {
  <div class="text-center text-gray-500 py-8">
    Nenhuma assinatura ativa encontrada.
  </div>
  }
</div>
