import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import {
  Check,
  CreditCard,
  LucideAngularModule,
  RefreshCw,
  X,
} from 'lucide-angular';

interface SubscriptionStatus {
  plan: string;
  status: 'active' | 'canceled' | 'pending' | 'expired';
  renewalDate: Date;
  paymentStatus: 'paid' | 'pending' | 'failed';
  nextPaymentAmount: number;
}

@Component({
  selector: 'app-subscription-status',
  standalone: true,
  imports: [CommonModule, RouterModule, LucideAngularModule],
  templateUrl: './subscription-status.component.html',
})
export class SubscriptionStatusComponent implements OnInit {
  readonly CheckIcon = Check;
  readonly XIcon = X;
  readonly RefreshCwIcon = RefreshCw;
  readonly CreditCardIcon = CreditCard;
  subscription: SubscriptionStatus | null = null;

  ngOnInit(): void {
    this.loadSubscriptionStatus();
  }

  loadSubscriptionStatus() {
    // Dad<PERSON> fake para demonstração
    this.subscription = {
      plan: 'Profissional',
      status: 'active',
      renewalDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
      paymentStatus: 'paid',
      nextPaymentAmount: 99.9,
    };
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-50';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'canceled':
      case 'expired':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  }

  getPaymentStatusText(status: string): string {
    switch (status) {
      case 'paid':
        return 'Pago';
      case 'pending':
        return 'Pendente';
      case 'failed':
        return 'Falhou';
      default:
        return 'Desconhecido';
    }
  }

  onRenew() {
    // Lógica para renovar assinatura
    console.log('Renovar assinatura');
  }

  onCancel() {
    // Lógica para cancelar assinatura
    console.log('Cancelar assinatura');
  }

  onUpgrade() {
    // Lógica para atualizar plano
    console.log('Atualizar plano');
  }
}
