import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NotificationService } from '../../../core/services/notification.service';

@Component({
  selector: 'app-notification-demo',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './notification-demo.component.html',
  styleUrls: ['./notification-demo.component.css']
})
export class NotificationDemoComponent {
  constructor(private notificationService: NotificationService) {}

  showSuccessToast(): void {
    this.notificationService.success('Operação realizada com sucesso!');
  }

  showErrorToast(): void {
    this.notificationService.error('Ocorreu um erro ao processar sua solicitação.');
  }

  showInfoToast(): void {
    this.notificationService.info('Esta é uma mensagem informativa.');
  }

  showWarningToast(): void {
    this.notificationService.warning('Atenção! Esta ação pode ter consequências.');
  }

  showConfirmDialog(): void {
    this.notificationService.confirm({
      title: 'Confirmação',
      text: 'Tem certeza que deseja realizar esta ação?',
      icon: 'question'
    }).then((result) => {
      if (result.isConfirmed) {
        this.notificationService.success('Ação confirmada!');
      } else if (result.isDismissed) {
        this.notificationService.info('Ação cancelada.');
      }
    });
  }

  showDangerConfirmDialog(): void {
    this.notificationService.confirm({
      title: 'Atenção!',
      text: 'Esta ação não pode ser desfeita. Deseja continuar?',
      icon: 'warning',
      confirmButtonText: 'Sim, continuar',
      cancelButtonText: 'Não, cancelar',
      focusCancel: true
    }).then((result) => {
      if (result.isConfirmed) {
        this.notificationService.success('Ação perigosa realizada com sucesso!');
      }
    });
  }
}
