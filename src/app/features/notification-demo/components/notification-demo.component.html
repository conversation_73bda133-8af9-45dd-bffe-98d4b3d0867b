<div class="container mx-auto px-4 py-8">
  <div class="max-w-3xl mx-auto">
    <h1 class="text-3xl font-bold mb-6">Demonstração de Notificações</h1>
    
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
      <h2 class="text-xl font-semibold mb-4">Toasts</h2>
      <p class="text-gray-600 mb-4">
        Toasts são notificações temporárias que aparecem no canto superior direito da tela.
      </p>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <button 
          (click)="showSuccessToast()" 
          class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
          Sucesso
        </button>
        
        <button 
          (click)="showErrorToast()" 
          class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
          Erro
        </button>
        
        <button 
          (click)="showInfoToast()" 
          class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
          Informação
        </button>
        
        <button 
          (click)="showWarningToast()" 
          class="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors">
          Aviso
        </button>
      </div>
    </div>
    
    <div class="bg-white shadow-md rounded-lg p-6">
      <h2 class="text-xl font-semibold mb-4">Diálogos de Confirmação</h2>
      <p class="text-gray-600 mb-4">
        Diálogos de confirmação são usados para confirmar ações importantes antes de executá-las.
      </p>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <button 
          (click)="showConfirmDialog()" 
          class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors">
          Confirmação Simples
        </button>
        
        <button 
          (click)="showDangerConfirmDialog()" 
          class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
          Confirmação de Perigo
        </button>
      </div>
    </div>
  </div>
</div>
