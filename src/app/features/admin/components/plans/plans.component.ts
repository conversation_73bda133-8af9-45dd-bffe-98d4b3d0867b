import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { LucideAngularModule } from 'lucide-angular';

interface Plan {
  id: number;
  name: string;
  price: number;
  description: string;
  features: string[];
  subscribers: number;
  status: 'active' | 'inactive';
}

@Component({
  selector: 'app-plans',
  standalone: true,
  imports: [CommonModule, FormsModule, LucideAngularModule],
  template: `
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Gerenciamento de Planos</h1>
        <button class="btn-primary">
          <lucide-icon name="plus" class="icon-sm mr-2"></lucide-icon>
          Novo Plano
        </button>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @for (plan of plans; track plan.id) {
        <div class="card">
          <div class="p-6">
            <div class="flex justify-between items-start mb-4">
              <div>
                <h3 class="text-xl font-bold">{{ plan.name }}</h3>
                <p class="text-3xl font-bold text-primary mt-2">
                  R$ {{ plan.price }}
                  <span class="text-sm font-normal text-gray-500">/mês</span>
                </p>
              </div>
              <span
                class="px-2 py-1 text-xs font-semibold rounded-full"
                [class]="
                  plan.status === 'active'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                "
              >
                {{ plan.status === 'active' ? 'Ativo' : 'Inativo' }}
              </span>
            </div>

            <p class="text-gray-600 mb-4">{{ plan.description }}</p>

            <div class="space-y-2 mb-6">
              @for (feature of plan.features; track feature) {
              <div class="flex items-center text-sm">
                <lucide-icon
                  name="check"
                  class="icon-sm text-green-500 mr-2"
                ></lucide-icon>
                {{ feature }}
              </div>
              }
            </div>

            <div class="flex items-center justify-between pt-4 border-t">
              <span class="text-sm text-gray-500">
                {{ plan.subscribers }} assinantes
              </span>
              <div class="flex gap-2">
                <button class="btn-secondary">
                  <lucide-icon name="edit" class="icon-sm"></lucide-icon>
                </button>
                <button class="btn-danger">
                  <lucide-icon name="trash-2" class="icon-sm"></lucide-icon>
                </button>
              </div>
            </div>
          </div>
        </div>
        }
      </div>
    </div>
  `,
})
export class PlansComponent implements OnInit {
  plans: Plan[] = [
    {
      id: 1,
      name: 'Plano Free',
      price: 0,
      description: 'Ideal para testes e projetos pequenos',
      features: [
        '1.000 requisições/dia',
        '10 moedas principais',
        'Dados de 7 dias',
        'Suporte por email',
      ],
      subscribers: 1250,
      status: 'active',
    },
    {
      id: 2,
      name: 'Plano Pro',
      price: 149,
      description: 'Perfeito para empresas em crescimento',
      features: [
        '50.000 requisições/dia',
        'Todas as moedas',
        'Dados de 1 ano',
        'WebSockets em tempo real',
        'Suporte prioritário',
      ],
      subscribers: 450,
      status: 'active',
    },
    {
      id: 3,
      name: 'Plano Enterprise',
      price: 499,
      description: 'Solução completa para grandes empresas',
      features: [
        '500.000 requisições/dia',
        'API dedicada',
        'Dados históricos completos',
        'SLA garantido',
        'Suporte 24/7',
        'Personalização de endpoints',
      ],
      subscribers: 120,
      status: 'active',
    },
  ];

  constructor() {}

  ngOnInit(): void {}
}
