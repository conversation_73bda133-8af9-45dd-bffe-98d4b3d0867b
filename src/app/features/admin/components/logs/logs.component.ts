import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { LogEntry } from '../../interfaces/log-entry.interface';

@Component({
  selector: 'app-logs',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './logs.component.html',
})
export class LogsComponent {
  logs: LogEntry[] = [
    {
      id: '1',
      timestamp: new Date(),
      level: 'error',
      message: 'Falha na autenticação da API Key',
      source: 'auth-service',
      details: {
        apiKey: '***',
        ip: '***********',
        reason: 'Chave expirada',
      },
    },
    {
      id: '2',
      timestamp: new Date(Date.now() - 3600000),
      level: 'warning',
      message: 'Alta taxa de requisições detectada',
      source: 'rate-limiter',
      details: {
        endpoint: '/v1/rates',
        requests: 1000,
        threshold: 800,
      },
    },
    {
      id: '3',
      timestamp: new Date(Date.now() - 7200000),
      level: 'info',
      message: 'Backup do banco de dados concluído',
      source: 'backup-service',
      details: {
        size: '2.5GB',
        duration: '15min',
      },
    },
  ];

  selectedLog: LogEntry | null = null;
  searchTerm: string = '';
  selectedLevel: string = 'all';

  get filteredLogs(): LogEntry[] {
    return this.logs.filter((log) => {
      const matchesSearch =
        log.message.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        log.source.toLowerCase().includes(this.searchTerm.toLowerCase());
      const matchesLevel =
        this.selectedLevel === 'all' || log.level === this.selectedLevel;
      return matchesSearch && matchesLevel;
    });
  }

  selectLog(log: LogEntry) {
    this.selectedLog = log;
  }

  clearSelection() {
    this.selectedLog = null;
  }

  getLevelColor(level: string): string {
    switch (level) {
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'info':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }
}
