<div class="bg-white shadow rounded-lg p-6">
  <h2 class="text-lg font-medium text-gray-900 mb-6">Logs do Sistema</h2>

  <!-- Filtros -->
  <div class="mb-6 flex flex-col md:flex-row gap-4">
    <div class="flex-1">
      <input
        type="text"
        [(ngModel)]="searchTerm"
        placeholder="Buscar por mensagem ou fonte..."
        class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>
    <div class="w-full md:w-48">
      <select
        [(ngModel)]="selectedLevel"
        class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option value="all">Todos os Níveis</option>
        <option value="error">Erro</option>
        <option value="warning">Alerta</option>
        <option value="info">Info</option>
      </select>
    </div>
  </div>

  <!-- Lista de Logs -->
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Timestamp
          </th>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Nível
          </th>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Mensagem
          </th>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Fonte
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        @for (log of filteredLogs; track log.id) {
        <tr class="hover:bg-gray-50 cursor-pointer" (click)="selectLog(log)">
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            {{ log.timestamp | date : "dd/MM/yyyy HH:mm:ss" }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
              [ngClass]="getLevelColor(log.level)"
            >
              {{ log.level | uppercase }}
            </span>
          </td>
          <td class="px-6 py-4 text-sm text-gray-900">
            {{ log.message }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            {{ log.source }}
          </td>
        </tr>
        }
      </tbody>
    </table>
  </div>

  @if (filteredLogs.length === 0) {
  <div class="text-center py-8 text-gray-500">Nenhum log encontrado.</div>
  }

  <!-- Modal de Detalhes -->
  @if (selectedLog) {
  <div
    class="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50"
  >
    <div class="bg-white rounded-lg shadow-lg max-w-2xl w-full p-6 relative">
      <button
        (click)="clearSelection()"
        class="absolute top-2 right-2 text-gray-400 hover:text-gray-700"
      >
        ✕
      </button>

      <h3 class="text-lg font-bold text-gray-900 mb-4">Detalhes do Log</h3>

      <div class="space-y-4">
        <div>
          <h4 class="text-sm font-medium text-gray-500">Timestamp</h4>
          <p class="mt-1 text-sm text-gray-900">
            {{ selectedLog.timestamp | date : "dd/MM/yyyy HH:mm:ss" }}
          </p>
        </div>

        <div>
          <h4 class="text-sm font-medium text-gray-500">Nível</h4>
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1"
            [ngClass]="getLevelColor(selectedLog.level)"
          >
            {{ selectedLog.level | uppercase }}
          </span>
        </div>

        <div>
          <h4 class="text-sm font-medium text-gray-500">Mensagem</h4>
          <p class="mt-1 text-sm text-gray-900">{{ selectedLog.message }}</p>
        </div>

        <div>
          <h4 class="text-sm font-medium text-gray-500">Fonte</h4>
          <p class="mt-1 text-sm text-gray-900">{{ selectedLog.source }}</p>
        </div>

        @if (selectedLog.details) {
        <div>
          <h4 class="text-sm font-medium text-gray-500">Detalhes</h4>
          <pre class="mt-1 text-sm bg-gray-50 rounded p-2 overflow-x-auto">
                {{ selectedLog.details | json }}
              </pre
          >
        </div>
        }
      </div>
    </div>
  </div>
  }
</div>
