<div class="bg-white shadow rounded-lg p-6">
  <h2 class="text-lg font-medium text-gray-900 mb-6">
    Configurações do Sistema
  </h2>

  <form
    [formGroup]="settingsForm"
    (ngSubmit)="saveSettings()"
    class="space-y-6"
  >
    @for (setting of settings; track setting.id) {
    <div class="border-b border-gray-200 pb-6">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <label
            [for]="setting.id"
            class="block text-sm font-medium text-gray-700"
          >
            {{ setting.name }}
          </label>
          <p class="mt-1 text-sm text-gray-500">{{ setting.description }}</p>
        </div>

        <div class="ml-6">
          @switch (setting.type) { @case ('number') {
          <input
            type="number"
            [id]="setting.id"
            [formControlName]="setting.id"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          />
          } @case ('boolean') {
          <div class="relative flex items-center">
            <input
              type="checkbox"
              [id]="setting.id"
              [formControlName]="setting.id"
              class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
          </div>
          } @case ('select') {
          <select
            [id]="setting.id"
            [formControlName]="setting.id"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          >
            @for (option of setting.options; track option) {
            <option [value]="option">{{ option | titlecase }}</option>
            }
          </select>
          } @default {
          <input
            type="text"
            [id]="setting.id"
            [formControlName]="setting.id"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          />
          } }
        </div>
      </div>
    </div>
    }

    <div class="flex justify-end space-x-3">
      <button
        type="button"
        (click)="resetSettings()"
        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        Restaurar Padrões
      </button>
      <button
        type="submit"
        [disabled]="settingsForm.invalid || isSaving"
        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        @if (isSaving) {
        <span class="inline-flex items-center">
          <svg
            class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          Salvando...
        </span>
        } @else { Salvar Configurações }
      </button>
    </div>
  </form>

  @if (saveSuccess) {
  <div class="mt-4 p-4 bg-green-50 text-green-700 rounded-md">
    Configurações salvas com sucesso!
  </div>
  } @if (saveError) {
  <div class="mt-4 p-4 bg-red-50 text-red-700 rounded-md">
    Erro ao salvar configurações. Tente novamente.
  </div>
  }
</div>
