import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { SystemSetting } from '../../interfaces/system-setting.interface';

@Component({
  selector: 'app-system-settings',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './system-settings.component.html',
})
export class SystemSettingsComponent {
  settings: SystemSetting[] = [
    {
      id: 'rate-limit',
      name: 'Limite de Requisições',
      value: 1000,
      type: 'number',
      description: 'Número máximo de requisições por minuto por API Key',
    },
    {
      id: 'maintenance-mode',
      name: 'Modo de Manutenção',
      value: false,
      type: 'boolean',
      description: 'Ativa o modo de manutenção do sistema',
    },
    {
      id: 'log-level',
      name: 'Nível de Log',
      value: 'info',
      type: 'select',
      options: ['debug', 'info', 'warning', 'error'],
      description: 'Nível de detalhamento dos logs do sistema',
    },
    {
      id: 'backup-frequency',
      name: 'Frequência de Backup',
      value: 'daily',
      type: 'select',
      options: ['hourly', 'daily', 'weekly'],
      description: 'Frequência de backup do banco de dados',
    },
  ];

  settingsForm: FormGroup;
  isSaving = false;
  saveSuccess = false;
  saveError = false;

  constructor(private fb: FormBuilder) {
    const formGroup: { [key: string]: any } = {};
    this.settings.forEach((setting) => {
      formGroup[setting.id] = [setting.value, Validators.required];
    });
    this.settingsForm = this.fb.group(formGroup);
  }

  saveSettings() {
    if (this.settingsForm.invalid) return;

    this.isSaving = true;
    this.saveSuccess = false;
    this.saveError = false;

    // Simulação de salvamento
    setTimeout(() => {
      this.isSaving = false;
      this.saveSuccess = true;

      // Atualiza os valores no array de settings
      this.settings.forEach((setting) => {
        setting.value = this.settingsForm.get(setting.id)?.value;
      });

      // Esconde a mensagem de sucesso após 3 segundos
      setTimeout(() => {
        this.saveSuccess = false;
      }, 3000);
    }, 1000);
  }

  resetSettings() {
    this.settingsForm.reset();
    this.settings.forEach((setting) => {
      this.settingsForm.get(setting.id)?.setValue(setting.value);
    });
  }
}
