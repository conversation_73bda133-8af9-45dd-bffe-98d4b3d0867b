import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import {
  EndpointUsage,
  UsageMetric,
} from '../../interfaces/usage-metric.interface';

@Component({
  selector: 'app-usage-monitoring',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './usage-monitoring.component.html',
})
export class UsageMonitoringComponent {
  metrics: UsageMetric[] = [
    {
      id: '1',
      name: 'Total de Requisições',
      value: 125000,
      change: 12.5,
      trend: 'up',
    },
    {
      id: '2',
      name: 'Usuários Ativos',
      value: 850,
      change: 5.2,
      trend: 'up',
    },
    {
      id: '3',
      name: 'Taxa de Erro',
      value: 0.8,
      change: -0.3,
      trend: 'down',
    },
    {
      id: '4',
      name: 'Tempo Médio de Resposta',
      value: 120,
      change: -15,
      trend: 'down',
    },
  ];

  endpointUsage: EndpointUsage[] = [
    {
      endpoint: '/v1/rates',
      requests: 45000,
      errors: 120,
      avgResponseTime: 95,
    },
    {
      endpoint: '/v1/convert',
      requests: 35000,
      errors: 85,
      avgResponseTime: 150,
    },
    {
      endpoint: '/v1/historical',
      requests: 25000,
      errors: 45,
      avgResponseTime: 200,
    },
  ];

  getTrendIcon(trend: 'up' | 'down' | 'stable'): string {
    switch (trend) {
      case 'up':
        return '↑';
      case 'down':
        return '↓';
      default:
        return '→';
    }
  }

  getTrendColor(trend: 'up' | 'down' | 'stable'): string {
    switch (trend) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  }
}
