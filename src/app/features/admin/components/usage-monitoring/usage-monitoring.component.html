<div class="bg-white shadow rounded-lg p-6">
  <h2 class="text-lg font-medium text-gray-900 mb-6">Monitoramento de Uso</h2>

  <!-- Métricas Principais -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
    @for (metric of metrics; track metric.id) {
    <div class="bg-gray-50 rounded-lg p-4">
      <h3 class="text-sm font-medium text-gray-500">{{ metric.name }}</h3>
      <div class="mt-2 flex items-baseline">
        <p class="text-2xl font-semibold text-gray-900">
          @if (metric.name === 'Taxa de Erro') {
          {{ metric.value }}% } @else if (metric.name === 'Tempo Médio de
          Resposta') { {{ metric.value }}ms } @else {
          {{ metric.value | number }}
          }
        </p>
        <span
          class="ml-2 text-sm font-medium"
          [ngClass]="getTrendColor(metric.trend)"
        >
          {{ getTrendIcon(metric.trend) }} {{ metric.change }}%
        </span>
      </div>
    </div>
    }
  </div>

  <!-- Uso por Endpoint -->
  <div class="mt-8">
    <h3 class="text-base font-medium text-gray-900 mb-4">Uso por Endpoint</h3>
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Endpoint
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Requisições
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Erros
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Tempo Médio
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          @for (endpoint of endpointUsage; track endpoint.endpoint) {
          <tr>
            <td
              class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
            >
              {{ endpoint.endpoint }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ endpoint.requests | number }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
              <span
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                [ngClass]="{
                  'bg-red-100 text-red-800': endpoint.errors > 100,
                  'bg-yellow-100 text-yellow-800':
                    endpoint.errors > 50 && endpoint.errors <= 100,
                  'bg-green-100 text-green-800': endpoint.errors <= 50
                }"
              >
                {{ endpoint.errors }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ endpoint.avgResponseTime }}ms
            </td>
          </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
</div>
