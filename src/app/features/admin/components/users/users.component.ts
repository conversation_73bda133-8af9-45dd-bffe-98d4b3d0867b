import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { LucideAngularModule } from 'lucide-angular';

interface User {
  id: number;
  name: string;
  email: string;
  phone: string;
  plan: string;
  status: 'active' | 'inactive' | 'suspended';
  lastLogin: string;
}

@Component({
  selector: 'app-users',
  standalone: true,
  imports: [CommonModule, FormsModule, LucideAngularModule],
  template: `
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Gerenciamento de Usuários</h1>
        <div class="relative">
          <input
            type="text"
            placeholder="Buscar usuários..."
            class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            [(ngModel)]="searchTerm"
            (input)="filterUsers()"
          />
          <lucide-icon
            name="search"
            class="absolute left-3 top-2.5 icon-sm text-gray-400"
          ></lucide-icon>
        </div>
      </div>

      <div class="card overflow-hidden">
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50">
              <tr>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Usuário
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Plano
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Último Login
                </th>
                <th
                  class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Ações
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              @for (user of filteredUsers; track user.id) {
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div>
                      <div class="text-sm font-medium text-gray-900">
                        {{ user.name }}
                      </div>
                      <div
                        class="text-sm text-gray-500 flex items-center gap-2"
                      >
                        <lucide-icon name="mail" class="icon-sm" />
                        {{ user.email }}
                      </div>
                      <div
                        class="text-sm text-gray-500 flex items-center gap-2"
                      >
                        <lucide-icon name="phone" class="icon-sm" />
                        {{ user.phone }}
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800"
                  >
                    {{ user.plan }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    [class]="getStatusClass(user.status)"
                  >
                    {{ user.status }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ user.lastLogin }}
                </td>
                <td
                  class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
                >
                  <button class="text-gray-400 hover:text-gray-500">
                    <lucide-icon name="more-vertical" class="icon-sm" />
                  </button>
                </td>
              </tr>
              }
            </tbody>
          </table>
        </div>
      </div>
    </div>
  `,
})
export class UsersComponent implements OnInit {
  users: User[] = [
    {
      id: 1,
      name: 'João Silva',
      email: '<EMAIL>',
      phone: '(11) 99999-9999',
      plan: 'Pro',
      status: 'active',
      lastLogin: '2024-02-20 14:30',
    },
    {
      id: 2,
      name: 'Maria Santos',
      email: '<EMAIL>',
      phone: '(11) 98888-8888',
      plan: 'Basic',
      status: 'active',
      lastLogin: '2024-02-20 13:15',
    },
    {
      id: 3,
      name: 'Pedro Oliveira',
      email: '<EMAIL>',
      phone: '(11) 97777-7777',
      plan: 'Enterprise',
      status: 'suspended',
      lastLogin: '2024-02-19 09:45',
    },
  ];

  filteredUsers: User[] = [];
  searchTerm: string = '';

  constructor() {}

  ngOnInit(): void {
    this.filteredUsers = [...this.users];
  }

  filterUsers(): void {
    if (!this.searchTerm) {
      this.filteredUsers = [...this.users];
      return;
    }

    const term = this.searchTerm.toLowerCase();
    this.filteredUsers = this.users.filter(
      (user) =>
        user.name.toLowerCase().includes(term) ||
        user.email.toLowerCase().includes(term) ||
        user.phone.includes(term)
    );
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'suspended':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }
}
