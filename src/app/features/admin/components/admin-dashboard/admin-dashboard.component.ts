import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
  Activity,
  BarChart3,
  CreditCard,
  LucideAngularModule,
  Users,
} from 'lucide-angular';

interface DashboardMetric {
  title: string;
  value: number;
  icon: any;
  change: number;
}

@Component({
  selector: 'app-admin-dashboard',
  standalone: true,
  imports: [CommonModule, LucideAngularModule],
  template: `
    <div class="p-6">
      <h1 class="text-2xl font-bold mb-6">Dashboard Administrativo</h1>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        @for (metric of metrics; track metric.title) {
        <div class="card p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">{{ metric.title }}</p>
              <h3 class="text-2xl font-bold mt-1">{{ metric.value }}</h3>
              <p
                class="text-sm"
                [class]="metric.change >= 0 ? 'text-green-500' : 'text-red-500'"
              >
                {{ metric.change >= 0 ? '+' : '' }}{{ metric.change }}% desde o
                último mês
              </p>
            </div>
            <div class="icon-sm text-primary">
              <ng-container [ngComponentOutlet]="metric.icon"></ng-container>
            </div>
          </div>
        </div>
        }
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        <div class="card p-4">
          <h2 class="card-title mb-4">Atividade Recente</h2>
          @for (activity of recentActivities; track activity.id) {
          <div
            class="flex items-center justify-between py-2 border-b last:border-0"
          >
            <div>
              <p class="font-medium">{{ activity.description }}</p>
              <p class="text-sm text-gray-500">{{ activity.time }}</p>
            </div>
            <span class="text-sm" [class]="getStatusClass(activity.status)">
              {{ activity.status }}
            </span>
          </div>
          }
        </div>

        <div class="card p-4">
          <h2 class="card-title mb-4">Planos Populares</h2>
          @for (plan of popularPlans; track plan.name) {
          <div
            class="flex items-center justify-between py-2 border-b last:border-0"
          >
            <div>
              <p class="font-medium">{{ plan.name }}</p>
              <p class="text-sm text-gray-500">
                {{ plan.subscribers }} assinantes
              </p>
            </div>
            <span class="text-sm text-primary">R$ {{ plan.price }}/mês</span>
          </div>
          }
        </div>
      </div>
    </div>
  `,
})
export class AdminDashboardComponent implements OnInit {
  metrics: DashboardMetric[] = [
    {
      title: 'Usuários Totais',
      value: 1234,
      icon: Users,
      change: 12,
    },
    {
      title: 'Receita Mensal',
      value: 45678,
      icon: CreditCard,
      change: 8,
    },
    {
      title: 'Requisições API',
      value: 98765,
      icon: Activity,
      change: 15,
    },
    {
      title: 'Taxa de Conversão',
      value: 3.2,
      icon: BarChart3,
      change: -2,
    },
  ];

  recentActivities = [
    {
      id: 1,
      description: 'Novo usuário registrado',
      time: '5 minutos atrás',
      status: 'Sucesso',
    },
    {
      id: 2,
      description: 'Upgrade de plano',
      time: '15 minutos atrás',
      status: 'Processando',
    },
    {
      id: 3,
      description: 'Falha na requisição API',
      time: '1 hora atrás',
      status: 'Erro',
    },
  ];

  popularPlans = [
    {
      name: 'Plano Pro',
      subscribers: 450,
      price: 149,
    },
    {
      name: 'Plano Basic',
      subscribers: 780,
      price: 49,
    },
    {
      name: 'Plano Enterprise',
      subscribers: 120,
      price: 499,
    },
  ];

  constructor() {}

  ngOnInit(): void {}

  getStatusClass(status: string): string {
    switch (status) {
      case 'Sucesso':
        return 'text-green-500';
      case 'Processando':
        return 'text-yellow-500';
      case 'Erro':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  }
}
