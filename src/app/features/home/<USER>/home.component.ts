import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import {
  CheckIcon,
  Clock3Icon,
  CodeIcon,
  CreditCardIcon,
  GlobeIcon,
  LucideAngularModule,
  ShieldIcon,
  StarIcon,
  TrendingUpIcon,
  UsersIcon,
  ZapIcon,
} from 'lucide-angular';
import { TranslationService } from '../../../core/services/translation.service';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, LucideAngularModule, RouterModule, TranslateModule],
  templateUrl: './home.component.html',
  styleUrl: './home.component.css',
})
export class HomeComponent implements OnInit {
  readonly ZapIcon = ZapIcon;
  readonly ShieldIcon = ShieldIcon;
  readonly CodeIcon = CodeIcon;
  readonly CheckIcon = CheckIcon;
  readonly StarIcon = StarIcon;
  readonly UsersIcon = UsersIcon;
  readonly TrendingUpIcon = TrendingUpIcon;
  readonly GlobeIcon = GlobeIcon;
  readonly Clock3Icon = Clock3Icon;
  readonly CreditCardIcon = CreditCardIcon;

  // Estado de carregamento para animações
  isLoading = false;

  // Dados de exemplo da API
  apiResponseExample = '';

  // Estatísticas da plataforma
  platformStats = {
    users: 0,
    requests: 0,
    uptime: 0,
    countries: 0,
  };

  constructor(public translationService: TranslationService) {}

  ngOnInit(): void {
    this.initializeApiExample();
    this.initializePlatformStats();
    this.animateStats();
  }

  /**
   * Inicializa o exemplo da API com base no idioma atual
   */
  private initializeApiExample(): void {
    const currentLanguage = this.translationService.getCurrentLanguage();

    if (currentLanguage === 'pt') {
      this.apiResponseExample = `{
  "base": "USD",
  "date": "2024-06-10",
  "rates": {
    "BRL": 5.08,
    "EUR": 0.92,
    "JPY": 149.32
  }
}`;
    } else {
      this.apiResponseExample = `{
  "base": "USD",
  "date": "2024-06-10",
  "rates": {
    "EUR": 0.92,
    "GBP": 0.78,
    "JPY": 149.32
  }
}`;
    }
  }

  /**
   * Inicializa as estatísticas da plataforma
   */
  private initializePlatformStats(): void {
    this.platformStats = {
      users: 15420,
      requests: 2850000,
      uptime: 99.9,
      countries: 180,
    };
  }

  /**
   * Anima as estatísticas com contador incremental
   */
  private animateStats(): void {
    // Simula animação de carregamento das estatísticas
    setTimeout(() => {
      this.isLoading = false;
    }, 500);
  }

  /**
   * Rola suavemente para a seção de preços
   */
  scrollToPricing(): void {
    const pricingSection = document.getElementById('pricing-section');
    if (pricingSection) {
      pricingSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }
}
