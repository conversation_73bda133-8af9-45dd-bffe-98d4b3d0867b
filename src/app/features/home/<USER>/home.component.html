<!-- Hero Section -->
<section class="bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-20">
  <div class="container-padded">
    <div class="text-center mb-16 animate-fade-in">
      <h1
        class="text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight"
      >
        {{ "home.hero.title" | translate }}
      </h1>
      <p class="text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto mb-8">
        {{ "home.hero.subtitle" | translate }}
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <button
          (click)="scrollToPricing()"
          class="btn-primary px-8 py-4 text-lg pulse-effect"
        >
          <i-lucide [img]="TrendingUpIcon" class="icon-md mr-2"></i-lucide>
          {{ "home.hero.startButton" | translate }}
        </button>
        <a
          routerLink="/playground"
          class="btn-secondary px-8 py-4 text-lg card-hover-effect"
        >
          <i-lucide [img]="CodeIcon" class="icon-md mr-2"></i-lucide>
          {{ "home.hero.tryButton" | translate }}
        </a>
      </div>
    </div>

    <!-- Platform Statistics -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16 animate-slide-up">
      <div
        class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 text-center card-hover-effect animate-count-up"
      >
        <div class="text-3xl font-bold text-blue-600 mb-2">
          {{ platformStats.users | number }}+
        </div>
        <div class="text-sm text-gray-600 font-medium">
          {{ "home.stats.developers" | translate }}
        </div>
      </div>
      <div
        class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 text-center card-hover-effect animate-count-up"
      >
        <div class="text-3xl font-bold text-green-600 mb-2">
          {{ platformStats.requests | number }}+
        </div>
        <div class="text-sm text-gray-600 font-medium">
          {{ "home.stats.requests" | translate }}
        </div>
      </div>
      <div
        class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 text-center card-hover-effect animate-count-up"
      >
        <div class="text-3xl font-bold text-purple-600 mb-2">
          {{ platformStats.uptime }}%
        </div>
        <div class="text-sm text-gray-600 font-medium">
          {{ "home.stats.uptime" | translate }}
        </div>
      </div>
      <div
        class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 text-center card-hover-effect animate-count-up"
      >
        <div class="text-3xl font-bold text-orange-600 mb-2">
          {{ platformStats.countries }}+
        </div>
        <div class="text-sm text-gray-600 font-medium">
          {{ "home.stats.countries" | translate }}
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Features Section -->
<section class="py-20 bg-white">
  <div class="container-padded">
    <div class="text-center mb-16 animate-fade-in">
      <h2 class="section-title mb-6">
        {{ "home.features.title" | translate }}
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        {{ "home.features.subtitle" | translate }}
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
      <div
        class="bg-white rounded-xl p-8 shadow-lg border border-gray-100 card-hover-effect animate-fade-in"
      >
        <div
          class="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mb-6 icon-bounce"
        >
          <i-lucide [img]="ZapIcon" class="icon-xl text-blue-600"></i-lucide>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3">
          {{ "home.features.fast.title" | translate }}
        </h3>
        <p class="text-gray-600 leading-relaxed">
          {{ "home.features.fast.description" | translate }}
        </p>
      </div>

      <div
        class="bg-white rounded-xl p-8 shadow-lg border border-gray-100 card-hover-effect animate-fade-in"
        style="animation-delay: 0.2s"
      >
        <div
          class="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mb-6 icon-bounce"
          style="animation-delay: 0.5s"
        >
          <i-lucide
            [img]="ShieldIcon"
            class="icon-xl text-green-600"
          ></i-lucide>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3">
          {{ "home.features.secure.title" | translate }}
        </h3>
        <p class="text-gray-600 leading-relaxed">
          {{ "home.features.secure.description" | translate }}
        </p>
      </div>

      <div
        class="bg-white rounded-xl p-8 shadow-lg border border-gray-100 card-hover-effect animate-fade-in"
        style="animation-delay: 0.4s"
      >
        <div
          class="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mb-6 icon-bounce"
          style="animation-delay: 1s"
        >
          <i-lucide [img]="CodeIcon" class="icon-xl text-purple-600"></i-lucide>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3">
          {{ "home.features.easy.title" | translate }}
        </h3>
        <p class="text-gray-600 leading-relaxed">
          {{ "home.features.easy.description" | translate }}
        </p>
      </div>
    </div>
  </div>
</section>

<!-- API Demo Section -->
<section class="py-20 bg-gray-50">
  <div class="container-padded">
    <div class="text-center mb-16 animate-fade-in">
      <h2 class="section-title mb-6">{{ "home.demo.title" | translate }}</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        {{ "home.demo.subtitle" | translate }}
      </p>
    </div>

    <div
      class="bg-white rounded-xl p-8 shadow-lg border border-gray-100 max-w-4xl mx-auto animate-slide-up"
    >
      <div class="mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <i-lucide
            [img]="CodeIcon"
            class="icon-md mr-2 text-blue-600"
          ></i-lucide>
          {{ "home.demo.requestTitle" | translate }}
        </h3>
        <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
          <pre
            class="text-green-400 text-sm"
          ><code>curl -X GET "https://api.currencywise.com/latest?base=USD&symbols=EUR,BRL,JPY" \
-H "Authorization: Bearer sua_api_key"</code></pre>
        </div>
      </div>

      <div>
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <i-lucide
            [img]="CheckIcon"
            class="icon-md mr-2 text-green-600"
          ></i-lucide>
          {{ "home.demo.responseTitle" | translate }}
        </h3>
        <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
          <pre
            class="text-blue-400 text-sm"
          ><code>{{ apiResponseExample }}</code></pre>
        </div>
      </div>

      <div class="mt-6 text-center">
        <a
          routerLink="/playground"
          class="btn-primary px-6 py-3 card-hover-effect"
        >
          <i-lucide [img]="CodeIcon" class="icon-sm mr-2"></i-lucide>
          {{ "home.demo.tryButton" | translate }}
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Pricing Section -->
<section id="pricing-section" class="py-20 bg-white">
  <div class="container-padded">
    <div class="text-center mb-16 animate-fade-in">
      <h2 class="section-title mb-6">{{ "home.pricing.title" | translate }}</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        {{ "home.pricing.subtitle" | translate }}
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
      <!-- Plano Básico -->
      <div
        class="bg-white rounded-xl p-8 shadow-lg border border-gray-200 card-hover-effect animate-fade-in"
      >
        <div class="text-center mb-8">
          <div
            class="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4"
          >
            <i-lucide
              [img]="UsersIcon"
              class="icon-xl text-gray-600"
            ></i-lucide>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-2">
            {{ "home.pricing.basic.title" | translate }}
          </h3>
          <div class="text-4xl font-bold text-gray-900 mb-2">
            {{ "home.pricing.basic.price" | translate }}
          </div>
          <p class="text-gray-600">
            {{ "home.pricing.basic.period" | translate }}
          </p>
        </div>

        <ul class="space-y-4 mb-8">
          <li class="flex items-center">
            <i-lucide
              [img]="CheckIcon"
              class="icon-sm text-green-600 mr-3"
            ></i-lucide>
            <span class="text-gray-700">{{
              "home.pricing.basic.feature1" | translate
            }}</span>
          </li>
          <li class="flex items-center">
            <i-lucide
              [img]="CheckIcon"
              class="icon-sm text-green-600 mr-3"
            ></i-lucide>
            <span class="text-gray-700">{{
              "home.pricing.basic.feature2" | translate
            }}</span>
          </li>
          <li class="flex items-center">
            <i-lucide
              [img]="CheckIcon"
              class="icon-sm text-green-600 mr-3"
            ></i-lucide>
            <span class="text-gray-700">{{
              "home.pricing.basic.feature3" | translate
            }}</span>
          </li>
        </ul>

        <a
          routerLink="/auth/register"
          class="btn-secondary w-full justify-center card-hover-effect"
        >
          {{ "home.pricing.basic.button" | translate }}
        </a>
      </div>

      <!-- Plano Profissional (Destacado) -->
      <div
        class="bg-white rounded-xl p-8 shadow-2xl border-2 border-blue-500 card-hover-effect animate-fade-in transform scale-105 relative"
        style="animation-delay: 0.2s"
      >
        <div
          class="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-bold"
        >
          {{ "home.pricing.professional.badge" | translate }}
        </div>

        <div class="text-center mb-8">
          <div
            class="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4"
          >
            <i-lucide
              [img]="TrendingUpIcon"
              class="icon-xl text-blue-600"
            ></i-lucide>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-2">
            {{ "home.pricing.professional.title" | translate }}
          </h3>
          <div class="text-4xl font-bold text-blue-600 mb-2">
            {{ "home.pricing.professional.price" | translate }}
          </div>
          <p class="text-gray-600">
            {{ "home.pricing.professional.period" | translate }}
          </p>
        </div>

        <ul class="space-y-4 mb-8">
          <li class="flex items-center">
            <i-lucide
              [img]="CheckIcon"
              class="icon-sm text-green-600 mr-3"
            ></i-lucide>
            <span class="text-gray-700">{{
              "home.pricing.professional.feature1" | translate
            }}</span>
          </li>
          <li class="flex items-center">
            <i-lucide
              [img]="CheckIcon"
              class="icon-sm text-green-600 mr-3"
            ></i-lucide>
            <span class="text-gray-700">{{
              "home.pricing.professional.feature2" | translate
            }}</span>
          </li>
          <li class="flex items-center">
            <i-lucide
              [img]="CheckIcon"
              class="icon-sm text-green-600 mr-3"
            ></i-lucide>
            <span class="text-gray-700">{{
              "home.pricing.professional.feature3" | translate
            }}</span>
          </li>
          <li class="flex items-center">
            <i-lucide
              [img]="CheckIcon"
              class="icon-sm text-green-600 mr-3"
            ></i-lucide>
            <span class="text-gray-700">{{
              "home.pricing.professional.feature4" | translate
            }}</span>
          </li>
        </ul>

        <a
          routerLink="/subscriptions"
          class="btn-primary w-full justify-center pulse-effect"
        >
          {{ "home.pricing.professional.button" | translate }}
        </a>
      </div>

      <!-- Plano Empresarial -->
      <div
        class="bg-white rounded-xl p-8 shadow-lg border border-gray-200 card-hover-effect animate-fade-in"
        style="animation-delay: 0.4s"
      >
        <div class="text-center mb-8">
          <div
            class="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4"
          >
            <i-lucide
              [img]="GlobeIcon"
              class="icon-xl text-purple-600"
            ></i-lucide>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-2">
            {{ "home.pricing.enterprise.title" | translate }}
          </h3>
          <div class="text-4xl font-bold text-gray-900 mb-2">
            {{ "home.pricing.enterprise.price" | translate }}
          </div>
          <p class="text-gray-600">
            {{ "home.pricing.enterprise.period" | translate }}
          </p>
        </div>

        <ul class="space-y-4 mb-8">
          <li class="flex items-center">
            <i-lucide
              [img]="CheckIcon"
              class="icon-sm text-green-600 mr-3"
            ></i-lucide>
            <span class="text-gray-700">{{
              "home.pricing.enterprise.feature1" | translate
            }}</span>
          </li>
          <li class="flex items-center">
            <i-lucide
              [img]="CheckIcon"
              class="icon-sm text-green-600 mr-3"
            ></i-lucide>
            <span class="text-gray-700">{{
              "home.pricing.enterprise.feature2" | translate
            }}</span>
          </li>
          <li class="flex items-center">
            <i-lucide
              [img]="CheckIcon"
              class="icon-sm text-green-600 mr-3"
            ></i-lucide>
            <span class="text-gray-700">{{
              "home.pricing.enterprise.feature3" | translate
            }}</span>
          </li>
          <li class="flex items-center">
            <i-lucide
              [img]="CheckIcon"
              class="icon-sm text-green-600 mr-3"
            ></i-lucide>
            <span class="text-gray-700">{{
              "home.pricing.enterprise.feature4" | translate
            }}</span>
          </li>
          <li class="flex items-center">
            <i-lucide
              [img]="CheckIcon"
              class="icon-sm text-green-600 mr-3"
            ></i-lucide>
            <span class="text-gray-700">{{
              "home.pricing.enterprise.feature5" | translate
            }}</span>
          </li>
        </ul>

        <a
          routerLink="/sales"
          class="btn-secondary w-full justify-center card-hover-effect"
        >
          {{ "home.pricing.enterprise.button" | translate }}
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Call to Action Section -->
<section class="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
  <div class="container-padded text-center">
    <div class="max-w-4xl mx-auto animate-fade-in">
      <h2 class="text-4xl lg:text-5xl font-bold mb-6">
        {{ "home.cta.title" | translate }}
      </h2>
      <p class="text-xl lg:text-2xl mb-8 text-blue-100">
        {{ "home.cta.subtitle" | translate }}
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <a
          routerLink="/auth/register"
          class="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 card-hover-effect"
        >
          <i-lucide [img]="TrendingUpIcon" class="icon-md mr-2"></i-lucide>
          {{ "home.cta.primaryButton" | translate }}
        </a>
        <a
          routerLink="/documentation"
          class="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 card-hover-effect"
        >
          <i-lucide [img]="CodeIcon" class="icon-md mr-2"></i-lucide>
          {{ "home.cta.secondaryButton" | translate }}
        </a>
      </div>
    </div>
  </div>
</section>
