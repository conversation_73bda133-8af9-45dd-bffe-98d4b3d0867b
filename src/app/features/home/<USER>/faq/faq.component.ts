import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';

interface FaqItem {
  question: string;
  answer: string;
  isOpen: boolean;
}

@Component({
  selector: 'app-faq',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './faq.component.html',
})
export class FaqComponent {
  faqItems: FaqItem[] = [
    {
      question: 'Como posso começar a usar a API?',
      answer:
        'Para começar, você precisa criar uma conta, gerar uma API key e seguir nossa documentação para fazer sua primeira requisição.',
      isOpen: false,
    },
    {
      question: 'Quais são os limites de requisições?',
      answer:
        'Os limites variam de acordo com seu plano. O plano gratuito permite 1000 requisições por mês, enquanto os planos pagos oferecem limites maiores.',
      isOpen: false,
    },
    {
      question: 'Como funciona o sistema de preços?',
      answer:
        'Oferecemos um plano gratuito e planos pagos com diferentes níveis de uso. Os preços são baseados no volume de requisições e recursos adicionais.',
      isOpen: false,
    },
    {
      question: 'A API suporta quais moedas?',
      answer:
        'Nossa API suporta mais de 170 moedas diferentes, incluindo todas as principais moedas mundiais e criptomoedas populares.',
      isOpen: false,
    },
    {
      question: 'Como posso obter suporte?',
      answer:
        'Oferecemos suporte por email, chat e documentação detalhada. Usuários de planos pagos têm acesso a suporte prioritário.',
      isOpen: false,
    },
  ];

  toggleFaq(index: number): void {
    this.faqItems[index].isOpen = !this.faqItems[index].isOpen;
  }
}
