<div class="max-w-3xl mx-auto p-4">
  <h2 class="text-2xl font-bold mb-6 text-gray-800">Perguntas Frequentes</h2>

  <div class="space-y-4">
    @for (item of faqItems; track item.question) {
    <div class="border rounded-lg overflow-hidden">
      <button
        (click)="toggleFaq($index)"
        class="w-full px-6 py-4 text-left bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <div class="flex justify-between items-center">
          <span class="font-medium text-gray-900">{{ item.question }}</span>
          <span class="text-gray-500">
            @if (item.isOpen) {
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M5 15l7-7 7 7"
              />
            </svg>
            } @else {
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
            }
          </span>
        </div>
      </button>

      @if (item.isOpen) {
      <div class="px-6 py-4 bg-gray-50">
        <p class="text-gray-600">{{ item.answer }}</p>
      </div>
      }
    </div>
    }
  </div>
</div>
