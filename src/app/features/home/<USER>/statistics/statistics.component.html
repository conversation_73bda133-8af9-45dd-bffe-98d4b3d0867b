<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4">
  @for (stat of statistics; track stat.title) {
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm text-gray-600">{{ stat.title }}</p>
        <h3 class="text-2xl font-bold mt-1">{{ stat.value }}</h3>
      </div>
      <div class="p-3 bg-blue-50 rounded-full">
        <ng-container *ngComponentOutlet="stat.icon" />
      </div>
    </div>
    <div class="mt-4 flex items-center">
      <span class="text-sm text-gray-600">{{ stat.description }}</span>
      <span
        class="ml-2 text-sm"
        [ngClass]="{
          'text-green-600': stat.trend === 'up',
          'text-red-600': stat.trend === 'down',
          'text-gray-600': stat.trend === 'neutral'
        }"
      >
        {{ stat.trendValue }}
      </span>
    </div>
  </div>
  }
</div>
