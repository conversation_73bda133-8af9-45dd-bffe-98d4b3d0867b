import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import {
  Activity,
  BarChart3,
  Clock,
  LucideAngularModule,
  Users,
} from 'lucide-angular';

interface Statistic {
  title: string;
  value: string;
  description: string;
  icon: any;
  trend: 'up' | 'down' | 'neutral';
  trendValue: string;
}

@Component({
  selector: 'app-statistics',
  standalone: true,
  imports: [CommonModule, LucideAngularModule],
  templateUrl: './statistics.component.html',
})
export class StatisticsComponent {
  statistics: Statistic[] = [
    {
      title: 'Total de Requisições',
      value: '1.2M',
      description: 'Últimos 30 dias',
      icon: BarChart3,
      trend: 'up',
      trendValue: '+12.5%',
    },
    {
      title: 'Usuários Ativos',
      value: '45.2K',
      description: 'Usuários únicos',
      icon: Users,
      trend: 'up',
      trendValue: '+8.2%',
    },
    {
      title: 'Taxa de Sucesso',
      value: '99.9%',
      description: 'Requisições bem-sucedidas',
      icon: Activity,
      trend: 'neutral',
      trendValue: '0%',
    },
    {
      title: 'Tempo Médio de Resposta',
      value: '45ms',
      description: 'Latência média',
      icon: Clock,
      trend: 'down',
      trendValue: '-5.3%',
    },
  ];
}
