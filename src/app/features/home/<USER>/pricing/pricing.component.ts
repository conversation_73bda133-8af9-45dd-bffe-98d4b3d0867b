import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { LucideAngularModule } from 'lucide-angular';

interface Plan {
  name: string;
  price: number;
  description: string;
  features: string[];
  popular?: boolean;
}

@Component({
  selector: 'app-pricing',
  standalone: true,
  imports: [CommonModule, LucideAngularModule],
  template: `
    <section class="py-20">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-3xl font-bold mb-4">Planos e Preços</h2>
          <p class="text-gray-600 max-w-2xl mx-auto">
            Escolha o plano ideal para o seu negócio. Todos os planos incluem
            suporte técnico e documentação completa.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          @for (plan of plans; track plan.name) {
          <div class="card p-8 relative" [class.border-primary]="plan.popular">
            @if (plan.popular) {
            <div class="absolute -top-4 left-1/2 -translate-x-1/2">
              <span
                class="bg-primary text-white px-4 py-1 rounded-full text-sm"
              >
                Mais Popular
              </span>
            </div>
            }

            <h3 class="text-2xl font-bold mb-2">{{ plan.name }}</h3>
            <div class="mb-4">
              <span class="text-4xl font-bold">R$ {{ plan.price }}</span>
              <span class="text-gray-600">/mês</span>
            </div>
            <p class="text-gray-600 mb-6">{{ plan.description }}</p>

            <ul class="space-y-3 mb-8">
              @for (feature of plan.features; track feature) {
              <li class="flex items-center">
                <lucide-icon
                  name="check"
                  class="icon-sm text-green-500 mr-2"
                ></lucide-icon>
                {{ feature }}
              </li>
              }
            </ul>

            <button
              class="w-full btn-primary"
              [class.btn-secondary]="!plan.popular"
            >
              Começar Agora
            </button>
          </div>
          }
        </div>
      </div>
    </section>
  `,
})
export class PricingComponent {
  plans: Plan[] = [
    {
      name: 'Básico',
      price: 99,
      description: 'Ideal para projetos pequenos e testes',
      features: [
        'Até 1.000 requisições/dia',
        'Dados em tempo real',
        'Suporte por email',
        'Documentação básica',
      ],
    },
    {
      name: 'Profissional',
      price: 299,
      description: 'Perfeito para empresas em crescimento',
      features: [
        'Até 10.000 requisições/dia',
        'Dados em tempo real',
        'Histórico de 30 dias',
        'Suporte prioritário',
        'Documentação completa',
      ],
      popular: true,
    },
    {
      name: 'Enterprise',
      price: 999,
      description: 'Para grandes empresas e aplicações críticas',
      features: [
        'Requisições ilimitadas',
        'Dados em tempo real',
        'Histórico completo',
        'Suporte 24/7',
        'API dedicada',
        'SLA garantido',
      ],
    },
  ];
}
