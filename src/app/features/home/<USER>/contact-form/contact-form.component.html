<div class="max-w-2xl mx-auto p-4">
  <h2 class="text-2xl font-bold mb-6 text-gray-800">Entre em Contato</h2>

  @if (submitSuccess) {
  <div class="mb-4 p-4 bg-green-50 text-green-700 rounded-lg">
    Mensagem enviada com sucesso! Entraremos em contato em breve.
  </div>
  } @if (submitError) {
  <div class="mb-4 p-4 bg-red-50 text-red-700 rounded-lg">
    Ocorreu um erro ao enviar sua mensagem. Por favor, tente novamente.
  </div>
  }

  <form (ngSubmit)="onSubmit()" class="space-y-4">
    <div>
      <label for="name" class="block text-sm font-medium text-gray-700"
        >Nome</label
      >
      <input
        type="text"
        id="name"
        name="name"
        [(ngModel)]="form.name"
        required
        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
      />
    </div>

    <div>
      <label for="email" class="block text-sm font-medium text-gray-700"
        >Email</label
      >
      <input
        type="email"
        id="email"
        name="email"
        [(ngModel)]="form.email"
        required
        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
      />
    </div>

    <div>
      <label for="subject" class="block text-sm font-medium text-gray-700"
        >Assunto</label
      >
      <input
        type="text"
        id="subject"
        name="subject"
        [(ngModel)]="form.subject"
        required
        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
      />
    </div>

    <div>
      <label for="message" class="block text-sm font-medium text-gray-700"
        >Mensagem</label
      >
      <textarea
        id="message"
        name="message"
        rows="4"
        [(ngModel)]="form.message"
        required
        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
      ></textarea>
    </div>

    <div>
      <button
        type="submit"
        [disabled]="isSubmitting"
        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
      >
        @if (isSubmitting) {
        <svg
          class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
        Enviando... } @else { Enviar Mensagem }
      </button>
    </div>
  </form>
</div>
