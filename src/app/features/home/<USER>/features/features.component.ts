import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { LucideAngularModule } from 'lucide-angular';

interface Feature {
  icon: string;
  title: string;
  description: string;
}

@Component({
  selector: 'app-features',
  standalone: true,
  imports: [CommonModule, LucideAngularModule],
  template: `
    <section class="py-20 bg-gray-50">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-3xl font-bold mb-4">Recursos Poderosos</h2>
          <p class="text-gray-600 max-w-2xl mx-auto">
            Nossa API oferece tudo que você precisa para trabalhar com dados de
            câmbio de forma eficiente e confiável.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          @for (feature of features; track feature.title) {
          <div class="card p-6 hover:shadow-lg transition-shadow">
            <div
              class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4"
            >
              <lucide-icon
                [name]="feature.icon"
                class="icon-lg text-primary"
              ></lucide-icon>
            </div>
            <h3 class="text-xl font-semibold mb-2">{{ feature.title }}</h3>
            <p class="text-gray-600">{{ feature.description }}</p>
          </div>
          }
        </div>
      </div>
    </section>
  `,
})
export class FeaturesComponent {
  features: Feature[] = [
    {
      icon: 'clock',
      title: 'Dados em Tempo Real',
      description:
        'Acesse taxas de câmbio atualizadas a cada minuto, garantindo precisão nas suas operações.',
    },
    {
      icon: 'history',
      title: 'Histórico Completo',
      description:
        'Consulte dados históricos de qualquer período, com granularidade de minutos, horas ou dias.',
    },
    {
      icon: 'line-chart',
      title: 'Análise Técnica',
      description:
        'Indicadores técnicos e gráficos para análise de tendências e tomada de decisões.',
    },
    {
      icon: 'shield',
      title: 'Alta Confiabilidade',
      description:
        'Infraestrutura robusta com 99.9% de uptime e redundância em múltiplas regiões.',
    },
    {
      icon: 'zap',
      title: 'Baixa Latência',
      description:
        'Resposta rápida com média de 50ms, ideal para aplicações que precisam de dados em tempo real.',
    },
    {
      icon: 'code',
      title: 'API RESTful',
      description:
        'Documentação completa e SDKs para as principais linguagens de programação.',
    },
  ];
}
