import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { LucideAngularModule } from 'lucide-angular';

@Component({
  selector: 'app-call-to-action',
  standalone: true,
  imports: [CommonModule, LucideAngularModule],
  template: `
    <section class="py-20 bg-primary text-white">
      <div class="container mx-auto px-6">
        <div class="max-w-3xl mx-auto text-center">
          <h2 class="text-3xl font-bold mb-4">
            Comece a usar nossa API hoje mesmo
          </h2>
          <p class="text-lg mb-8 text-gray-100">
            Junte-se a milhares de desenvolvedores e empresas que já estão
            usando nossa API para suas operações de câmbio.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button class="btn-secondary">
              <PERSON><PERSON><PERSON>
              <lucide-icon
                name="arrow-right"
                class="icon-sm ml-2"
              ></lucide-icon>
            </button>
            <button class="btn-outline-white">
              Falar com Vendas
              <lucide-icon name="phone" class="icon-sm ml-2"></lucide-icon>
            </button>
          </div>
          <p class="mt-4 text-sm text-gray-300">
            Não é necessário cartão de crédito. Teste gratuitamente por 14 dias.
          </p>
        </div>
      </div>
    </section>
  `,
})
export class CallToActionComponent {}
