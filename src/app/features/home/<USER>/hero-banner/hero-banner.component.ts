import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { LucideAngularModule } from 'lucide-angular';

@Component({
  selector: 'app-hero-banner',
  standalone: true,
  imports: [CommonModule, LucideAngularModule],
  template: `
    <div
      class="relative bg-gradient-to-r from-primary to-primary-dark text-white"
    >
      <div class="container mx-auto px-6 py-24">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 class="text-4xl md:text-5xl font-bold mb-6">
              API de Câmbio Mais Completa do Brasil
            </h1>
            <p class="text-lg mb-8 text-gray-100">
              Acesse dados de câmbio em tempo real, histórico completo e muito
              mais. Ideal para desenvolvedores e empresas que precisam de dados
              precisos de moedas.
            </p>
            <div class="flex flex-col sm:flex-row gap-4">
              <button class="btn-primary">
                Começar Agora
                <lucide-icon
                  name="arrow-right"
                  class="icon-sm ml-2"
                ></lucide-icon>
              </button>
              <button class="btn-secondary">
                Ver Documentação
                <lucide-icon
                  name="book-open"
                  class="icon-sm ml-2"
                ></lucide-icon>
              </button>
            </div>
          </div>
          <div class="hidden lg:block">
            <div class="card bg-white/10 backdrop-blur-sm p-6">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-semibold">Taxas em Tempo Real</h3>
                <lucide-icon
                  name="refresh-cw"
                  class="icon-sm animate-spin"
                ></lucide-icon>
              </div>
              @for (rate of exchangeRates; track rate.pair) {
              <div
                class="flex items-center justify-between py-2 border-b last:border-0"
              >
                <span class="font-medium">{{ rate.pair }}</span>
                <span class="text-lg">{{ rate.value }}</span>
                <span [class]="getChangeClass(rate.change)">
                  {{ rate.change > 0 ? '+' : '' }}{{ rate.change }}%
                </span>
              </div>
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
})
export class HeroBannerComponent {
  // Dados fake para demonstração
  exchangeRates = [
    {
      pair: 'USD/BRL',
      value: '4.95',
      change: 0.5,
    },
    {
      pair: 'EUR/BRL',
      value: '5.35',
      change: -0.2,
    },
    {
      pair: 'GBP/BRL',
      value: '6.25',
      change: 0.8,
    },
  ];

  // Método para classes de mudança (será movido para um serviço posteriormente)
  getChangeClass(change: number): string {
    if (change > 0) return 'text-green-400';
    if (change < 0) return 'text-red-400';
    return 'text-gray-400';
  }
}
