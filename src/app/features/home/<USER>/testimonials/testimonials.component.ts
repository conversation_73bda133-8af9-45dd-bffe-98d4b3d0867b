import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { LucideAngularModule } from 'lucide-angular';

interface Testimonial {
  name: string;
  role: string;
  company: string;
  content: string;
  avatar: string;
}

@Component({
  selector: 'app-testimonials',
  standalone: true,
  imports: [CommonModule, LucideAngularModule],
  template: `
    <section class="py-20 bg-gray-50">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-3xl font-bold mb-4">O que nossos clientes dizem</h2>
          <p class="text-gray-600 max-w-2xl mx-auto">
            Empresas de todos os tamanhos confiam em nossa API para suas
            operações de câmbio e análise de mercado.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          @for (testimonial of testimonials; track testimonial.name) {
          <div class="card p-6">
            <div class="flex items-center mb-4">
              <img
                [src]="testimonial.avatar"
                [alt]="testimonial.name"
                class="w-12 h-12 rounded-full mr-4"
              />
              <div>
                <h4 class="font-semibold">{{ testimonial.name }}</h4>
                <p class="text-sm text-gray-600">
                  {{ testimonial.role }} na {{ testimonial.company }}
                </p>
              </div>
            </div>
            <p class="text-gray-600">{{ testimonial.content }}</p>
            <div class="mt-4 flex text-yellow-400">
              @for (star of [1,2,3,4,5]; track star) {
              <lucide-icon name="star" class="icon-sm"></lucide-icon>
              }
            </div>
          </div>
          }
        </div>
      </div>
    </section>
  `,
})
export class TestimonialsComponent {
  testimonials: Testimonial[] = [
    {
      name: 'Ana Silva',
      role: 'CTO',
      company: 'Fintech Brasil',
      content:
        'A API de câmbio nos ajudou a reduzir significativamente o tempo de desenvolvimento e melhorar a precisão das nossas operações.',
      avatar: 'https://i.pravatar.cc/150?img=1',
    },
    {
      name: 'Carlos Santos',
      role: 'Desenvolvedor',
      company: 'Tech Solutions',
      content:
        'A documentação é excelente e o suporte técnico é muito responsivo. Recomendo fortemente para qualquer projeto que precise de dados de câmbio.',
      avatar: 'https://i.pravatar.cc/150?img=2',
    },
    {
      name: 'Mariana Costa',
      role: 'Analista Financeiro',
      company: 'Global Invest',
      content:
        'A precisão dos dados e a facilidade de integração nos permitiu focar no nosso negócio principal sem nos preocuparmos com a infraestrutura.',
      avatar: 'https://i.pravatar.cc/150?img=3',
    },
  ];
}
