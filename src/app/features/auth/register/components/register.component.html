<div class="bg-white rounded-lg shadow-md p-8">
  <div class="text-center mb-6">
    <h1 class="text-2xl font-bold text-gray-900">Criar uma conta</h1>
    <p class="text-gray-600 mt-2">Registre-se para começar a usar nossa API</p>
  </div>

  @if (errorMessage) {
  <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-6 rounded">
    <div class="flex items-center">
      <i-lucide
        [img]="AlertCircleIcon"
        class="text-red-500 w-5 h-5 mr-2"
      ></i-lucide>
      <span class="text-red-700">{{ errorMessage }}</span>
    </div>
  </div>
  }

  <form (ngSubmit)="register()" class="space-y-6">
    <div>
      <label for="name" class="block text-sm font-medium text-gray-700 mb-1"
        >Nome completo</label
      >
      <input
        type="text"
        id="name"
        name="name"
        [(ngModel)]="registerData.name"
        class="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
        placeholder="Seu nome completo"
        required
      />
    </div>

    <div>
      <label for="email" class="block text-sm font-medium text-gray-700 mb-1"
        >Email</label
      >
      <input
        type="email"
        id="email"
        name="email"
        [(ngModel)]="registerData.email"
        class="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
        placeholder="<EMAIL>"
        required
      />
    </div>

    <div>
      <label for="password" class="block text-sm font-medium text-gray-700 mb-1"
        >Senha</label
      >
      <div class="relative">
        <input
          [type]="showPassword ? 'text' : 'password'"
          id="password"
          name="password"
          [(ngModel)]="registerData.password"
          class="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          placeholder="••••••••"
          required
        />
        <button
          type="button"
          (click)="togglePasswordVisibility()"
          class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
        >
          <i-lucide
            [img]="showPassword ? EyeOffIcon : EyeIcon"
            class="w-5 h-5"
          ></i-lucide>
        </button>
      </div>

      @if (registerData.password) {
      <div class="mt-2">
        <div class="flex items-center justify-between mb-1">
          <span class="text-xs text-gray-500">Força da senha:</span>
          <span
            class="text-xs font-medium"
            [ngClass]="{
              'text-red-500': getPasswordStrength().text === 'Fraca',
              'text-yellow-500': getPasswordStrength().text === 'Média',
              'text-blue-500': getPasswordStrength().text === 'Boa',
              'text-green-500': getPasswordStrength().text === 'Forte'
            }"
            >{{ getPasswordStrength().text }}</span
          >
        </div>
        <div class="w-full bg-gray-200 rounded-full h-1.5">
          <div
            [ngClass]="getPasswordStrength().color"
            class="h-1.5 rounded-full"
            [style.width.%]="getPasswordStrength().strength"
          ></div>
        </div>
      </div>
      }
    </div>

    <div>
      <label
        for="confirmPassword"
        class="block text-sm font-medium text-gray-700 mb-1"
        >Confirmar senha</label
      >
      <div class="relative">
        <input
          [type]="showConfirmPassword ? 'text' : 'password'"
          id="confirmPassword"
          name="confirmPassword"
          [(ngModel)]="registerData.confirmPassword"
          class="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          placeholder="••••••••"
          required
        />
        <button
          type="button"
          (click)="toggleConfirmPasswordVisibility()"
          class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
        >
          <i-lucide
            [img]="showConfirmPassword ? EyeOffIcon : EyeIcon"
            class="w-5 h-5"
          ></i-lucide>
        </button>
      </div>

      @if (registerData.password && registerData.confirmPassword) {
      <div
        class="mt-2 flex items-center"
        [ngClass]="
          registerData.password === registerData.confirmPassword
            ? 'text-green-500'
            : 'text-red-500'
        "
      >
        <i-lucide
          [img]="
            registerData.password === registerData.confirmPassword
              ? CheckIcon
              : AlertCircleIcon
          "
          class="w-4 h-4 mr-1"
        ></i-lucide>
        <span class="text-xs">
          {{
            registerData.password === registerData.confirmPassword
              ? "As senhas coincidem"
              : "As senhas não coincidem"
          }}
        </span>
      </div>
      }
    </div>

    <div class="flex items-center">
      <input
        type="checkbox"
        id="accept-terms"
        name="acceptTerms"
        [(ngModel)]="registerData.acceptTerms"
        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        required
      />
      <label for="accept-terms" class="ml-2 block text-sm text-gray-700">
        Eu aceito os
        <a routerLink="/auth/terms" class="text-blue-600 hover:text-blue-800"
          >Termos de Serviço</a
        >
        e a
        <a
          routerLink="/auth/terms"
          [queryParams]="{ tab: 'privacy' }"
          class="text-blue-600 hover:text-blue-800"
          >Política de Privacidade</a
        >
      </label>
    </div>

    <button
      type="submit"
      class="btn-primary w-full justify-center"
      [disabled]="isLoading"
    >
      @if (isLoading) {
      <svg
        class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      Registrando... } @else {
      <i-lucide [img]="UserPlusIcon" class="icon-sm"></i-lucide>
      Criar Conta }
    </button>
  </form>

  <div class="mt-6 text-center">
    <p class="text-sm text-gray-600">
      Já tem uma conta?
      <a
        routerLink="/auth/login"
        class="text-blue-600 hover:text-blue-800 font-medium"
      >
        Entrar
      </a>
    </p>
  </div>
</div>
