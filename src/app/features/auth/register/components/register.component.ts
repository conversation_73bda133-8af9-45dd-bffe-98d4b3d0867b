import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import {
  AlertCircleIcon,
  CheckIcon,
  EyeIcon,
  EyeOffIcon,
  LucideAngularModule,
  UserPlusIcon,
} from 'lucide-angular';
import {
  AuthResponse,
  RegisterData,
} from '../../../../shared/interfaces/auth.interface';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule, LucideAngularModule],
  templateUrl: './register.component.html',
  styleUrl: './register.component.css',
})
export class RegisterComponent {
  readonly UserPlusIcon = UserPlusIcon;
  readonly EyeIcon = EyeIcon;
  readonly EyeOffIcon = EyeOffIcon;
  readonly AlertCircleIcon = AlertCircleIcon;
  readonly CheckIcon = CheckIcon;

  registerData: RegisterData = {
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    acceptTerms: false,
  };

  showPassword: boolean = false;
  showConfirmPassword: boolean = false;
  isLoading: boolean = false;
  errorMessage: string | null = null;

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  toggleConfirmPasswordVisibility() {
    this.showConfirmPassword = !this.showConfirmPassword;
  }

  register() {
    // Validação básica
    if (
      !this.registerData.name ||
      !this.registerData.email ||
      !this.registerData.password ||
      !this.registerData.confirmPassword
    ) {
      this.errorMessage = 'Por favor, preencha todos os campos.';
      return;
    }

    if (this.registerData.password !== this.registerData.confirmPassword) {
      this.errorMessage = 'As senhas não coincidem.';
      return;
    }

    if (!this.registerData.acceptTerms) {
      this.errorMessage = 'Você precisa aceitar os termos de serviço.';
      return;
    }

    this.isLoading = true;
    this.errorMessage = null;

    // Simulação de chamada à API com dados fake
    setTimeout(() => {
      // Simulação de resposta bem-sucedida
      const authResponse: AuthResponse = {
        accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        user: {
          id: 'usr_' + Math.random().toString(36).substring(2, 11),
          name: this.registerData.name,
          email: this.registerData.email,
          emailVerified: false,
          createdAt: new Date(),
        },
      };

      // Aqui seria implementada a lógica para armazenar os tokens e redirecionar o usuário
      console.log('Registro bem-sucedido:', authResponse);

      // Redirecionamento para o dashboard
      window.location.href = '/dashboard';

      this.isLoading = false;
    }, 1500); // Simulação de delay de rede
  }

  // Validação de força da senha
  getPasswordStrength(): { strength: number; text: string; color: string } {
    const password = this.registerData.password;

    if (!password) {
      return { strength: 0, text: '', color: 'bg-gray-200' };
    }

    let strength = 0;

    // Comprimento mínimo
    if (password.length >= 8) strength += 1;

    // Letras maiúsculas e minúsculas
    if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength += 1;

    // Números
    if (/[0-9]/.test(password)) strength += 1;

    // Caracteres especiais
    if (/[^a-zA-Z0-9]/.test(password)) strength += 1;

    // Retorna informações com base na força
    switch (strength) {
      case 0:
      case 1:
        return { strength: 25, text: 'Fraca', color: 'bg-red-500' };
      case 2:
        return { strength: 50, text: 'Média', color: 'bg-yellow-500' };
      case 3:
        return { strength: 75, text: 'Boa', color: 'bg-blue-500' };
      case 4:
        return { strength: 100, text: 'Forte', color: 'bg-green-500' };
      default:
        return { strength: 0, text: '', color: 'bg-gray-200' };
    }
  }
}
