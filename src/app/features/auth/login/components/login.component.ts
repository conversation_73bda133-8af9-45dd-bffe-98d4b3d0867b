import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import {
  AlertCircleIcon,
  EyeIcon,
  EyeOffIcon,
  LogInIcon,
  LucideAngularModule,
} from 'lucide-angular';
import {
  AuthResponse,
  LoginCredentials,
} from '../../../../shared/interfaces/auth.interface';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule, LucideAngularModule],
  templateUrl: './login.component.html',
  styleUrl: './login.component.css',
})
export class LoginComponent {
  readonly LogInIcon = LogInIcon;
  readonly EyeIcon = EyeIcon;
  readonly EyeOffIcon = EyeOffIcon;
  readonly AlertCircleIcon = AlertCircleIcon;

  credentials: LoginCredentials = {
    email: '',
    password: '',
    rememberMe: false,
  };

  showPassword: boolean = false;
  isLoading: boolean = false;
  errorMessage: string | null = null;

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  login() {
    // Validação básica
    if (!this.credentials.email || !this.credentials.password) {
      this.errorMessage = 'Por favor, preencha todos os campos.';
      return;
    }

    this.isLoading = true;
    this.errorMessage = null;

    // Simulação de chamada à API com dados fake
    setTimeout(() => {
      // Simulação de credenciais válidas para teste
      if (
        this.credentials.email === '<EMAIL>' &&
        this.credentials.password === 'senha123'
      ) {
        // Simulação de resposta bem-sucedida
        const authResponse: AuthResponse = {
          accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
          refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
          user: {
            id: 'usr_123456',
            name: 'Usuário Teste',
            email: '<EMAIL>',
            emailVerified: true,
            createdAt: new Date('2023-01-01'),
          },
        };

        // Aqui seria implementada a lógica para armazenar os tokens e redirecionar o usuário
        console.log('Login bem-sucedido:', authResponse);

        // Redirecionamento para o dashboard
        window.location.href = '/dashboard';
      } else {
        // Simulação de erro de autenticação
        this.errorMessage = 'Email ou senha inválidos. Tente novamente.';
      }

      this.isLoading = false;
    }, 1000); // Simulação de delay de rede
  }
}
