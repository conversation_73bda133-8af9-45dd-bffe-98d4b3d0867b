<div class="bg-white rounded-lg shadow-md p-8">
  <div class="text-center mb-6">
    <h1 class="text-2xl font-bold text-gray-900">Entrar na sua conta</h1>
    <p class="text-gray-600 mt-2">Acesse sua conta para gerenciar sua API</p>
  </div>

  @if (errorMessage) {
  <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-6 rounded">
    <div class="flex items-center">
      <i-lucide
        [img]="AlertCircleIcon"
        class="text-red-500 w-5 h-5 mr-2"
      ></i-lucide>
      <span class="text-red-700">{{ errorMessage }}</span>
    </div>
  </div>
  }

  <form (ngSubmit)="login()" class="space-y-6">
    <div>
      <label for="email" class="block text-sm font-medium text-gray-700 mb-1"
        >Email</label
      >
      <input
        type="email"
        id="email"
        name="email"
        [(ngModel)]="credentials.email"
        class="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
        placeholder="<EMAIL>"
        required
      />
    </div>

    <div>
      <label for="password" class="block text-sm font-medium text-gray-700 mb-1"
        >Senha</label
      >
      <div class="relative">
        <input
          [type]="showPassword ? 'text' : 'password'"
          id="password"
          name="password"
          [(ngModel)]="credentials.password"
          class="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          placeholder="••••••••"
          required
        />
        <button
          type="button"
          (click)="togglePasswordVisibility()"
          class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
        >
          <i-lucide
            [img]="showPassword ? EyeOffIcon : EyeIcon"
            class="w-5 h-5"
          ></i-lucide>
        </button>
      </div>
    </div>

    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <input
          type="checkbox"
          id="remember-me"
          name="remember-me"
          [(ngModel)]="credentials.rememberMe"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label for="remember-me" class="ml-2 block text-sm text-gray-700">
          Lembrar de mim
        </label>
      </div>
      <a
        routerLink="/auth/forgot-password"
        class="text-sm text-blue-600 hover:text-blue-800"
      >
        Esqueceu a senha?
      </a>
    </div>

    <button
      type="submit"
      class="btn-primary w-full justify-center"
      [disabled]="isLoading"
    >
      @if (isLoading) {
      <svg
        class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      Entrando... } @else {
      <i-lucide [img]="LogInIcon" class="icon-sm"></i-lucide>
      Entrar }
    </button>
  </form>

  <div class="mt-6 text-center">
    <p class="text-sm text-gray-600">
      Não tem uma conta?
      <a
        routerLink="/auth/register"
        class="text-blue-600 hover:text-blue-800 font-medium"
      >
        Registre-se
      </a>
    </p>
  </div>

  <div class="mt-8 pt-6 border-t border-gray-200">
    <p class="text-xs text-center text-gray-500">
      Ao entrar, você concorda com nossos
      <a routerLink="/auth/terms" class="text-blue-600 hover:text-blue-800"
        >Termos de Serviço</a
      >
      e
      <a
        routerLink="/auth/terms"
        [queryParams]="{ tab: 'privacy' }"
        class="text-blue-600 hover:text-blue-800"
        >Política de Privacidade</a
      >.
    </p>
  </div>
</div>
