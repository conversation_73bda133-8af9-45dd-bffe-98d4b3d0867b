<div class="bg-white rounded-lg shadow-md p-8">
  <div class="text-center mb-6">
    <h1 class="text-2xl font-bold text-gray-900">Recuperar <PERSON></h1>
    <p class="text-gray-600 mt-2">
      Enviaremos um link para redefinir sua senha
    </p>
  </div>

  @if (!isSubmitted) { @if (errorMessage) {
  <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-6 rounded">
    <div class="flex items-center">
      <i-lucide
        [img]="AlertCircleIcon"
        class="text-red-500 w-5 h-5 mr-2"
      ></i-lucide>
      <span class="text-red-700">{{ errorMessage }}</span>
    </div>
  </div>
  }

  <form (ngSubmit)="resetPassword()" class="space-y-6">
    <div>
      <label for="email" class="block text-sm font-medium text-gray-700 mb-1"
        >Email</label
      >
      <input
        type="email"
        id="email"
        name="email"
        [(ngModel)]="forgotPasswordData.email"
        class="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
        placeholder="<EMAIL>"
        required
      />
      <p class="mt-2 text-sm text-gray-500">
        Informe o email associado à sua conta.
      </p>
    </div>

    <button
      type="submit"
      class="btn-primary w-full justify-center"
      [disabled]="isLoading"
    >
      @if (isLoading) {
      <svg
        class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      Enviando... } @else {
      <i-lucide [img]="KeyIcon" class="icon-sm"></i-lucide>
      Enviar Link de Recuperação }
    </button>
  </form>
  } @else {
  <div class="bg-green-50 border-l-4 border-green-500 p-4 mb-6 rounded">
    <div class="flex items-start">
      <i-lucide
        [img]="CheckCircleIcon"
        class="text-green-500 w-5 h-5 mr-2 mt-0.5"
      ></i-lucide>
      <div>
        <h3 class="text-green-800 font-medium">Email enviado com sucesso!</h3>
        <p class="text-green-700 mt-1">
          Enviamos um email para
          <strong>{{ forgotPasswordData.email }}</strong> com instruções para
          redefinir sua senha. Por favor, verifique sua caixa de entrada e siga
          as instruções no email.
        </p>
      </div>
    </div>
  </div>

  <p class="text-sm text-gray-600 mb-6">
    Se você não receber o email em alguns minutos, verifique sua pasta de spam
    ou tente novamente.
  </p>
  }

  <div class="mt-6">
    <a
      routerLink="/auth/login"
      class="flex items-center text-blue-600 hover:text-blue-800 font-medium"
    >
      <i-lucide [img]="ArrowLeftIcon" class="icon-sm mr-1"></i-lucide>
      Voltar para o login
    </a>
  </div>
</div>
