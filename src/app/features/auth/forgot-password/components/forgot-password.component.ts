import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import {
  AlertCircleIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
  KeyIcon,
  LucideAngularModule,
} from 'lucide-angular';
import { ForgotPasswordData } from '../../../../shared/interfaces/auth.interface';

@Component({
  selector: 'app-forgot-password',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule, LucideAngularModule],
  templateUrl: './forgot-password.component.html',
  styleUrl: './forgot-password.component.css',
})
export class ForgotPasswordComponent {
  readonly KeyIcon = KeyIcon;
  readonly AlertCircleIcon = AlertCircleIcon;
  readonly CheckCircleIcon = CheckCircleIcon;
  readonly ArrowLeftIcon = ArrowLeftIcon;

  forgotPasswordData: ForgotPasswordData = {
    email: '',
  };

  isLoading: boolean = false;
  errorMessage: string | null = null;
  isSubmitted: boolean = false;

  resetPassword() {
    // Validação básica
    if (!this.forgotPasswordData.email) {
      this.errorMessage = 'Por favor, informe seu email.';
      return;
    }

    this.isLoading = true;
    this.errorMessage = null;

    // Simulação de chamada à API com dados fake
    setTimeout(() => {
      // Simulação de resposta bem-sucedida
      console.log(
        'Email de recuperação enviado para:',
        this.forgotPasswordData.email
      );

      // Marca como enviado para mostrar a mensagem de sucesso
      this.isSubmitted = true;
      this.isLoading = false;
    }, 1500); // Simulação de delay de rede
  }
}
