import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-verify-email',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './verify-email.component.html',
})
export class VerifyEmailComponent {
  isResending = false;
  resendSuccess = false;
  resendError = false;

  constructor(private router: Router) {}

  resendVerificationEmail(): void {
    this.isResending = true;
    this.resendSuccess = false;
    this.resendError = false;

    // Simulando reenvio do email
    setTimeout(() => {
      this.isResending = false;
      this.resendSuccess = true;
    }, 1500);
  }

  goToLogin(): void {
    this.router.navigate(['/auth/login']);
  }
}
