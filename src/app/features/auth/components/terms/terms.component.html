<div class="container-padded py-8 md:py-12 lg:py-16 bg-gray-50 min-h-screen">
  <!-- Cabe<PERSON>lho -->
  <div class="text-center mb-10 md:mb-12 animate-fade-in">
    <h1 class="page-title">{{ "terms.pageTitle" | translate }}</h1>
    <p class="text-muted mt-2">
      {{ "terms.lastUpdated" | translate }}:
      {{ lastUpdatedDate | date : "longDate" }}
    </p>
  </div>

  <!-- Abas de Navegação -->
  <div class="mb-8 md:mb-10 flex justify-center animate-slide-up">
    <div
      class="inline-flex rounded-lg shadow-sm bg-white border border-gray-200"
      role="group"
    >
      <button
        type="button"
        (click)="setActiveTab('terms')"
        class="tab-button-base"
        [class.tab-button-active]="activeTab === 'terms'"
      >
        <i-lucide [name]="FileTextIcon" class="icon-sm mr-2"></i-lucide>
        {{ "terms.tabs.termsOfService" | translate }}
      </button>
      <button
        type="button"
        (click)="setActiveTab('privacy')"
        class="tab-button-base"
        [class.tab-button-active]="activeTab === 'privacy'"
      >
        <i-lucide [name]="ShieldIcon" class="icon-sm mr-2"></i-lucide>
        {{ "terms.tabs.privacyPolicy" | translate }}
      </button>
    </div>
  </div>

  <!-- Conteúdo das Abas -->
  <div
    class="bg-white rounded-xl shadow-xl border border-gray-100 p-6 md:p-10 animate-fade-in"
  >
    <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">
      {{ currentCategory.title | translate }}
    </h2>

    <div class="space-y-10">
      @for (section of currentCategory.sections; track section.title; let i =
      $index) {
      <div
        class="term-card animate-fade-in"
        [style.animation-delay]="i * 0.1 + 's'"
      >
        <h3
          class="text-xl md:text-2xl font-semibold text-gray-700 mb-4 pb-2 border-b border-gray-200 flex items-center"
        >
          <i-lucide
            [name]="section.icon"
            class="icon-md mr-3 text-blue-600"
          ></i-lucide>
          {{ section.title | translate }}
        </h3>
        <div class="prose prose-blue max-w-none text-gray-600 space-y-3">
          @for (paragraph of section.content; track paragraph) {
          <p [innerHTML]="paragraph | translate | marked"></p>
          }
        </div>
      </div>
      }
    </div>

    <!-- Aviso de aceitação -->
    @if (activeTab === 'terms') {
    <div
      class="mt-10 p-6 rounded-lg bg-blue-50 border border-blue-200 text-blue-700 flex items-start animate-fade-in"
      [style.animation-delay]="'0.' + currentCategory.sections.length + 's'"
    >
      <i-lucide
        [name]="InfoIcon"
        class="icon-md mr-3 flex-shrink-0 mt-1"
      ></i-lucide>
      <p class="text-sm">{{ "terms.acceptanceNotice" | translate }}</p>
    </div>
    }
  </div>

  <!-- Seção de contato -->
  <div class="mt-12 text-center animate-fade-in">
    <p class="text-gray-600 mb-6 text-lg">
      {{ "terms.contact.question" | translate }}
    </p>
    <a routerLink="/support" class="btn-primary px-8 py-3 text-lg">
      <i-lucide [name]="MessageCircleIcon" class="icon-sm mr-2"></i-lucide>
      {{ "terms.contact.button" | translate }}
    </a>
  </div>
</div>
