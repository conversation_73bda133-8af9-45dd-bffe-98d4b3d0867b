import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import {
  AlertCircleIcon,
  BookTextIcon,
  ClipboardListIcon,
  DatabaseIcon,
  FileSignatureIcon,
  FileTextIcon,
  InfoIcon,
  LifeBuoyIcon,
  LockIcon,
  LucideAngularModule,
  MessageCircleIcon,
  ScaleIcon,
  ServerIcon,
  ShieldCheckIcon,
  ShieldIcon,
  UsersIcon,
} from 'lucide-angular';
import { TranslationService } from '../../../../core/services/translation.service';
import { MarkedPipe } from '../../../../shared/pipes/marked.pipe';

interface TermSection {
  title: string;
  content: string[];
  isPrivacyPolicy?: boolean;
  icon: any;
}

interface TermCategory {
  id: string;
  title: string;
  sections: TermSection[];
  icon: any;
}

@Component({
  selector: 'app-terms',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    LucideAngularModule,
    TranslateModule,
    MarkedPipe,
  ],
  templateUrl: './terms.component.html',
  styleUrls: ['./terms.component.css'],
})
export class TermsComponent implements OnInit {
  readonly InfoIcon = InfoIcon;
  readonly ShieldIcon = ShieldIcon;
  readonly FileTextIcon = FileTextIcon;
  readonly AlertCircleIcon = AlertCircleIcon;
  readonly MessageCircleIcon = MessageCircleIcon;
  readonly ShieldCheckIcon = ShieldCheckIcon;
  readonly BookTextIcon = BookTextIcon;
  readonly LifeBuoyIcon = LifeBuoyIcon;

  lastUpdatedDate: Date = new Date('2024-03-01T00:00:00Z');
  activeTab: 'terms' | 'privacy' = 'terms';

  constructor(
    private route: ActivatedRoute,
    public translationService: TranslationService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      if (params['tab'] === 'privacy') {
        this.activeTab = 'privacy';
      }
    });
  }

  categories: TermCategory[] = [
    {
      id: 'terms',
      title: 'terms.tabs.termsOfService',
      icon: BookTextIcon,
      sections: [
        {
          title: 'terms.termsOfService.acceptance.title',
          icon: FileSignatureIcon,
          content: [
            'terms.termsOfService.acceptance.paragraph1',
            'terms.termsOfService.acceptance.paragraph2',
          ],
        },
        {
          title: 'terms.termsOfService.apiUsage.title',
          icon: ServerIcon,
          content: [
            'terms.termsOfService.apiUsage.paragraph1',
            'terms.termsOfService.apiUsage.paragraph2',
            'terms.termsOfService.apiUsage.paragraph3',
          ],
        },
        {
          title: 'terms.termsOfService.usageLimitations.title',
          icon: ScaleIcon,
          content: [
            'terms.termsOfService.usageLimitations.paragraph1',
            'terms.termsOfService.usageLimitations.listTitle',
            '• terms.termsOfService.usageLimitations.listItem1',
            '• terms.termsOfService.usageLimitations.listItem2',
            '• terms.termsOfService.usageLimitations.listItem3',
            '• terms.termsOfService.usageLimitations.listItem4',
          ],
        },
        {
          title: 'terms.termsOfService.apiKeys.title',
          icon: LockIcon,
          content: [
            'terms.termsOfService.apiKeys.paragraph1',
            'terms.termsOfService.apiKeys.paragraph2',
          ],
        },
        {
          title: 'terms.termsOfService.intellectualProperty.title',
          icon: DatabaseIcon,
          content: [
            'terms.termsOfService.intellectualProperty.paragraph1',
            'terms.termsOfService.intellectualProperty.paragraph2',
          ],
        },
        {
          title: 'terms.termsOfService.liabilityLimitation.title',
          icon: ClipboardListIcon,
          content: [
            'terms.termsOfService.liabilityLimitation.paragraph1',
            'terms.termsOfService.liabilityLimitation.paragraph2',
          ],
        },
        {
          title: 'terms.termsOfService.indemnification.title',
          icon: UsersIcon,
          content: ['terms.termsOfService.indemnification.paragraph1'],
        },
        {
          title: 'terms.termsOfService.modifications.title',
          icon: DatabaseIcon,
          content: [
            'terms.termsOfService.modifications.paragraph1',
            'terms.termsOfService.modifications.paragraph2',
          ],
        },
        {
          title: 'terms.termsOfService.termination.title',
          icon: LockIcon,
          content: ['terms.termsOfService.termination.paragraph1'],
        },
        {
          title: 'terms.termsOfService.applicableLaw.title',
          icon: BookTextIcon,
          content: [
            'terms.termsOfService.applicableLaw.paragraph1',
            'terms.termsOfService.applicableLaw.paragraph2',
          ],
        },
      ],
    },
    {
      id: 'privacy',
      title: 'terms.tabs.privacyPolicy',
      icon: ShieldCheckIcon,
      sections: [
        {
          title: 'terms.privacyPolicy.informationCollected.title',
          icon: UsersIcon,
          content: [
            'terms.privacyPolicy.informationCollected.paragraph1',
            '• terms.privacyPolicy.informationCollected.listItem1',
            '• terms.privacyPolicy.informationCollected.listItem2',
            '• terms.privacyPolicy.informationCollected.listItem3',
            '• terms.privacyPolicy.informationCollected.listItem4',
          ],
          isPrivacyPolicy: true,
        },
        {
          title: 'terms.privacyPolicy.howWeUseInfo.title',
          icon: DatabaseIcon,
          content: [
            'terms.privacyPolicy.howWeUseInfo.paragraph1',
            '• terms.privacyPolicy.howWeUseInfo.listItem1',
            '• terms.privacyPolicy.howWeUseInfo.listItem2',
            '• terms.privacyPolicy.howWeUseInfo.listItem3',
            '• terms.privacyPolicy.howWeUseInfo.listItem4',
            '• terms.privacyPolicy.howWeUseInfo.listItem5',
          ],
          isPrivacyPolicy: true,
        },
        {
          title: 'terms.privacyPolicy.sharingInfo.title',
          icon: ClipboardListIcon,
          content: ['terms.privacyPolicy.sharingInfo.paragraph1'],
          isPrivacyPolicy: true,
        },
        {
          title: 'terms.privacyPolicy.dataSecurity.title',
          icon: LockIcon,
          content: [
            'terms.privacyPolicy.dataSecurity.paragraph1',
            'terms.privacyPolicy.dataSecurity.paragraph2',
          ],
          isPrivacyPolicy: true,
        },
        {
          title: 'terms.privacyPolicy.yourRights.title',
          icon: UsersIcon,
          content: [
            'terms.privacyPolicy.yourRights.paragraph1',
            '• terms.privacyPolicy.yourRights.listItem1',
            '• terms.privacyPolicy.yourRights.listItem2',
            '• terms.privacyPolicy.yourRights.listItem3',
            '• terms.privacyPolicy.yourRights.listItem4',
          ],
          isPrivacyPolicy: true,
        },
        {
          title: 'terms.privacyPolicy.dataRetention.title',
          icon: DatabaseIcon,
          content: [
            'terms.privacyPolicy.dataRetention.paragraph1',
            'terms.privacyPolicy.dataRetention.paragraph2',
          ],
          isPrivacyPolicy: true,
        },
        {
          title: 'terms.privacyPolicy.cookiesAndSimilarTechnologies.title',
          icon: BookTextIcon,
          content: [
            'terms.privacyPolicy.cookiesAndSimilarTechnologies.paragraph1',
            'terms.privacyPolicy.cookiesAndSimilarTechnologies.paragraph2',
          ],
          isPrivacyPolicy: true,
        },
        {
          title: 'terms.privacyPolicy.policyModifications.title',
          icon: BookTextIcon,
          content: [
            'terms.privacyPolicy.policyModifications.paragraph1',
            'terms.privacyPolicy.policyModifications.paragraph2',
          ],
          isPrivacyPolicy: true,
        },
        {
          title: 'terms.privacyPolicy.contact.title',
          icon: LifeBuoyIcon,
          content: [
            'terms.privacyPolicy.contact.paragraph1',
            'terms.privacyPolicy.contact.paragraph2',
          ],
          isPrivacyPolicy: true,
        },
      ],
    },
  ];

  get currentCategory(): TermCategory {
    return (
      this.categories.find((category) => category.id === this.activeTab) ||
      this.categories[0]
    );
  }

  setActiveTab(tab: 'terms' | 'privacy'): void {
    this.activeTab = tab;
  }
}
