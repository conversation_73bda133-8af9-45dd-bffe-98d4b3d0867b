{"header": {"home": "Início", "dashboard": "Dashboard", "apiKeys": "<PERSON>ves de <PERSON>", "subscriptions": "Assinaturas", "documentation": "Documentação", "playground": "Playground", "login": "Entrar", "register": "Registrar", "language": "Idioma", "switchLanguage": "<PERSON><PERSON> para <PERSON>"}, "common": {"loading": "Carregando...", "error": "Ocorreu um erro", "success": "Operação realizada com sucesso", "save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "delete": "Excluir", "edit": "<PERSON><PERSON>", "view": "Visualizar", "search": "Buscar", "filter": "Filtrar", "noResults": "Nenhum resultado encontrado", "required": "Campo obrigatório", "invalidEmail": "<PERSON><PERSON>", "passwordMismatch": "As senhas não coincidem", "passwordTooShort": "A senha deve ter pelo menos 8 caracteres"}, "dashboard": {"title": "Dashboard", "loading": "Carregando...", "lastUpdated": "Última atualização", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "historicalRates": "Histórico de Taxas", "historicalChart": "Gráfico de histórico de taxas será exibido aqui", "recentConversions": "Conversões Recentes", "noRecentConversions": "Nenhuma conversão recente", "apiUsage": "<PERSON><PERSON> da <PERSON>", "requestsThisMonth": "Requisições este mês", "currentPlan": "Plano atual", "nextRenewal": "Próxima renovação", "status": "Status", "active": "Ativo"}, "home": {"hero": {"title": "API de Conversão de Moedas Confiável e Rápida", "subtitle": "Acesse taxas de câmbio em tempo real e histórico para mais de 170 moedas com nossa API simples e poderosa.", "startButton": "<PERSON><PERSON><PERSON>", "tryButton": "Testar API"}, "stats": {"developers": "<PERSON><PERSON><PERSON><PERSON>", "requests": "Requisições/mês", "uptime": "Disponibilidade", "countries": "Países"}, "features": {"title": "Por Que Escolher Nossa API?", "subtitle": "Construída para desenvolvedores que precisam de dados de moeda confiáveis, rápidos e precisos.", "fast": {"title": "Super Rápida", "description": "Obtenha taxas de câmbio em tempo real com tempos de resposta abaixo de 100ms. Nossa CDN global garante baixa latência mundial."}, "secure": {"title": "Segurança Bancária", "description": "Segurança de nível empresarial com criptografia SSL, autenticação por chave de API e proteção por limitação de taxa."}, "easy": {"title": "Amigável ao Desenvolvedor", "description": "API REST simples com documentação abrangente, SDKs e exemplos de código em várias linguagens."}}, "demo": {"title": "Veja em Ação", "subtitle": "Experimente nossa API com este exemplo ao vivo e veja como é fácil integrar.", "requestTitle": "Requisição da API", "responseTitle": "Resposta", "tryButton": "Testar no Playground"}, "pricing": {"title": "Preços Simples e Transparentes", "subtitle": "Escolha o plano que atende às suas necessidades. Sem taxas ocultas, sem surpresas.", "basic": {"title": "Básico", "price": "<PERSON><PERSON><PERSON><PERSON>", "period": "Para sempre", "feature1": "1.000 requisições/mês", "feature2": "Taxas em tempo real", "feature3": "Suporte por email", "button": "<PERSON><PERSON><PERSON>"}, "professional": {"title": "Profissional", "price": "R$ 149", "period": "por mês", "badge": "Mais <PERSON>", "feature1": "100.000 requisições/mês", "feature2": "<PERSON><PERSON> his<PERSON>ó<PERSON>", "feature3": "Suporte prioritário", "feature4": "<PERSON><PERSON><PERSON><PERSON>", "button": "Escolher Profissional"}, "enterprise": {"title": "Empresarial", "price": "Personalizado", "period": "Entre em contato", "feature1": "Requisições ilimitadas", "feature2": "Integrações personalizadas", "feature3": "Suporte dedicado", "feature4": "Garantia de SLA", "feature5": "Implantação local", "button": "<PERSON><PERSON><PERSON>"}}, "cta": {"title": "Pronto para Começar?", "subtitle": "Junte-se a milhares de desenvolvedores que confiam em nossa API para suas necessidades de conversão de moeda.", "primaryButton": "Iniciar <PERSON>", "secondaryButton": "Ver Documentação"}}, "terms": {"pageTitle": "Termos de Serviço e Política de Privacidade", "lastUpdated": "Última atualização", "acceptanceNotice": "Ao continuar usando nossa API, você concorda com estes termos. Se você não concordar com qualquer parte destes termos, por favor, não use nossa API.", "tabs": {"termsOfService": "Termos de Serviço", "privacyPolicy": "Política de Privacidade"}, "contact": {"question": "Tem dúvidas sobre nossos termos ou política de privacidade?", "button": "Entrar em Contato com o Suporte"}, "termsOfService": {"acceptance": {"title": "1. Aceitação dos Termos", "paragraph1": "Ao acessar e usar a API CurrencyWise, você concorda em cumprir estes termos de serviço, todas as leis e regulamentos aplicáveis, e reconhece que é responsável pelo cumprimento de quaisquer leis locais aplicáveis.", "paragraph2": "Se você não concordar com qualquer parte destes termos, você não está autorizado a acessar ou usar nossa API."}, "apiUsage": {"title": "2. <PERSON><PERSON>", "paragraph1": "A API CurrencyWise é fornecida para uso em conformidade com estes termos. Você concorda em não usar a API para qualquer finalidade ilegal ou não autorizada.", "paragraph2": "Você não deve tentar obter acesso não autorizado à nossa API, outros sistemas ou redes conectadas à nossa API.", "paragraph3": "Você é responsável por manter a confidencialidade de suas credenciais de API e por todas as atividades que ocorrem sob sua conta."}, "usageLimitations": {"title": "3. Limitações de Uso", "paragraph1": "O uso da API está sujeito a limites de taxa e outras restrições conforme detalhado em sua assinatura. O uso excessivo ou abusivo pode resultar em suspensão ou término do acesso.", "listTitle": "Você concorda em não:", "listItem1": "Usar a API de maneira que possa danificar, desativar, sobrecarregar ou prejudicar nossos servidores ou redes.", "listItem2": "Usar a API para distribuir malware, spyware ou qualquer outro código malicioso.", "listItem3": "Revender, sublicenciar ou redistribuir a API sem nossa autorização expressa por escrito.", "listItem4": "Usar a API para criar um produto ou serviço concorrente."}, "apiKeys": {"title": "4. <PERSON><PERSON>", "paragraph1": "Suas chaves de API são confidenciais e não devem ser compartilhadas com terceiros. Cada chave é única e vinculada à sua conta.", "paragraph2": "Você é responsável por manter suas chaves seguras e notificar-nos imediatamente se suspeitar de uso não autorizado. Recomendamos rotacionar suas chaves regularmente."}, "intellectualProperty": {"title": "5. <PERSON><PERSON><PERSON>ade Intelectual", "paragraph1": "Todos os direitos de propriedade intelectual da API e dos dados fornecidos pertencem à CurrencyWise.", "paragraph2": "Você não pode copiar, modificar, distribuir ou criar trabalhos derivados baseados em nossa API sem autorização."}, "liabilityLimitation": {"title": "6. Limitação de Responsabilidade", "paragraph1": "A CurrencyWise não será responsável por quaisquer danos diretos, indiretos, incidentais ou consequenciais.", "paragraph2": "Nossa responsabilidade total não excederá o valor pago por você nos últimos 12 meses."}, "indemnification": {"title": "7. Indeniza<PERSON>", "paragraph1": "Você concorda em indenizar e isentar a CurrencyWise de qualquer reclamação decorrente do seu uso da API."}, "modifications": {"title": "8. Modificações", "paragraph1": "Reservamo-nos o direito de modificar estes termos a qualquer momento.", "paragraph2": "As modificações entrarão em vigor imediatamente após a publicação."}, "termination": {"title": "9. <PERSON><PERSON><PERSON><PERSON>", "paragraph1": "Podemos rescindir ou suspender seu acesso à API a qualquer momento, com ou sem aviso prévio."}, "applicableLaw": {"title": "10. <PERSON><PERSON>", "paragraph1": "Estes termos são regidos pelas leis do Brasil.", "paragraph2": "Qualquer disputa será resolvida nos tribunais competentes de São Paulo, SP."}}, "privacyPolicy": {"informationCollected": {"title": "1. Informações que Coletamos", "paragraph1": "Coletamos as seguintes informações quando você se registra e usa nossa API:", "listItem1": "Informações de registro: nome, endereço de e-mail, informações de contato.", "listItem2": "Informações de uso: padrões de uso da API, volume de solicitações, endpoints acessados.", "listItem3": "Informações de pagamento: detalhes de faturamento e pagamento.", "listItem4": "Informações técnicas: endereço IP, tipo de navegador, sistema operacional."}, "howWeUseInfo": {"title": "2. Como Usamos Suas Informações", "paragraph1": "Usamos suas informações para:", "listItem1": "Fornecer e manter nossos serviços.", "listItem2": "Processar transações e enviar faturas.", "listItem3": "Monitorar e analisar tendências de uso.", "listItem4": "Comunicar-se com você sobre atualizações, suporte e outras informações relacionadas ao serviço.", "listItem5": "Detectar, prevenir e resolver problemas técnicos e de segurança."}, "sharingInfo": {"title": "3. Compartilhamento de Informações", "paragraph1": "Não compartilhamos suas informações pessoais com terceiros, exceto conforme descrito nesta política."}, "dataSecurity": {"title": "4. <PERSON><PERSON><PERSON><PERSON> de Dados", "paragraph1": "Implementamos medidas de segurança técnicas e organizacionais para proteger suas informações.", "paragraph2": "No entanto, nenhum método de transmissão pela internet é 100% seguro."}, "yourRights": {"title": "5. <PERSON><PERSON>", "paragraph1": "Dependendo da sua localização, você pode ter certos direitos em relação às suas informações pessoais, incluindo:", "listItem1": "Direito de acesso às suas informações.", "listItem2": "Direito de corrigir informações imprecisas.", "listItem3": "Direito de excluir suas informações.", "listItem4": "<PERSON><PERSON><PERSON> de restringir ou opor-se ao processamento.", "listItem5": "Direito à portabilidade de dados. Para exercer esses direitos, entre em contato conosco através do e-mail <EMAIL>."}, "dataRetention": {"title": "6. Retenção de Dados", "paragraph1": "Mantemos suas informações pelo tempo necessário para fornecer nossos serviços.", "paragraph2": "Dados de uso da API são mantidos por até 2 anos para fins de análise e melhoria do serviço."}, "cookiesAndSimilarTechnologies": {"title": "7. <PERSON><PERSON> e Tecnologias Semelhantes", "paragraph1": "Usamos cookies e tecnologias similares para melhorar sua experiência.", "paragraph2": "Você pode controlar o uso de cookies através das configurações do seu navegador."}, "policyModifications": {"title": "8. Alterações a Esta Política", "paragraph1": "Podemos atualizar esta política periodicamente.", "paragraph2": "Notificaremos você sobre mudanças significativas por e-mail ou através de nossa plataforma."}, "contact": {"title": "9. <PERSON><PERSON><PERSON>", "paragraph1": "Se você tiver dúvidas sobre esta Política de Privacidade, entre em contato conosco em:", "paragraph2": "E-mail: <EMAIL><br>Endereço: Av. <PERSON>, 1000, São Paulo, SP, Brasil"}}}}