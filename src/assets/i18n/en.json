{"header": {"home": "Home", "dashboard": "Dashboard", "apiKeys": "API Keys", "subscriptions": "Subscriptions", "documentation": "Documentation", "playground": "Playground", "login": "<PERSON><PERSON>", "register": "Register", "language": "Language", "switchLanguage": "Mudar para Português"}, "common": {"loading": "Loading...", "error": "An error occurred", "success": "Operation completed successfully", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "view": "View", "search": "Search", "filter": "Filter", "noResults": "No results found", "required": "Required field", "invalidEmail": "Invalid email", "passwordMismatch": "Passwords do not match", "passwordTooShort": "Password must be at least 8 characters"}, "dashboard": {"title": "Dashboard", "loading": "Loading...", "lastUpdated": "Last updated", "refresh": "Refresh", "historicalRates": "Historical Rates", "historicalChart": "Historical rates chart will be displayed here", "recentConversions": "Recent Conversions", "noRecentConversions": "No recent conversions", "apiUsage": "API Usage", "requestsThisMonth": "Requests this month", "currentPlan": "Current plan", "nextRenewal": "Next renewal", "status": "Status", "active": "Active"}, "home": {"hero": {"title": "Reliable and Fast Currency Conversion API", "subtitle": "Access real-time and historical exchange rates for over 170 currencies with our simple and powerful API.", "startButton": "Get Started", "tryButton": "Try API"}, "stats": {"developers": "Developers", "requests": "Requests/month", "uptime": "Uptime", "countries": "Countries"}}, "terms": {"pageTitle": "Terms of Service and Privacy Policy", "lastUpdated": "Last updated", "acceptanceNotice": "By continuing to use our API, you agree to these terms. If you do not agree with any part of these terms, please do not use our API.", "tabs": {"termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy"}, "contact": {"question": "Have questions about our terms or privacy policy?", "button": "Contact Support"}, "termsOfService": {"acceptance": {"title": "1. Acceptance of Terms", "paragraph1": "By accessing and using the CurrencyWise API, you agree to comply with these terms of service, all applicable laws and regulations, and acknowledge that you are responsible for compliance with any applicable local laws.", "paragraph2": "If you do not agree with any part of these terms, you are not authorized to access or use our API."}, "apiUsage": {"title": "2. API Usage", "paragraph1": "The CurrencyWise API is provided for use in accordance with these terms. You agree not to use the API for any illegal or unauthorized purpose.", "paragraph2": "You must not attempt to gain unauthorized access to our API, other systems, or networks connected to our API.", "paragraph3": "You are responsible for maintaining the confidentiality of your API credentials and for all activities that occur under your account."}, "usageLimitations": {"title": "3. Usage Limitations", "paragraph1": "API usage is subject to rate limits and other restrictions as detailed in your subscription. Excessive or abusive use may result in suspension or termination of access.", "listTitle": "You agree not to:", "listItem1": "Use the API in a way that could damage, disable, overburden, or impair our servers or networks.", "listItem2": "Use the API to distribute malware, spyware, or any other malicious code.", "listItem3": "Resell, sublicense, or redistribute the API without our express written authorization.", "listItem4": "Use the API to create a competing product or service."}, "apiKeys": {"title": "4. API Keys", "paragraph1": "Your API keys are confidential and should not be shared with third parties. Each key is unique and linked to your account.", "paragraph2": "You are responsible for keeping your keys secure and notifying us immediately if you suspect unauthorized use. We recommend rotating your keys regularly."}, "intellectualProperty": {"title": "5. Intellectual Property", "paragraph1": "All intellectual property rights in the API and data provided belong to CurrencyWise.", "paragraph2": "You may not copy, modify, distribute, or create derivative works based on our API without authorization."}, "liabilityLimitation": {"title": "6. Limitation of Liability", "paragraph1": "CurrencyWise shall not be liable for any direct, indirect, incidental, or consequential damages.", "paragraph2": "Our total liability shall not exceed the amount paid by you in the last 12 months."}, "indemnification": {"title": "7. Indemnification", "paragraph1": "You agree to indemnify and hold CurrencyWise harmless from any claims arising from your use of the API."}, "modifications": {"title": "8. Modifications", "paragraph1": "We reserve the right to modify these terms at any time.", "paragraph2": "Modifications will take effect immediately upon posting."}, "termination": {"title": "9. Termination", "paragraph1": "We may terminate or suspend your access to the API at any time, with or without notice."}, "applicableLaw": {"title": "10. Applicable Law", "paragraph1": "These terms are governed by the laws of Brazil.", "paragraph2": "Any disputes will be resolved in the competent courts of São Paulo, SP."}}, "privacyPolicy": {"informationCollected": {"title": "1. Information We Collect", "paragraph1": "We collect the following information when you register and use our API:", "listItem1": "Registration information: name, email address, contact information.", "listItem2": "Usage information: API usage patterns, request volume, accessed endpoints.", "listItem3": "Payment information: billing and payment details.", "listItem4": "Technical information: IP address, browser type, operating system."}, "howWeUseInfo": {"title": "2. How We Use Your Information", "paragraph1": "We use your information to:", "listItem1": "Provide and maintain our services.", "listItem2": "Process transactions and send invoices.", "listItem3": "Monitor and analyze usage trends.", "listItem4": "Communicate with you about updates, support, and other service-related information.", "listItem5": "Detect, prevent, and resolve technical and security issues."}, "sharingInfo": {"title": "3. Information Sharing", "paragraph1": "We do not share your personal information with third parties except as described in this policy."}, "dataSecurity": {"title": "4. Data Security", "paragraph1": "We implement technical and organizational security measures to protect your information.", "paragraph2": "However, no method of transmission over the internet is 100% secure."}, "yourRights": {"title": "5. Your Rights", "paragraph1": "Depending on your location, you may have certain rights regarding your personal information, including:", "listItem1": "Right to access your information.", "listItem2": "Right to correct inaccurate information.", "listItem3": "Right to delete your information.", "listItem4": "Right to restrict or object to processing.", "listItem5": "Right to data portability. To exercise these rights, contact <NAME_EMAIL>."}, "dataRetention": {"title": "6. Data Retention", "paragraph1": "We retain your information for as long as necessary to provide our services.", "paragraph2": "API usage data is retained for up to 2 years for analysis and service improvement purposes."}, "cookiesAndSimilarTechnologies": {"title": "7. Cookies and Similar Technologies", "paragraph1": "We use cookies and similar technologies to improve your experience.", "paragraph2": "You can control cookie usage through your browser settings."}, "policyModifications": {"title": "8. Policy Modifications", "paragraph1": "We may update this policy periodically.", "paragraph2": "We will notify you of significant changes via email or through our platform."}, "contact": {"title": "9. <PERSON>", "paragraph1": "If you have questions about this Privacy Policy, contact us at:", "paragraph2": "Email: <EMAIL><br>Address: Av. <PERSON>, 1000, São Paulo, SP, Brazil"}}}}