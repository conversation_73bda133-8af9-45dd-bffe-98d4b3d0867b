<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="80" viewBox="0 0 200 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Gradiente para o círculo -->
    <defs>
        <linearGradient id="circleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
        </linearGradient>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="2" dy="2" stdDeviation="2" flood-opacity="0.3"/>
        </filter>
    </defs>

    <!-- Círculo de loading -->
    <circle cx="35" cy="35" r="25" fill="none" stroke="url(#circleGradient)" stroke-width="4">
        <animateTransform
            attributeName="transform"
            type="rotate"
            from="0 35 35"
            to="360 35 35"
            dur="1.5s"
            repeatCount="indefinite"/>
        <animate
            attributeName="stroke-opacity"
            values="1;0.3;1"
            dur="2s"
            repeatCount="indefinite"
            calcMode="spline"
            keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"/>
    </circle>
    
    <!-- Símbolo da moeda -->
    <circle cx="35" cy="35" r="20" fill="url(#circleGradient)" filter="url(#shadow)">
        <animate
            attributeName="r"
            values="20;22;20"
            dur="2s"
            repeatCount="indefinite"
            calcMode="spline"
            keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"/>
    </circle>
    <text x="35" y="43" font-family="'Segoe UI', Arial, sans-serif" font-size="24" font-weight="bold" fill="white" text-anchor="middle">
        <animate
            attributeName="opacity"
            values="1;0.7;1"
            dur="2s"
            repeatCount="indefinite"
            calcMode="spline"
            keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"/>
        $
    </text>
    
    <!-- Nome da aplicação -->
    <text x="75" y="35" font-family="'Segoe UI', Arial, sans-serif" font-size="24" font-weight="600" fill="#1e293b">
        Currency
    </text>
    <text x="175" y="55" font-family="'Segoe UI', Arial, sans-serif" font-size="24" font-weight="600" fill="#1e293b" text-anchor="end">
        wise
    </text>
</svg> 