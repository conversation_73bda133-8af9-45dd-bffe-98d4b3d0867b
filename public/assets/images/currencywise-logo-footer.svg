<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="30" viewBox="0 0 120 30" xmlns="http://www.w3.org/2000/svg">
    <!-- Gradiente para o círculo -->
    <defs>
        <linearGradient id="circleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
        </linearGradient>
    </defs>

    <!-- <PERSON><PERSON><PERSON><PERSON> da moeda -->
    <circle cx="15" cy="15" r="12" fill="url(#circleGradient)" />
    <text x="15" y="20" font-family="'Segoe UI', Arial, sans-serif" font-size="12" font-weight="bold" fill="white" text-anchor="middle">$</text>
    
    <!-- Nome da aplicação -->
    <text x="35" y="15" font-family="'Segoe UI', Arial, sans-serif" font-size="12" font-weight="600" fill="#1e293b">
        Currency
    </text>
    <text x="115" y="15" font-family="'Segoe UI', Arial, sans-serif" font-size="12" font-weight="600" fill="#1e293b" text-anchor="end">
        wise
    </text>
</svg> 