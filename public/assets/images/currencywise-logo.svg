<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="80" viewBox="0 0 200 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Gradiente para o círculo -->
    <defs>
        <linearGradient id="circleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
        </linearGradient>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="2" dy="2" stdDeviation="2" flood-opacity="0.3"/>
        </filter>
    </defs>

    <!-- <PERSON><PERSON><PERSON><PERSON> da moeda -->
    <circle cx="35" cy="35" r="25" fill="url(#circleGradient)" filter="url(#shadow)">
        <animate
            attributeName="r"
            values="25;27;25"
            dur="2s"
            repeatCount="indefinite"
            calcMode="spline"
            keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"/>
    </circle>
    <text x="35" y="43" font-family="'Segoe UI', Arial, sans-serif" font-size="24" font-weight="bold" fill="white" text-anchor="middle">
        <animate
            attributeName="opacity"
            values="1;0.8;1"
            dur="2s"
            repeatCount="indefinite"
            calcMode="spline"
            keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"/>
        $
    </text>
    
    <!-- Nome da aplicação -->
    <text x="75" y="35" font-family="'Segoe UI', Arial, sans-serif" font-size="24" font-weight="600" fill="white" filter="url(#shadow)">
        Currency
    </text>
    <text x="175" y="55" font-family="'Segoe UI', Arial, sans-serif" font-size="24" font-weight="600" fill="white" text-anchor="end" filter="url(#shadow)">
        wise
    </text>
</svg> 