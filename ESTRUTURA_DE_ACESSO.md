# Estrutura de Acesso às Páginas - CurrencyWise API

Este documento define quais páginas são públicas e quais são privadas (requerem autenticação) no frontend da CurrencyWise API.

## Categorias de Acesso

### 1. Páginas Públicas

Estas páginas são acessíveis sem necessidade de login:

- **Página Inicial (Home)**

  - Rota: `/`
  - Componente: `HomeComponent`
  - Descrição: Apresentação da API, recursos, benefícios, planos, depoimentos, FAQ e formulário de contato.

- **Autenticação**
  - Rota: `/auth/login`
  - Rota: `/auth/register`
  - Rota: `/auth/forgot-password`
  - Rota: `/auth/verify-email`
  - Rota: `/auth/terms`
  - Componentes: `LoginComponent`, `RegisterComponent`, `ForgotPasswordComponent`, `VerifyEmailComponent`, `TermsComponent`
  - Descrição: Páginas relacionadas ao processo de autenticação e registro.

### 2. Páginas Privadas (Requerem Autenticação)

Estas páginas são acessíveis apenas para usuários autenticados:

- **Dashboard do Usuário**

  - Rota: `/dashboard`
  - Componente: `DashboardComponent`
  - Descrição: Visão geral do uso da API pelo usuário.
  - Guard: `AuthGuard`

- **Gerenciamento de API Keys**

  - Rota: `/api-keys`
  - Componente: `ApiKeysComponent`
  - Descrição: Criação e gerenciamento de chaves de API.
  - Guard: `AuthGuard`

- **Gerenciamento de Assinaturas**

  - Rota: `/subscriptions`
  - Componente: `SubscriptionsComponent`
  - Descrição: Gerenciamento do plano de assinatura.
  - Guard: `AuthGuard`

- **Documentação Completa**

  - Rota: `/documentation`
  - Componente: `DocumentationComponent`
  - Descrição: Documentação detalhada da API.
  - Guard: `AuthGuard`
  - Observação: Futuramente, podemos ter uma versão pública com acesso limitado.

- **Playground Completo**
  - Rota: `/playground`
  - Componente: `PlaygroundComponent`
  - Descrição: Interface para testar a API em tempo real.
  - Guard: `AuthGuard`
  - Observação: Futuramente, podemos ter uma versão pública com acesso limitado.

### 3. Páginas Administrativas (Requerem Autenticação + Papel de Admin)

Estas páginas são acessíveis apenas para usuários com papel de administrador:

- **Dashboard Administrativo**

  - Rota: `/admin`
  - Componente: `AdminDashboardComponent`
  - Descrição: Métricas gerais do sistema.
  - Guard: `AdminGuard`

- **Gerenciamento de Usuários**

  - Rota: `/admin/users`
  - Componente: `UsersComponent`
  - Descrição: Gerenciamento de usuários do sistema.
  - Guard: `AdminGuard`

- **Gerenciamento de Planos**
  - Rota: `/admin/plans`
  - Componente: `PlansComponent`
  - Descrição: Configuração de planos e preços.
  - Guard: `AdminGuard`

## Implementação de Guards

### AuthGuard

Protege rotas que requerem autenticação:

```typescript
@Injectable({
  providedIn: "root",
})
export class AuthGuard implements CanActivate {
  constructor(private authService: AuthService, private router: Router) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    if (this.authService.isAuthenticated()) {
      return true;
    }

    this.router.navigate(["/auth/login"], {
      queryParams: { returnUrl: state.url },
    });
    return false;
  }
}
```

### AdminGuard

Protege rotas que requerem papel de administrador:

```typescript
@Injectable({
  providedIn: "root",
})
export class AdminGuard implements CanActivate {
  constructor(private authService: AuthService, private router: Router) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    // Verifica se o usuário está autenticado e tem papel de admin
    if (this.authService.isAuthenticated() && this.authService.isAdmin()) {
      return true;
    }

    // Se o usuário está autenticado mas não é admin, redireciona para o dashboard
    if (this.authService.isAuthenticated()) {
      this.router.navigate(["/dashboard"]);
      return false;
    }

    // Se o usuário não está autenticado, redireciona para o login
    this.router.navigate(["/auth/login"], {
      queryParams: { returnUrl: state.url },
    });
    return false;
  }
}
```

## Considerações Futuras

1. **Documentação Pública Limitada**:

   - Implementar uma versão pública da documentação com acesso limitado
   - Usar um guard personalizado para verificar o nível de acesso

2. **Playground Público Limitado**:

   - Implementar uma versão pública do playground com limite de requisições
   - Usar um serviço para controlar o número de requisições por IP

3. **Níveis de Acesso Baseados em Planos**:
   - Implementar diferentes níveis de acesso baseados no plano do usuário
   - Usar guards personalizados para verificar o plano do usuário
