{"name": "currency-frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.12", "@angular/cdk": "^19.2.17", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "@swimlane/ngx-charts": "^22.0.0", "@tailwindcss/postcss": "^4.1.7", "apexcharts": "^4.7.0", "d3-shape": "^3.2.0", "lucide-angular": "^0.511.0", "marked": "^15.0.12", "ng-apexcharts": "^1.15.0", "postcss": "^8.5.3", "rxjs": "~7.8.0", "sweetalert2": "^11.21.2", "tailwindcss": "^4.1.7", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.4", "@angular/cli": "^19.2.4", "@angular/compiler-cli": "^19.2.0", "@types/d3-shape": "^3.1.7", "@types/jasmine": "~5.1.0", "@types/marked": "^5.0.2", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.2"}}