# Análise e Correção de Links na Aplicação CurrencyWise API

Este documento contém uma análise detalhada de todos os links presentes na aplicação CurrencyWise API e recomendações para direcioná-los aos componentes corretos.

## 1. Links no Header

### Estado Atual

O componente `HeaderComponent` (`src/app/shared/components/header/header.component.ts`) contém os seguintes links:

```typescript
navItems: NavItem[] = [
  { path: '/', label: 'Início', icon: HomeIcon },
  { path: '/dashboard', label: 'Dashboard', icon: ChartBar },
  { path: '/api-keys', label: 'API Keys', icon: KeyIcon },
  { path: '/subscriptions', label: 'Assinaturas', icon: CreditCardIcon },
  { path: '/documentation', label: 'Documentação', icon: FileTextIcon },
  { path: '/playground', label: 'Playground', icon: TerminalIcon },
];
```

Além disso, há links para autenticação:

```html
<a routerLink="/auth/login" class="nav-link">
  <i-lucide [img]="LogInIcon" class="nav-icon"></i-lucide>
  Entrar
</a>
<a routerLink="/auth/register" class="btn-primary">
  <i-lucide [img]="UserPlusIcon" class="nav-icon"></i-lucide>
  Registrar
</a>
```

### Análise

Todos os links no header estão corretamente direcionados para as rotas definidas em:
- `src/app/layout/main-layout/main-layout.routes.ts`
- `src/app/layout/auth-layout/auth-layout.routes.ts`

**Status**: ✅ Correto

## 2. Links no Footer

### Estado Atual

O componente `FooterComponent` (`src/app/shared/components/footer/footer.component.ts`) contém os seguintes links:

```typescript
footerSections: FooterSection[] = [
  {
    title: 'Links Rápidos',
    links: [
      { label: 'Documentação', url: '/documentation', isRouterLink: true },
      { label: 'Preços', url: '/subscriptions', isRouterLink: true },
      { label: 'API Status', url: '#', isRouterLink: false },
    ],
  },
  {
    title: 'Recursos',
    links: [
      { label: 'Blog', url: '#', isRouterLink: false },
      { label: 'Tutoriais', url: '#', isRouterLink: false },
      { label: 'FAQ', url: '#', isRouterLink: false },
    ],
  },
  {
    title: 'Contato',
    links: [
      { label: 'Suporte', url: '#', isRouterLink: false },
      { label: 'Vendas', url: '#', isRouterLink: false },
      { label: 'Sobre Nós', url: '#', isRouterLink: false },
    ],
  },
];
```

### Análise e Recomendações

1. **Links Rápidos**:
   - ✅ Documentação: Corretamente direcionado para `/documentation`
   - ✅ Preços: Corretamente direcionado para `/subscriptions`
   - ❌ API Status: Atualmente aponta para `#`. Deve ser atualizado para `/status`

2. **Recursos**:
   - ❌ Blog: Atualmente aponta para `#`. Deve ser atualizado para `/blog`
   - ❌ Tutoriais: Atualmente aponta para `#`. Deve ser atualizado para `/tutorials`
   - ❌ FAQ: Atualmente aponta para `#`. Deve ser atualizado para `/faq`

3. **Contato**:
   - ❌ Suporte: Atualmente aponta para `#`. Deve ser atualizado para `/support`
   - ❌ Vendas: Atualmente aponta para `#`. Deve ser atualizado para `/sales`
   - ❌ Sobre Nós: Atualmente aponta para `#`. Deve ser atualizado para `/about`

### Correção Recomendada

```typescript
footerSections: FooterSection[] = [
  {
    title: 'Links Rápidos',
    links: [
      { label: 'Documentação', url: '/documentation', isRouterLink: true },
      { label: 'Preços', url: '/subscriptions', isRouterLink: true },
      { label: 'API Status', url: '/status', isRouterLink: true },
    ],
  },
  {
    title: 'Recursos',
    links: [
      { label: 'Blog', url: '/blog', isRouterLink: true },
      { label: 'Tutoriais', url: '/tutorials', isRouterLink: true },
      { label: 'FAQ', url: '/faq', isRouterLink: true },
    ],
  },
  {
    title: 'Contato',
    links: [
      { label: 'Suporte', url: '/support', isRouterLink: true },
      { label: 'Vendas', url: '/sales', isRouterLink: true },
      { label: 'Sobre Nós', url: '/about', isRouterLink: true },
    ],
  },
];
```

## 3. Links na Página Inicial (Home)

### Estado Atual

A página inicial (`src/app/features/home/<USER>/home.component.html`) contém botões de ação que não estão direcionados a rotas específicas:

```html
<button class="btn-primary w-full">Começar Grátis</button>
<button class="btn-primary w-full">Assinar Agora</button>
<button class="btn-primary w-full">Contate Vendas</button>
```

### Recomendações

Estes botões devem ser atualizados para direcionar para as páginas apropriadas:

```html
<a routerLink="/auth/register" class="btn-primary w-full">Começar Grátis</a>
<a routerLink="/subscriptions" class="btn-primary w-full">Assinar Agora</a>
<a routerLink="/sales" class="btn-primary w-full">Contate Vendas</a>
```

## 4. Links na Documentação

### Estado Atual

A página de documentação (`src/app/features/documentation/components/documentation.component.html`) contém links para o playground e para a seção de API Keys:

```html
<a href="/playground" class="btn-primary">
  <i-lucide [img]="CodeIcon" class="icon-sm"></i-lucide>
  Testar no Playground
</a>

<a href="/api-keys" class="text-blue-600 hover:text-blue-800">API Keys</a>
```

### Análise

Estes links usam `href` em vez de `routerLink`, o que causa um recarregamento completo da página.

### Recomendações

Atualizar para usar `routerLink`:

```html
<a routerLink="/playground" class="btn-primary">
  <i-lucide [img]="CodeIcon" class="icon-sm"></i-lucide>
  Testar no Playground
</a>

<a routerLink="/api-keys" class="text-blue-600 hover:text-blue-800">API Keys</a>
```

## 5. Links no Playground

### Estado Atual

A página do playground (`src/app/features/playground/components/playground.component.html`) contém links para a documentação e API Keys:

```html
<a href="/documentation" class="btn-primary">
  <i-lucide [img]="CodeIcon" class="icon-sm"></i-lucide>
  Ver Documentação
</a>

<a href="/api-keys" class="text-blue-600 hover:text-blue-800">API Keys</a>

<a href="/documentation" class="text-blue-800 underline">documentação completa</a>
<a href="/api-keys" class="text-blue-800 underline">API Keys</a>
```

### Recomendações

Atualizar para usar `routerLink`:

```html
<a routerLink="/documentation" class="btn-primary">
  <i-lucide [img]="CodeIcon" class="icon-sm"></i-lucide>
  Ver Documentação
</a>

<a routerLink="/api-keys" class="text-blue-600 hover:text-blue-800">API Keys</a>

<a routerLink="/documentation" class="text-blue-800 underline">documentação completa</a>
<a routerLink="/api-keys" class="text-blue-800 underline">API Keys</a>
```

## Resumo das Alterações Necessárias

1. **Footer**: Atualizar todos os links que atualmente apontam para `#` para direcionar para as rotas corretas.

2. **Página Inicial**: Converter botões em links com `routerLink` para as páginas apropriadas.

3. **Documentação e Playground**: Substituir `href` por `routerLink` para evitar recarregamento completo da página.

## Implementação das Rotas Faltantes

Para que os links funcionem corretamente, é necessário implementar as seguintes rotas no arquivo `src/app/layout/main-layout/main-layout.routes.ts`:

```typescript
{
  path: 'status',
  loadComponent: () => import('../../features/status').then(m => m.StatusComponent),
  title: 'API Status - CurrencyWise'
},
{
  path: 'blog',
  loadComponent: () => import('../../features/blog').then(m => m.BlogComponent),
  title: 'Blog - CurrencyWise'
},
{
  path: 'tutorials',
  loadComponent: () => import('../../features/tutorials').then(m => m.TutorialsComponent),
  title: 'Tutoriais - CurrencyWise'
},
{
  path: 'faq',
  loadComponent: () => import('../../features/faq').then(m => m.FaqComponent),
  title: 'FAQ - CurrencyWise'
},
{
  path: 'support',
  loadComponent: () => import('../../features/support').then(m => m.SupportComponent),
  title: 'Suporte - CurrencyWise'
},
{
  path: 'sales',
  loadComponent: () => import('../../features/sales').then(m => m.SalesComponent),
  title: 'Vendas - CurrencyWise'
},
{
  path: 'about',
  loadComponent: () => import('../../features/about').then(m => m.AboutComponent),
  title: 'Sobre Nós - CurrencyWise'
}
```

Estas rotas devem ser implementadas após a criação dos componentes correspondentes, conforme especificado no documento `IMPLEMENTACAO_LINKS_FOOTER.md`.
