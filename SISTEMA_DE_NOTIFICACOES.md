# Sistema de Notificações com SweetAlert2

Este documento descreve o sistema de notificações implementado no projeto CurrencyWise API, que utiliza a biblioteca SweetAlert2 para exibir notificações e diálogos de confirmação.

## Visão Geral

O sistema de notificações foi aprimorado para utilizar a biblioteca SweetAlert2, que oferece uma experiência de usuário mais rica e interativa. O serviço mantém compatibilidade com o sistema anterior, permitindo uma migração gradual.

## Funcionalidades

O sistema de notificações oferece as seguintes funcionalidades:

1. **Toasts** - Notificações temporárias que aparecem no canto da tela
2. **Diálogos de Confirmação** - Janelas modais para confirmar ações importantes
3. **Compatibilidade** - Mantém compatibilidade com o componente de notificação existente

## Como Usar

### Importação

Para usar o serviço de notificação, importe-o no seu componente:

```typescript
import { NotificationService } from "../../../core/services/notification.service";

@Component({
  // ...
})
export class SeuComponente {
  constructor(private notificationService: NotificationService) {}

  // ...
}
```

### Toasts

#### Notificação de Sucesso

```typescript
// Básico
this.notificationService.success("Operação realizada com sucesso!");

// Com título personalizado
this.notificationService.success("Operação realizada com sucesso!", "Tudo certo!");

// Com duração personalizada (em milissegundos)
this.notificationService.success("Operação realizada com sucesso!", "Tudo certo!", 3000);
```

#### Notificação de Erro

```typescript
// Básico
this.notificationService.error("Ocorreu um erro ao processar sua solicitação.");

// Com título personalizado
this.notificationService.error("Ocorreu um erro ao processar sua solicitação.", "Ops!");

// Com duração personalizada (em milissegundos)
this.notificationService.error("Ocorreu um erro ao processar sua solicitação.", "Ops!", 10000);
```

#### Notificação Informativa

```typescript
this.notificationService.info("Esta é uma mensagem informativa.");
```

#### Notificação de Aviso

```typescript
this.notificationService.warning("Atenção! Esta ação pode ter consequências.");
```

### Diálogos de Confirmação

#### Confirmação Simples

```typescript
this.notificationService
  .confirm({
    title: "Confirmação",
    text: "Tem certeza que deseja realizar esta ação?",
    icon: "question",
  })
  .then((result) => {
    if (result.isConfirmed) {
      // Usuário clicou em "Sim"
      this.notificationService.success("Ação confirmada!");
    } else if (result.isDismissed) {
      // Usuário clicou em "Cancelar" ou fechou o diálogo
      this.notificationService.info("Ação cancelada.");
    }
  });
```

#### Confirmação de Ação Perigosa

```typescript
this.notificationService
  .confirm({
    title: "Atenção!",
    text: "Esta ação não pode ser desfeita. Deseja continuar?",
    icon: "warning",
    confirmButtonText: "Sim, continuar",
    cancelButtonText: "Não, cancelar",
    focusCancel: true, // Foca no botão de cancelar por padrão
  })
  .then((result) => {
    if (result.isConfirmed) {
      // Usuário confirmou a ação perigosa
      this.notificationService.success("Ação perigosa realizada com sucesso!");
    }
  });
```

## Opções de Configuração

### Opções para Toasts

| Opção               | Tipo    | Descrição                                                                                                                        | Valor Padrão    |
| ------------------- | ------- | -------------------------------------------------------------------------------------------------------------------------------- | --------------- |
| `type`              | string  | Tipo da notificação ('success', 'error', 'info', 'warning')                                                                      | -               |
| `message`           | string  | Mensagem a ser exibida                                                                                                           | -               |
| `title`             | string  | Título da notificação                                                                                                            | Depende do tipo |
| `duration`          | number  | Duração em milissegundos                                                                                                         | 5000            |
| `position`          | string  | Posição do toast ('top', 'top-start', 'top-end', 'center', 'center-start', 'center-end', 'bottom', 'bottom-start', 'bottom-end') | 'top-end'       |
| `showConfirmButton` | boolean | Exibir botão de confirmação                                                                                                      | false           |
| `html`              | string  | Conteúdo HTML para a notificação                                                                                                 | -               |

### Opções para Diálogos de Confirmação

| Opção               | Tipo    | Descrição                                                            | Valor Padrão        |
| ------------------- | ------- | -------------------------------------------------------------------- | ------------------- |
| `title`             | string  | Título do diálogo                                                    | 'Você tem certeza?' |
| `text`              | string  | Texto do diálogo                                                     | -                   |
| `icon`              | string  | Ícone do diálogo ('success', 'error', 'warning', 'info', 'question') | 'question'          |
| `confirmButtonText` | string  | Texto do botão de confirmação                                        | 'Sim'               |
| `cancelButtonText`  | string  | Texto do botão de cancelamento                                       | 'Cancelar'          |
| `showCancelButton`  | boolean | Exibir botão de cancelamento                                         | true                |
| `focusCancel`       | boolean | Focar no botão de cancelamento                                       | false               |

## Página de Demonstração

Uma página de demonstração foi criada para mostrar as diferentes notificações disponíveis. Você pode acessá-la em:

```plaintext
/notification-demo
```

## Compatibilidade com o Sistema Anterior

O serviço mantém compatibilidade com o componente de notificação existente através do `BehaviorSubject`. Isso permite uma migração gradual para o novo sistema.

```typescript
// O componente existente continua funcionando
this.notificationSubject.next(notification);

// Mas agora também usamos o SweetAlert2
const toast = Swal.mixin({
  // ...
});

toast.fire({
  // ...
});
```

## Considerações de Uso

1. **Toasts vs. Diálogos**: Use toasts para notificações informativas que não interrompem o fluxo do usuário. Use diálogos de confirmação para ações importantes que requerem confirmação explícita.

2. **Duração**: Ajuste a duração das notificações de acordo com a importância e quantidade de texto. Notificações de erro geralmente devem ficar visíveis por mais tempo.

3. **Posição**: A posição padrão dos toasts é 'top-end' (canto superior direito), mas você pode alterá-la conforme necessário.

4. **HTML**: Você pode usar HTML nas notificações para formatação mais rica, mas use com moderação e cuidado com a segurança.

## Referências

- [Documentação do SweetAlert2](https://sweetalert2.github.io/)
- [Exemplos do SweetAlert2](https://sweetalert2.github.io/#examples)
- [GitHub do SweetAlert2](https://github.com/sweetalert2/sweetalert2)
