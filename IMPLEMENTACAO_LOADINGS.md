# Implementação de Loadings na Aplicação CurrencyWise API

Este documento contém recomendações para implementação de indicadores de carregamento (loadings) nas páginas e componentes que dependem de aguardar retorno da API.

## Visão Geral

A implementação de indicadores de carregamento é essencial para melhorar a experiência do usuário, fornecendo feedback visual durante operações assíncronas. Como a aplicação ainda não está consumindo a API real, este documento serve como um guia para a futura implementação.

## Estratégias de Loading

Recomendamos três estratégias principais para implementação de loadings:

1. **Loading de Página Completa**: Para operações iniciais de carregamento de página
2. **Loading de Componente**: Para carregamento de seções específicas
3. **Loading de Ação**: Para ações específicas iniciadas pelo usuário

## Componentes a Serem Implementados

### 1. Componente de Loading Global

```typescript
// src/app/shared/components/loading/loading.component.ts
@Component({
  selector: 'app-loading',
  standalone: true,
  template: `
    <div class="fixed inset-0 flex items-center justify-center bg-white bg-opacity-75 z-50">
      <div class="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-primary"></div>
    </div>
  `
})
export class LoadingComponent {}
```

### 2. Componente de Loading de Seção

```typescript
// src/app/shared/components/section-loading/section-loading.component.ts
@Component({
  selector: 'app-section-loading',
  standalone: true,
  template: `
    <div class="flex items-center justify-center p-8">
      <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
    </div>
  `
})
export class SectionLoadingComponent {}
```

### 3. Componente de Loading de Botão

```typescript
// src/app/shared/components/button-loading/button-loading.component.ts
@Component({
  selector: 'app-button-loading',
  standalone: true,
  inputs: ['loading', 'text', 'loadingText'],
  template: `
    <button 
      class="btn-primary" 
      [disabled]="loading"
    >
      @if (loading) {
        <span class="inline-block animate-spin mr-2">
          <i-lucide [img]="RefreshCwIcon" class="icon-sm"></i-lucide>
        </span>
        {{ loadingText || 'Carregando...' }}
      } @else {
        {{ text }}
      }
    </button>
  `
})
export class ButtonLoadingComponent {
  loading = false;
  text = 'Enviar';
  loadingText = 'Carregando...';
  readonly RefreshCwIcon = RefreshCwIcon;
}
```

## Locais Recomendados para Implementação

### 1. Autenticação

#### 1.1. Login

**Arquivo**: `src/app/features/auth/login/components/login.component.ts`

**Implementação Recomendada**:
- Loading de botão durante o envio do formulário
- Variável de estado `isLoading` para controlar o estado

```typescript
// Adicionar ao componente
isLoading = false;

// Modificar o método de login
login() {
  this.isLoading = true;
  this.authService.login(this.loginForm.value).pipe(
    finalize(() => this.isLoading = false)
  ).subscribe({
    next: (response) => {
      // Lógica de sucesso
    },
    error: (error) => {
      // Tratamento de erro
    }
  });
}
```

**HTML**:
```html
<button 
  type="submit" 
  class="btn-primary w-full" 
  [disabled]="loginForm.invalid || isLoading">
  @if (isLoading) {
    <i-lucide [img]="RefreshCwIcon" class="icon-sm animate-spin mr-2"></i-lucide>
    Entrando...
  } @else {
    Entrar
  }
</button>
```

#### 1.2. Registro e Recuperação de Senha

Aplicar a mesma lógica de loading nos componentes:
- `src/app/features/auth/register/components/register.component.ts`
- `src/app/features/auth/forgot-password/components/forgot-password.component.ts`

### 2. Dashboard

**Arquivo**: `src/app/features/dashboard/components/dashboard.component.ts`

**Implementação Recomendada**:
- Loading de página completa durante o carregamento inicial
- Loading de seção para atualizações parciais

```typescript
// Adicionar ao componente
isLoading = true;
isChartLoading = false;

// No ngOnInit
ngOnInit() {
  this.loadDashboardData();
}

loadDashboardData() {
  this.isLoading = true;
  forkJoin({
    summary: this.dashboardService.getSummary(),
    usage: this.dashboardService.getUsage(),
    recentActivity: this.dashboardService.getRecentActivity()
  }).pipe(
    finalize(() => this.isLoading = false)
  ).subscribe({
    next: (data) => {
      // Processar dados
    },
    error: (error) => {
      // Tratamento de erro
    }
  });
}

refreshUsageChart() {
  this.isChartLoading = true;
  this.dashboardService.getUsage().pipe(
    finalize(() => this.isChartLoading = false)
  ).subscribe({
    next: (data) => {
      // Atualizar gráfico
    },
    error: (error) => {
      // Tratamento de erro
    }
  });
}
```

**HTML**:
```html
@if (isLoading) {
  <app-loading></app-loading>
} @else {
  <!-- Conteúdo do dashboard -->
  
  <!-- Para seção específica -->
  <div class="card">
    <div class="card-header flex-between">
      <h2>Uso da API</h2>
      <button (click)="refreshUsageChart()" [disabled]="isChartLoading">
        <i-lucide 
          [img]="RefreshCwIcon" 
          class="icon-sm" 
          [class.animate-spin]="isChartLoading"
        ></i-lucide>
      </button>
    </div>
    <div class="card-body">
      @if (isChartLoading) {
        <app-section-loading></app-section-loading>
      } @else {
        <!-- Conteúdo do gráfico -->
      }
    </div>
  </div>
}
```

### 3. API Keys

**Arquivo**: `src/app/features/api-keys/components/api-keys.component.ts`

**Implementação Recomendada**:
- Loading de página para carregamento inicial
- Loading de ação para operações específicas (criar, revogar, regenerar)

```typescript
// Adicionar ao componente
isLoading = true;
isCreating = false;
isRevoking = false;
isRegenerating = false;

// No ngOnInit
ngOnInit() {
  this.loadApiKeys();
}

loadApiKeys() {
  this.isLoading = true;
  this.apiKeyService.getApiKeys().pipe(
    finalize(() => this.isLoading = false)
  ).subscribe({
    next: (keys) => {
      this.apiKeys = keys;
    },
    error: (error) => {
      // Tratamento de erro
    }
  });
}

createApiKey() {
  this.isCreating = true;
  this.apiKeyService.createApiKey(this.newKeyForm.value).pipe(
    finalize(() => this.isCreating = false)
  ).subscribe({
    next: (key) => {
      this.apiKeys.push(key);
      this.notificationService.success('Chave API criada com sucesso');
    },
    error: (error) => {
      // Tratamento de erro
    }
  });
}
```

### 4. Playground

**Arquivo**: `src/app/features/playground/components/playground.component.ts`

**Implementação Recomendada**:
- Já possui implementação de loading (`isLoading`) durante a execução de requisições
- Manter a implementação atual e adaptar para API real quando disponível

### 5. Gerenciamento de Assinaturas

**Arquivo**: `src/app/features/subscriptions/components/subscriptions.component.ts`

**Implementação Recomendada**:
- Loading de página para carregamento inicial
- Loading de ação para operações de pagamento e alteração de plano

```typescript
// Adicionar ao componente
isLoading = true;
isUpgrading = false;
isCanceling = false;

// No ngOnInit
ngOnInit() {
  this.loadSubscriptionData();
}

loadSubscriptionData() {
  this.isLoading = true;
  forkJoin({
    subscription: this.subscriptionService.getCurrentSubscription(),
    invoices: this.subscriptionService.getInvoices(),
    plans: this.subscriptionService.getAvailablePlans()
  }).pipe(
    finalize(() => this.isLoading = false)
  ).subscribe({
    next: (data) => {
      this.currentSubscription = data.subscription;
      this.invoices = data.invoices;
      this.availablePlans = data.plans;
    },
    error: (error) => {
      // Tratamento de erro
    }
  });
}

upgradePlan(planId: string) {
  this.isUpgrading = true;
  this.subscriptionService.upgradePlan(planId).pipe(
    finalize(() => this.isUpgrading = false)
  ).subscribe({
    next: (subscription) => {
      this.currentSubscription = subscription;
      this.notificationService.success('Plano atualizado com sucesso');
    },
    error: (error) => {
      // Tratamento de erro
    }
  });
}
```

### 6. Painel Administrativo

#### 6.1. Gerenciamento de Usuários

**Arquivo**: `src/app/features/admin/components/users/users.component.ts`

**Implementação Recomendada**:
- Loading de página para carregamento inicial
- Loading de tabela para operações de filtragem e paginação

```typescript
// Adicionar ao componente
isLoading = true;
isFiltering = false;

// No ngOnInit
ngOnInit() {
  this.loadUsers();
}

loadUsers() {
  this.isLoading = true;
  this.adminService.getUsers().pipe(
    finalize(() => this.isLoading = false)
  ).subscribe({
    next: (users) => {
      this.users = users;
      this.filteredUsers = [...this.users];
    },
    error: (error) => {
      // Tratamento de erro
    }
  });
}

filterUsers() {
  this.isFiltering = true;
  
  // Se a filtragem for local (como está atualmente)
  if (!this.searchTerm) {
    this.filteredUsers = [...this.users];
    this.isFiltering = false;
    return;
  }

  const term = this.searchTerm.toLowerCase();
  this.filteredUsers = this.users.filter(
    (user) =>
      user.name.toLowerCase().includes(term) ||
      user.email.toLowerCase().includes(term) ||
      user.phone.includes(term)
  );
  this.isFiltering = false;
  
  // Se a filtragem for via API
  /*
  this.adminService.searchUsers(this.searchTerm).pipe(
    finalize(() => this.isFiltering = false)
  ).subscribe({
    next: (users) => {
      this.filteredUsers = users;
    },
    error: (error) => {
      // Tratamento de erro
    }
  });
  */
}
```

## Serviço de Loading Global

Para facilitar o gerenciamento de estados de loading, recomendamos a criação de um serviço de loading global:

```typescript
// src/app/core/services/loading.service.ts
@Injectable({
  providedIn: 'root'
})
export class LoadingService {
  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  show() {
    this.loadingSubject.next(true);
  }

  hide() {
    this.loadingSubject.next(false);
  }
}
```

## Interceptor HTTP para Loading Automático

Para automatizar o loading em todas as requisições HTTP:

```typescript
// src/app/core/interceptors/loading.interceptor.ts
@Injectable()
export class LoadingInterceptor implements HttpInterceptor {
  private activeRequests = 0;

  constructor(private loadingService: LoadingService) {}

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (this.activeRequests === 0) {
      this.loadingService.show();
    }
    
    this.activeRequests++;
    
    return next.handle(request).pipe(
      finalize(() => {
        this.activeRequests--;
        if (this.activeRequests === 0) {
          this.loadingService.hide();
        }
      })
    );
  }
}
```

## Considerações Finais

1. **Tempos de Timeout**: Defina tempos de timeout adequados para requisições para evitar loadings infinitos.

2. **Feedback de Erro**: Sempre forneça feedback visual quando uma operação falhar.

3. **Skeleton Screens**: Considere usar skeleton screens para melhorar a percepção de velocidade.

4. **Throttling e Debouncing**: Implemente throttling e debouncing para operações frequentes como filtragem e busca.

5. **Caching**: Implemente estratégias de cache para reduzir a necessidade de loadings repetidos.

6. **Testes**: Teste os loadings em diferentes condições de rede para garantir uma boa experiência do usuário.
